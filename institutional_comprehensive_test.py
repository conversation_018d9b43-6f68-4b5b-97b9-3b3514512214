#!/usr/bin/env python3
"""
🏛️ 机构级别时间戳统一性综合测试
按照修复质量保证.md的严格标准进行三段进阶验证
"""

import sys
import os
import time
import asyncio
import traceback
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')
sys.path.insert(0, os.path.join(os.getcwd(), '.'))

class InstitutionalComprehensiveTest:
    """机构级别综合测试器"""
    
    def __init__(self):
        self.test_results = {
            'basic_core_tests': {},
            'complex_system_tests': {},
            'production_simulation_tests': {},
            'overall_score': 0.0,
            'critical_failures': []
        }
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有三段进阶测试"""
        print("🏛️ 开始机构级别时间戳统一性综合测试")
        print("=" * 80)
        
        try:
            # ① 基础核心测试
            print("\n📋 第一段：基础核心测试")
            print("-" * 50)
            basic_results = await self._run_basic_core_tests()
            self.test_results['basic_core_tests'] = basic_results
            
            # ② 复杂系统级联测试
            print("\n📋 第二段：复杂系统级联测试")
            print("-" * 50)
            complex_results = await self._run_complex_system_tests()
            self.test_results['complex_system_tests'] = complex_results
            
            # ③ 生产模拟测试
            print("\n📋 第三段：生产模拟测试")
            print("-" * 50)
            production_results = await self._run_production_simulation_tests()
            self.test_results['production_simulation_tests'] = production_results
            
            # 计算总分
            self._calculate_overall_score()
            
            return self.test_results
            
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            traceback.print_exc()
            self.test_results['critical_failures'].append(f"测试执行异常: {e}")
            return self.test_results
    
    async def _run_basic_core_tests(self) -> Dict[str, Any]:
        """① 基础核心测试：模块单元功能验证"""
        results = {}
        
        # 测试1：统一时间戳处理函数
        print("🔍 测试1：统一时间戳处理函数")
        try:
            from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp, calculate_data_age
            
            # 边界检查
            test_cases = [
                (time.time(), "当前秒级时间戳"),
                (int(time.time() * 1000), "当前毫秒级时间戳"),
                (1754028976.668, "历史秒级时间戳"),
                (1754028976668, "历史毫秒级时间戳"),
                (0, "零值"),
                (None, "None值"),
            ]
            
            passed = 0
            total = len(test_cases) - 1  # 排除None值测试
            
            for timestamp, desc in test_cases:
                try:
                    if timestamp is None:
                        # None值应该返回当前时间戳
                        result = ensure_milliseconds_timestamp(timestamp)
                        if result > 1e12:  # 确保返回有效的毫秒级时间戳
                            print(f"  ✅ {desc}: None → {result} (当前时间戳)")
                            passed += 1
                        else:
                            print(f"  ❌ {desc}: None值处理失败")

                    else:
                        result = ensure_milliseconds_timestamp(timestamp)
                        if result > 1e12:  # 确保是毫秒级
                            print(f"  ✅ {desc}: {timestamp} → {result}")
                            passed += 1
                        else:
                            print(f"  ❌ {desc}: 结果不是毫秒级 {result}")
                except Exception as e:
                    print(f"  ❌ {desc}: 异常 {e}")
            
            results['unified_functions'] = {
                'passed': passed,
                'total': total + 1,  # 包括None值测试
                'score': (passed / (total + 1)) * 100
            }
            
        except Exception as e:
            print(f"  ❌ 统一函数测试失败: {e}")
            results['unified_functions'] = {'passed': 0, 'total': 1, 'score': 0}
        
        # 测试2：MarketData时间戳类型
        print("\n🔍 测试2：MarketData时间戳类型")
        try:
            from core.opportunity_scanner import MarketData
            
            current_ms = int(time.time() * 1000)
            market_data = MarketData(
                exchange="gate",
                symbol="BTC-USDT",
                price=50000.0,
                timestamp=current_ms,
                orderbook={}
            )
            
            type_correct = isinstance(market_data.timestamp, int)
            format_correct = market_data.timestamp > 1e12
            
            if type_correct and format_correct:
                print(f"  ✅ MarketData.timestamp: 类型={type(market_data.timestamp)}, 格式=毫秒级")
                results['market_data_type'] = {'passed': 1, 'total': 1, 'score': 100}
            else:
                print(f"  ❌ MarketData.timestamp: 类型={type(market_data.timestamp)}, 值={market_data.timestamp}")
                results['market_data_type'] = {'passed': 0, 'total': 1, 'score': 0}
                
        except Exception as e:
            print(f"  ❌ MarketData测试失败: {e}")
            results['market_data_type'] = {'passed': 0, 'total': 1, 'score': 0}
        
        # 测试3：关键修复点验证
        print("\n🔍 测试3：关键修复点验证")
        try:
            # 验证OpportunityScanner修复
            from websocket.unified_timestamp_processor import calculate_data_age
            
            current_time = time.time()
            data_timestamp = int(time.time() * 1000)
            
            # 使用修复后的计算方式
            correct_age = calculate_data_age(data_timestamp, current_time)
            
            # 模拟修复前的错误计算
            wrong_age = current_time - data_timestamp
            
            if abs(correct_age) < 1 and wrong_age < -1e9:  # 正确年龄接近0，错误年龄是巨大负数
                print(f"  ✅ 时间戳单位修复验证: 正确年龄={correct_age:.3f}s, 错误年龄={wrong_age:.0f}")
                results['fix_verification'] = {'passed': 1, 'total': 1, 'score': 100}
            else:
                print(f"  ❌ 时间戳单位修复验证失败")
                results['fix_verification'] = {'passed': 0, 'total': 1, 'score': 0}
                
        except Exception as e:
            print(f"  ❌ 修复点验证失败: {e}")
            results['fix_verification'] = {'passed': 0, 'total': 1, 'score': 0}
        
        return results
    
    async def _run_complex_system_tests(self) -> Dict[str, Any]:
        """② 复杂系统级联测试：模块交互逻辑验证"""
        results = {}
        
        # 测试1：跨模块时间戳一致性
        print("🔍 测试1：跨模块时间戳一致性")
        try:
            from websocket.unified_timestamp_processor import get_synced_timestamp
            from core.opportunity_scanner import MarketData
            
            # 获取统一时间戳
            unified_timestamp = get_synced_timestamp("gate", None)
            
            # 创建MarketData
            market_data = MarketData(
                exchange="gate",
                symbol="BTC-USDT",
                price=50000.0,
                timestamp=unified_timestamp,
                orderbook={}
            )
            
            # 验证时间戳一致性
            timestamp_diff = abs(unified_timestamp - market_data.timestamp)
            
            if timestamp_diff == 0:
                print(f"  ✅ 跨模块时间戳完全一致")
                results['cross_module_consistency'] = {'passed': 1, 'total': 1, 'score': 100}
            else:
                print(f"  ❌ 跨模块时间戳不一致: 差异={timestamp_diff}")
                results['cross_module_consistency'] = {'passed': 0, 'total': 1, 'score': 0}
                
        except Exception as e:
            print(f"  ❌ 跨模块一致性测试失败: {e}")
            results['cross_module_consistency'] = {'passed': 0, 'total': 1, 'score': 0}
        
        # 测试2：多交易所时间戳同步
        print("\n🔍 测试2：多交易所时间戳同步")
        try:
            exchanges = ["gate", "bybit", "okx"]
            timestamps = {}
            
            for exchange in exchanges:
                timestamps[exchange] = get_synced_timestamp(exchange, None)
            
            # 检查时间戳差异
            max_diff = 0
            for i, ex1 in enumerate(exchanges):
                for ex2 in exchanges[i+1:]:
                    diff = abs(timestamps[ex1] - timestamps[ex2])
                    max_diff = max(max_diff, diff)
            
            # 允许最大10ms差异
            if max_diff <= 10:
                print(f"  ✅ 多交易所时间戳同步: 最大差异={max_diff}ms")
                results['multi_exchange_sync'] = {'passed': 1, 'total': 1, 'score': 100}
            else:
                print(f"  ❌ 多交易所时间戳差异过大: {max_diff}ms")
                results['multi_exchange_sync'] = {'passed': 0, 'total': 1, 'score': 0}
                
        except Exception as e:
            print(f"  ❌ 多交易所同步测试失败: {e}")
            results['multi_exchange_sync'] = {'passed': 0, 'total': 1, 'score': 0}
        
        return results
    
    async def _run_production_simulation_tests(self) -> Dict[str, Any]:
        """③ 生产模拟测试：真实场景模拟"""
        results = {}
        
        # 测试1：高频数据处理模拟
        print("🔍 测试1：高频数据处理模拟")
        try:
            from websocket.unified_timestamp_processor import calculate_data_age, ensure_milliseconds_timestamp
            
            # 模拟高频数据流
            data_count = 1000
            processing_times = []
            accuracy_count = 0
            
            start_time = time.time()
            
            for i in range(data_count):
                # 模拟不同格式的时间戳
                if i % 3 == 0:
                    timestamp = time.time()  # 秒级
                elif i % 3 == 1:
                    timestamp = int(time.time() * 1000)  # 毫秒级
                else:
                    timestamp = time.time() - 0.5  # 0.5秒前的数据
                
                # 处理时间戳
                process_start = time.time()
                normalized = ensure_milliseconds_timestamp(timestamp)
                age = calculate_data_age(normalized)
                process_end = time.time()
                
                processing_times.append((process_end - process_start) * 1000)  # 转换为毫秒
                
                # 验证准确性
                if normalized > 1e12 and age >= 0:
                    accuracy_count += 1
            
            avg_processing_time = sum(processing_times) / len(processing_times)
            accuracy_rate = accuracy_count / data_count
            
            # 性能要求：平均处理时间 < 0.1ms，准确率 > 99%
            performance_ok = avg_processing_time < 0.1
            accuracy_ok = accuracy_rate > 0.99
            
            if performance_ok and accuracy_ok:
                print(f"  ✅ 高频处理: 平均{avg_processing_time:.4f}ms/次, 准确率{accuracy_rate*100:.1f}%")
                results['high_frequency_processing'] = {'passed': 1, 'total': 1, 'score': 100}
            else:
                print(f"  ❌ 高频处理不达标: 平均{avg_processing_time:.4f}ms/次, 准确率{accuracy_rate*100:.1f}%")
                results['high_frequency_processing'] = {'passed': 0, 'total': 1, 'score': 0}
                
        except Exception as e:
            print(f"  ❌ 高频处理测试失败: {e}")
            results['high_frequency_processing'] = {'passed': 0, 'total': 1, 'score': 0}
        
        return results
    
    def _calculate_overall_score(self):
        """计算总体分数"""
        all_scores = []
        
        for category, tests in self.test_results.items():
            if category in ['basic_core_tests', 'complex_system_tests', 'production_simulation_tests']:
                for test_name, test_result in tests.items():
                    if isinstance(test_result, dict) and 'score' in test_result:
                        all_scores.append(test_result['score'])
        
        if all_scores:
            self.test_results['overall_score'] = sum(all_scores) / len(all_scores)
        else:
            self.test_results['overall_score'] = 0.0
    
    def generate_final_report(self) -> str:
        """生成最终报告"""
        report = []
        report.append("🏛️ 机构级别时间戳统一性综合测试报告")
        report.append("=" * 80)
        
        # 总体评分
        score = self.test_results['overall_score']
        if score >= 95:
            status = "🏆 优秀 - 机构级别质量标准"
            conclusion = "✅ 时间戳统一性修复完美！可以部署到生产环境！"
        elif score >= 80:
            status = "⚠️ 良好 - 需要改进"
            conclusion = "⚠️ 大部分功能正常，但存在需要改进的地方"
        else:
            status = "❌ 不合格 - 需要重大修复"
            conclusion = "❌ 存在严重问题，不能部署到生产环境"
        
        report.append(f"📊 总体评分: {score:.1f}/100 - {status}")
        report.append("")
        
        # 详细结果
        for category, tests in self.test_results.items():
            if category in ['basic_core_tests', 'complex_system_tests', 'production_simulation_tests']:
                category_name = {
                    'basic_core_tests': '① 基础核心测试',
                    'complex_system_tests': '② 复杂系统级联测试', 
                    'production_simulation_tests': '③ 生产模拟测试'
                }[category]
                
                report.append(f"📋 {category_name}:")
                for test_name, result in tests.items():
                    if isinstance(result, dict) and 'score' in result:
                        status_icon = "✅" if result['score'] >= 90 else "⚠️" if result['score'] >= 70 else "❌"
                        report.append(f"  {status_icon} {test_name}: {result['passed']}/{result['total']} ({result['score']:.1f}%)")
                report.append("")
        
        # 关键失败
        if self.test_results['critical_failures']:
            report.append("🔥 关键失败:")
            for failure in self.test_results['critical_failures']:
                report.append(f"  ❌ {failure}")
            report.append("")
        
        report.append(f"🎯 最终结论: {conclusion}")
        
        return "\n".join(report)

async def main():
    """主函数"""
    tester = InstitutionalComprehensiveTest()
    results = await tester.run_all_tests()
    
    print("\n" + "=" * 80)
    print(tester.generate_final_report())
    
    # 返回成功状态
    return results['overall_score'] >= 95

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
