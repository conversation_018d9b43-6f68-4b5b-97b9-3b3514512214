#!/usr/bin/env python3
"""
测试Gate.io时间戳修复
验证Gate.io API返回的时间戳是否正确处理
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "123"
sys.path.insert(0, str(project_root))

async def test_gate_time_fix():
    """测试Gate.io时间戳修复"""
    print("🔍 测试Gate.io时间戳修复...")
    
    try:
        # 导入Gate.io交易所类
        from exchanges.gate_exchange import GateExchange
        
        # 创建实例（使用空的API密钥进行时间测试）
        gate = GateExchange(api_key="test", api_secret="test")
        
        # 获取服务器时间
        print("📡 获取Gate.io服务器时间...")
        server_time = await gate.get_server_time()
        
        # 获取当前本地时间
        current_time = int(time.time() * 1000)
        
        # 计算时间差
        time_diff = abs(server_time - current_time)
        
        print(f"🕐 Gate.io服务器时间: {server_time}")
        print(f"🕐 本地当前时间: {current_time}")
        print(f"⏱️  时间差: {time_diff}ms")
        
        # 验证时间戳格式
        if server_time > 1e12:  # 毫秒级时间戳
            print("✅ 时间戳格式正确: 毫秒级")
        else:
            print("❌ 时间戳格式错误: 不是毫秒级")
            return False
            
        # 验证时间差是否合理（应该在10秒内）
        if time_diff < 10000:  # 10秒 = 10000ms
            print("✅ 时间同步正常: 时间差在合理范围内")
        else:
            print("❌ 时间同步异常: 时间差过大")
            return False
            
        print("🎉 Gate.io时间戳修复验证成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_timestamp_sync_threshold():
    """测试800ms时间戳同步阈值"""
    print("\n🔍 测试800ms时间戳同步阈值...")
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 获取Gate.io时间戳处理器
        processor = get_timestamp_processor("gate")
        
        # 模拟两个时间戳，差异在800ms内
        current_time = int(time.time() * 1000)
        timestamp1 = current_time
        timestamp2 = current_time + 500  # 500ms差异
        
        # 验证同步
        is_synced, time_diff = processor.validate_cross_exchange_sync(
            timestamp1, timestamp2, "gate", "bybit", max_diff_ms=800
        )
        
        print(f"📊 时间戳1: {timestamp1}")
        print(f"📊 时间戳2: {timestamp2}")
        print(f"⏱️  时间差: {time_diff}ms")
        print(f"🔄 同步状态: {'✅ 同步' if is_synced else '❌ 不同步'}")
        
        if is_synced and time_diff == 500:
            print("✅ 800ms阈值测试通过")
            return True
        else:
            print("❌ 800ms阈值测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 阈值测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始Gate.io时间戳修复验证测试\n")
    
    # 测试1: Gate.io时间戳修复
    test1_result = await test_gate_time_fix()
    
    # 测试2: 800ms同步阈值
    test2_result = await test_timestamp_sync_threshold()
    
    # 总结
    print("\n📋 测试结果总结:")
    print(f"   Gate.io时间戳修复: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   800ms同步阈值: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！Gate.io时间戳问题已修复！")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    asyncio.run(main())
