
🔍 时间戳统一性审计报告
==================================================

📊 总体统计:
- 检查文件数: 502
- 发现问题数: 1483
- 涉及文件数: 94

🚨 高风险文件 (包含多种时间戳操作):
- 123/check_network_latency.py (4种模式)
- 123/config/network_config.py (4种模式)
- 123/exchanges/gate_exchange.py (5种模式)
- 123/exchanges/okx_exchange.py (8种模式)
- 123/exchanges/bybit_exchange.py (6种模式)
- 123/exchanges/exchanges_base.py (6种模式)
- 123/trading/order_manager.py (4种模式)
- 123/trading/futures_trader.py (5种模式)
- 123/trading/spot_trader.py (5种模式)
- 123/utils/helpers.py (4种模式)
- 123/tests/final_fix_quality_assurance_report.py (4种模式)
- 123/tests/comprehensive_precision_validation.py (3种模式)
- 123/tests/institutional_quality_spk_usdt_fix_test.py (5种模式)
- 123/官方SDK/gatews-master/python/gate_ws/client.py (4种模式)
- 123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py (4种模式)
- 123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py (3种模式)
- 123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py (3种模式)
- 123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py (3种模式)
- 123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py (3种模式)
- 123/monitoring/position_monitor.py (7种模式)
- 123/monitoring/performance_monitor.py (7种模式)
- 123/monitoring/risk_monitor.py (7种模式)
- 123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py (7种模式)
- 123/diagnostic_scripts/websocket_blockage_diagnosis.py (3种模式)
- 123/diagnostic_scripts/async_precision_diagnosis.py (3种模式)
- 123/diagnostic_scripts/orderbook_sync_diagnosis.py (5种模式)
- 123/diagnostic_scripts/exchange_consistency_diagnosis.py (3种模式)
- 123/diagnostic_scripts/orderbook_sync_fix.py (3种模式)
- 123/websocket/unified_connection_pool_manager.py (3种模式)
- 123/websocket/unified_timestamp_processor.py (9种模式)
- 123/websocket/orderbook_validator.py (8种模式)
- 123/websocket/unified_data_formatter.py (3种模式)
- 123/websocket/error_handler.py (5种模式)
- 123/websocket/ws_manager.py (3种模式)
- 123/websocket/gate_ws.py (6种模式)
- 123/websocket/bybit_ws.py (6种模式)
- 123/websocket/performance_monitor.py (5种模式)
- 123/websocket/enhanced_ws_client_base.py (3种模式)
- 123/websocket/ws_client.py (5种模式)
- 123/websocket/okx_ws.py (4种模式)
- 123/core/unified_order_spread_calculator.py (7种模式)
- 123/core/execution_engine.py (11种模式)
- 123/core/system_monitor.py (7种模式)
- 123/core/trading_system_initializer.py (3种模式)
- 123/core/unified_leverage_manager.py (3种模式)
- 123/core/unified_closing_manager.py (6种模式)
- 123/core/convergence_monitor.py (3种模式)
- 123/core/opportunity_scanner.py (10种模式)
- 123/core/data_snapshot_validator.py (6种模式)
- 123/core/universal_token_system.py (5种模式)
- 123/core/trading_rules_preloader.py (7种模式)

⚠️ 潜在BUG (26个):
- 123/websocket/unified_timestamp_processor.py:492 - age_ms = abs(current_time - timestamp)
- 123/websocket/unified_timestamp_processor.py:563 - time_diff_ms = abs(current_time - min(aligned_timestamp1, aligned_timestamp2))
- 123/websocket/orderbook_validator.py:358 - data_age_spot = current_time - spot_timestamp
- 123/websocket/orderbook_validator.py:359 - data_age_futures = current_time - futures_timestamp
- 123/websocket/error_handler.py:318 - recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
- 123/websocket/performance_monitor.py:263 - if current_time - error["timestamp"] <= 3600:  # 1小时
- 123/core/unified_order_spread_calculator.py:707 - snapshot_age = current_time - snapshot['snapshot_timestamp']
- 123/core/execution_engine.py:1404 - spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
- 123/core/execution_engine.py:1405 - futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
- 123/core/execution_engine.py:1726 - data_age_spot = current_time - spot_timestamp
- 123/core/execution_engine.py:1727 - data_age_futures = current_time - futures_timestamp
- 123/core/execution_engine.py:2391 - data_age = current_time - orderbook.get('timestamp', current_time)
- 123/core/execution_engine.py:2844 - spot_data_age = current_time - spot_orderbook.get('timestamp', current_time)
- 123/core/execution_engine.py:2845 - futures_data_age = current_time - futures_orderbook.get('timestamp', current_time)
- 123/core/system_monitor.py:469 - if current_time - issue.timestamp < 300
- 123/core/unified_leverage_manager.py:441 - if (current_time - cache_entry.get('timestamp', 0)) >= self.cache_ttl:
- 123/core/opportunity_scanner.py:418 - data_age = current_time - stored_data.timestamp
- 123/core/opportunity_scanner.py:1475 - data_age = current_time - timestamp_seconds
- 123/core/opportunity_scanner.py:1884 - data_age_spot = current_time - spot_timestamp
- 123/core/opportunity_scanner.py:1885 - data_age_futures = current_time - futures_timestamp
- 123/core/data_snapshot_validator.py:84 - snapshot_age = current_time - snapshot['snapshot_timestamp']
- 123/core/data_snapshot_validator.py:256 - spot_age = current_time - spot_data.timestamp
- 123/core/data_snapshot_validator.py:274 - futures_age = current_time - futures_data.timestamp
- 123/core/universal_token_system.py:75 - if current_time - self._cache_timestamp < self._cache_ttl:
- 123/core/universal_token_system.py:137 - return (current_time - self._cache_timestamp) < self._cache_ttl
- 123/core/trading_rules_preloader.py:711 - if time.time() - rule.timestamp < self.trading_rules_ttl:

📈 模式统计:
- time_time: 608次
- timestamp_comparison: 210次
- millisecond_conversion: 204次
- timestamp_assignment: 180次
- current_time: 80次
- timestamp_field: 70次
- time_comparison: 35次
- second_conversion: 32次
- potential_unit_mismatch: 26次
- age_calculation: 19次
- data_age: 13次
- time_diff: 6次


详细问题列表:
==================================================
123/check_network_latency.py:111 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/check_network_latency.py:121 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/check_network_latency.py:122 [millisecond_conversion]
  代码: latency = (end_time - start_time) * 1000
  匹配: * 1000

123/check_network_latency.py:151 [time_time]
  代码: local_time = time.time() * 1000  # 本地时间（毫秒）
  匹配: time.time()

123/check_network_latency.py:151 [millisecond_conversion]
  代码: local_time = time.time() * 1000  # 本地时间（毫秒）
  匹配: * 1000

123/check_network_latency.py:172 [time_diff]
  代码: time_diff = abs(server_time - local_time)
  匹配: time_diff =

123/check_network_latency.py:190 [time_diff]
  代码: time_diff = abs(server_times[ex1] - server_times[ex2])
  匹配: time_diff =

123/check_network_latency.py:203 [second_conversion]
  代码: return int(result["timeNano"]) // 1000000
  匹配: / 1000

123/check_network_latency.py:209 [millisecond_conversion]
  代码: return int(result["timeSecond"]) * 1000
  匹配: * 1000

123/vps_network_test.py:54 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/vps_network_test.py:61 [time_time]
  代码: latency = (time.time() - start_time) * 1000
  匹配: time.time()

123/vps_network_test.py:61 [millisecond_conversion]
  代码: latency = (time.time() - start_time) * 1000
  匹配: * 1000

123/vps_network_test.py:101 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/vps_network_test.py:111 [time_time]
  代码: latency = (time.time() - start_time) * 1000
  匹配: time.time()

123/vps_network_test.py:111 [millisecond_conversion]
  代码: latency = (time.time() - start_time) * 1000
  匹配: * 1000

123/main.py:156 [time_comparison]
  代码: self._arbitrage_log_interval = 10  # 套利机会日志间隔（秒）
  匹配: = 10  # 套利机会日志间隔（秒

123/main.py:158 [time_comparison]
  代码: self._spot_premium_log_interval = 60  # 现货溢价日志间隔（60秒）
  匹配: = 60  # 现货溢价日志间隔（60秒

123/clean_cache.py:115 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/clean_cache.py:170 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/config/network_config.py:20 [time_comparison]
  代码: keepalive_timeout: int = 60              # 保持连接60秒
  匹配: = 60              # 保持连接60秒

123/config/network_config.py:27 [time_comparison]
  代码: ws_connect_timeout: int = 10             # WebSocket连接超时10秒
  匹配: = 10             # WebSocket连接超时10秒

123/config/network_config.py:28 [time_comparison]
  代码: ws_reconnect_timeout: int = 3000         # WebSocket重连超时3秒
  匹配: = 3000         # WebSocket重连超时3秒

123/config/network_config.py:30 [time_comparison]
  代码: ws_heartbeat_interval: int = 20          # 🔥 正确修复：WebSocket心跳间隔统一为20秒，符合文档要求
  匹配: = 20          # 🔥 正确修复：WebSocket心跳间隔统一为20秒

123/config/network_config.py:53 [millisecond_conversion]
  代码: self.logger.info(f"   🔥 重试延迟: {self._config.retry_delay*1000:.0f}ms")
  匹配: *1000

123/config/network_config.py:67 [second_conversion]
  代码: retry_delay=float(os.getenv('RETRY_DELAY', '50')) / 1000,  # 转换为秒
  匹配: / 1000

123/config/network_config.py:127 [timestamp_field]
  代码: 'timestamp_tolerance': self._config.timestamp_tolerance,
  匹配: .timestamp

123/config/network_config.py:145 [millisecond_conversion]
  代码: 'RETRY_DELAY': str(int(self._config.retry_delay * 1000)),
  匹配: * 1000

123/config/network_config.py:150 [timestamp_field]
  代码: 'TIMESTAMP_TOLERANCE': str(self._config.timestamp_tolerance),
  匹配: .timestamp

123/config/network_config.py:169 [millisecond_conversion]
  代码: print(f"  重试间隔: {self._config.retry_delay*1000:.0f}ms")
  匹配: *1000

123/config/network_config.py:174 [timestamp_field]
  代码: print(f"  时间戳容忍度: {self._config.timestamp_tolerance}ms")
  匹配: .timestamp

123/exchanges/exchange_adapters.py:193 [time_time]
  代码: params["text"] = f"t-market-{int(time.time() * 1000)}"
  匹配: time.time()

123/exchanges/exchange_adapters.py:193 [millisecond_conversion]
  代码: params["text"] = f"t-market-{int(time.time() * 1000)}"
  匹配: * 1000

123/exchanges/gate_exchange.py:53 [time_comparison]
  代码: self.rate_limit = 8  # 🔥 根源修复：降低到8次/秒，确保30+代币健壮启动
  匹配: = 8  # 🔥 根源修复：降低到8次/秒

123/exchanges/gate_exchange.py:113 [time_time]
  代码: timestamp = time.time()  # 官方SDK使用浮点数: t = time.time()
  匹配: time.time()

123/exchanges/gate_exchange.py:113 [time_time]
  代码: timestamp = time.time()  # 官方SDK使用浮点数: t = time.time()
  匹配: time.time()

123/exchanges/gate_exchange.py:113 [timestamp_comparison]
  代码: timestamp = time.time()  # 官方SDK使用浮点数: t = time.time()
  匹配: timestamp =

123/exchanges/gate_exchange.py:113 [timestamp_assignment]
  代码: timestamp = time.time()  # 官方SDK使用浮点数: t = time.time()
  匹配: timestamp =

123/exchanges/gate_exchange.py:273 [time_time]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: time.time()

123/exchanges/gate_exchange.py:273 [millisecond_conversion]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: * 1000

123/exchanges/gate_exchange.py:464 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:464 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:603 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:603 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:705 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:705 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:775 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:775 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:838 [time_time]
  代码: "timestamp": int(response.get("create_time", time.time())) * 1000
  匹配: time.time()

123/exchanges/gate_exchange.py:838 [millisecond_conversion]
  代码: "timestamp": int(response.get("create_time", time.time())) * 1000
  匹配: * 1000

123/exchanges/gate_exchange.py:870 [time_time]
  代码: "timestamp": int(order_data.get("create_time", time.time())) * 1000
  匹配: time.time()

123/exchanges/gate_exchange.py:870 [millisecond_conversion]
  代码: "timestamp": int(order_data.get("create_time", time.time())) * 1000
  匹配: * 1000

123/exchanges/gate_exchange.py:1436 [millisecond_conversion]
  代码: return int(result["server_time"]) * 1000
  匹配: * 1000

123/exchanges/gate_exchange.py:1439 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:1439 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:1442 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:1442 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:1446 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:1446 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/gate_exchange.py:1449 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/gate_exchange.py:1449 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:99 [time_comparison]
  代码: self.rate_limit = 2  # 🔥 优化修复：提升到2次/秒，平衡限速和WebSocket性能
  匹配: = 2  # 🔥 优化修复：提升到2次/秒

123/exchanges/okx_exchange.py:181 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/exchanges/okx_exchange.py:181 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/exchanges/okx_exchange.py:194 [timestamp_comparison]
  代码: timestamp = utc_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
  匹配: timestamp =

123/exchanges/okx_exchange.py:194 [timestamp_assignment]
  代码: timestamp = utc_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
  匹配: timestamp =

123/exchanges/okx_exchange.py:221 [time_time]
  代码: local_before = time.time()
  匹配: time.time()

123/exchanges/okx_exchange.py:223 [time_time]
  代码: local_after = time.time()
  匹配: time.time()

123/exchanges/okx_exchange.py:238 [second_conversion]
  代码: server_time = server_timestamp_ms / 1000
  匹配: / 1000

123/exchanges/okx_exchange.py:245 [time_time]
  代码: self.last_server_time_check = time.time()
  匹配: time.time()

123/exchanges/okx_exchange.py:260 [age_calculation]
  代码: message = timestamp + method.upper() + path + body
  匹配: age = timestamp

123/exchanges/okx_exchange.py:325 [timestamp_comparison]
  代码: timestamp = self._get_timestamp()
  匹配: timestamp =

123/exchanges/okx_exchange.py:325 [timestamp_assignment]
  代码: timestamp = self._get_timestamp()
  匹配: timestamp =

123/exchanges/okx_exchange.py:329 [time_time]
  代码: time.time() - self.last_server_time_check > 300):
  匹配: time.time()

123/exchanges/okx_exchange.py:330 [timestamp_comparison]
  代码: timestamp = await self._get_server_time() or timestamp
  匹配: timestamp =

123/exchanges/okx_exchange.py:330 [timestamp_assignment]
  代码: timestamp = await self._get_server_time() or timestamp
  匹配: timestamp =

123/exchanges/okx_exchange.py:611 [time_time]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: time.time()

123/exchanges/okx_exchange.py:611 [millisecond_conversion]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: * 1000

123/exchanges/okx_exchange.py:789 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:789 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:982 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:982 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:1115 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:1115 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:1179 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:1179 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:1213 [time_time]
  代码: "timestamp": int(order.get("cTime", 0) or time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:1213 [millisecond_conversion]
  代码: "timestamp": int(order.get("cTime", 0) or time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:1510 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:1510 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/okx_exchange.py:1514 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/okx_exchange.py:1514 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:42 [time_comparison]
  代码: self.rate_limit = 4  # 🔥 根源修复：降低到4次/秒，确保30+代币健壮启动
  匹配: = 4  # 🔥 根源修复：降低到4次/秒

123/exchanges/bybit_exchange.py:121 [second_conversion]
  代码: server_time = int(result["timeNano"]) // 1000000
  匹配: / 1000

123/exchanges/bybit_exchange.py:129 [millisecond_conversion]
  代码: server_time = int(result["timeSecond"]) * 1000
  匹配: * 1000

123/exchanges/bybit_exchange.py:143 [time_time]
  代码: local_time = int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:143 [millisecond_conversion]
  代码: local_time = int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:160 [time_time]
  代码: # 官方SDK标准：int(time.time() * 10**3)
  匹配: time.time()

123/exchanges/bybit_exchange.py:161 [time_time]
  代码: local_time = int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:161 [millisecond_conversion]
  代码: local_time = int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:262 [timestamp_comparison]
  代码: timestamp = str(timestamp)
  匹配: timestamp =

123/exchanges/bybit_exchange.py:262 [timestamp_assignment]
  代码: timestamp = str(timestamp)
  匹配: timestamp =

123/exchanges/bybit_exchange.py:490 [timestamp_comparison]
  代码: fixed_timestamp = None
  匹配: timestamp =

123/exchanges/bybit_exchange.py:490 [timestamp_assignment]
  代码: fixed_timestamp = None
  匹配: timestamp =

123/exchanges/bybit_exchange.py:499 [timestamp_comparison]
  代码: fixed_timestamp = self._get_timestamp()
  匹配: timestamp =

123/exchanges/bybit_exchange.py:499 [timestamp_assignment]
  代码: fixed_timestamp = self._get_timestamp()
  匹配: timestamp =

123/exchanges/bybit_exchange.py:780 [time_time]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: time.time()

123/exchanges/bybit_exchange.py:780 [millisecond_conversion]
  代码: 'timestamp': int(time.time() * 1000),
  匹配: * 1000

123/exchanges/bybit_exchange.py:877 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:877 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:984 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:984 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:1130 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:1130 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:1195 [time_time]
  代码: "timestamp": int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:1195 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:1377 [second_conversion]
  代码: server_time_ms = time_nano // 1000000
  匹配: / 1000

123/exchanges/bybit_exchange.py:1388 [millisecond_conversion]
  代码: server_time_ms = time_second * 1000
  匹配: * 1000

123/exchanges/bybit_exchange.py:1408 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:1408 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:1412 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:1412 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/bybit_exchange.py:1415 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/bybit_exchange.py:1415 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/exchanges_base.py:236 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/exchanges/exchanges_base.py:236 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/exchanges/exchanges_base.py:237 [time_diff]
  代码: time_diff = current_time - self.last_request_time
  匹配: time_diff =

123/exchanges/exchanges_base.py:243 [time_time]
  代码: self.last_request_time = time.time()
  匹配: time.time()

123/exchanges/exchanges_base.py:346 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/exchanges_base.py:346 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/exchanges_base.py:349 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/exchanges_base.py:349 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/exchanges/exchanges_base.py:430 [timestamp_comparison]
  代码: timestamp=timestamp
  匹配: timestamp=

123/exchanges/exchanges_base.py:430 [timestamp_assignment]
  代码: timestamp=timestamp
  匹配: timestamp=

123/exchanges/exchanges_base.py:441 [time_time]
  代码: "timestamp": timestamp or int(time.time() * 1000),
  匹配: time.time()

123/exchanges/exchanges_base.py:441 [millisecond_conversion]
  代码: "timestamp": timestamp or int(time.time() * 1000),
  匹配: * 1000

123/exchanges/exchanges_base.py:529 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/exchanges/exchanges_base.py:529 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/trading/order_manager.py:153 [time_time]
  代码: timestamp = int(time.time() * 1000)
  匹配: time.time()

123/trading/order_manager.py:153 [timestamp_comparison]
  代码: timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/trading/order_manager.py:153 [millisecond_conversion]
  代码: timestamp = int(time.time() * 1000)
  匹配: * 1000

123/trading/order_manager.py:153 [timestamp_assignment]
  代码: timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/trading/order_manager.py:225 [time_time]
  代码: order.spot_start_time = time.time()
  匹配: time.time()

123/trading/order_manager.py:229 [time_time]
  代码: order.spot_end_time = time.time()
  匹配: time.time()

123/trading/order_manager.py:243 [time_time]
  代码: order.futures_start_time = time.time()
  匹配: time.time()

123/trading/order_manager.py:257 [time_time]
  代码: order.futures_end_time = time.time()
  匹配: time.time()

123/trading/order_manager.py:266 [millisecond_conversion]
  代码: hedge_delay = (order.futures_start_time - order.spot_end_time) * 1000
  匹配: * 1000

123/trading/order_manager.py:609 [millisecond_conversion]
  代码: "average_execution_time_ms": (total_execution_time / max(1, completed_orders_count)) * 1000
  匹配: * 1000

123/trading/order_manager.py:624 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/trading/futures_trader.py:226 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/trading/futures_trader.py:282 [time_time]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/trading/futures_trader.py:282 [millisecond_conversion]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/trading/futures_trader.py:344 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/trading/futures_trader.py:344 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:344 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/trading/futures_trader.py:344 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:373 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:373 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:373 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:373 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:379 [time_time]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/trading/futures_trader.py:379 [millisecond_conversion]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/trading/futures_trader.py:397 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:397 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:397 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:397 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:420 [time_time]
  代码: hedge_delay = (time.time() - hedge_start_time) * 1000
  匹配: time.time()

123/trading/futures_trader.py:420 [millisecond_conversion]
  代码: hedge_delay = (time.time() - hedge_start_time) * 1000
  匹配: * 1000

123/trading/futures_trader.py:475 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:475 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:475 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:475 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:553 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:553 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:553 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:553 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:594 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/trading/futures_trader.py:594 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:594 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/trading/futures_trader.py:594 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:651 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/trading/futures_trader.py:651 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:651 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/trading/futures_trader.py:651 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/futures_trader.py:671 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:671 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:671 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:671 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:688 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/futures_trader.py:688 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:688 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/futures_trader.py:688 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/futures_trader.py:868 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/trading/futures_trader.py:962 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/trading/futures_trader.py:1103 [time_time]
  代码: hedge_start_time = time.time()
  匹配: time.time()

123/trading/spot_trader.py:169 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/trading/spot_trader.py:193 [time_time]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/trading/spot_trader.py:193 [millisecond_conversion]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/trading/spot_trader.py:288 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/trading/spot_trader.py:288 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/spot_trader.py:288 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/trading/spot_trader.py:288 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/spot_trader.py:302 [time_time]
  代码: wait_start = time.time()
  匹配: time.time()

123/trading/spot_trader.py:304 [time_time]
  代码: while (time.time() - wait_start) < wait_timeout:
  匹配: time.time()

123/trading/spot_trader.py:346 [time_time]
  代码: wait_time = (time.time() - wait_start) * 1000
  匹配: time.time()

123/trading/spot_trader.py:346 [millisecond_conversion]
  代码: wait_time = (time.time() - wait_start) * 1000
  匹配: * 1000

123/trading/spot_trader.py:359 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/trading/spot_trader.py:359 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/spot_trader.py:359 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/trading/spot_trader.py:359 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/trading/spot_trader.py:378 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:378 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:378 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:378 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:391 [time_time]
  代码: wait_time = (time.time() - wait_start) * 1000
  匹配: time.time()

123/trading/spot_trader.py:391 [millisecond_conversion]
  代码: wait_time = (time.time() - wait_start) * 1000
  匹配: * 1000

123/trading/spot_trader.py:420 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:420 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:420 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:420 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:443 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:443 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:443 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:443 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:463 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:463 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:463 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:463 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:485 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:485 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:485 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:485 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:491 [time_time]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/trading/spot_trader.py:491 [millisecond_conversion]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/trading/spot_trader.py:508 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:508 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:508 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:508 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:526 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/trading/spot_trader.py:562 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:562 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:562 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:562 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:619 [time_time]
  代码: timestamp=int(time.time() * 1000),
  匹配: time.time()

123/trading/spot_trader.py:619 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:619 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000),
  匹配: * 1000

123/trading/spot_trader.py:619 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000),
  匹配: timestamp=

123/trading/spot_trader.py:725 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/trading/spot_trader.py:780 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/trading/spot_trader.py:875 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/trading/spot_trader.py:930 [timestamp_field]
  代码: 'timestamp': result.timestamp
  匹配: .timestamp

123/utils/cache_monitor.py:63 [time_time]
  代码: self.cache_stats["balance_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:76 [time_time]
  代码: self.cache_stats["balance_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:86 [time_time]
  代码: self.cache_stats["margin_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:100 [time_time]
  代码: self.cache_stats["trading_rules_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:116 [time_time]
  代码: self.cache_stats["orderbook_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:138 [time_time]
  代码: self.cache_stats["hedge_quality_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/cache_monitor.py:161 [time_time]
  代码: self.cache_stats["precision_cache"]["last_update"] = time.time()
  匹配: time.time()

123/utils/helpers.py:19 [second_conversion]
  代码: return f"{amount/1000000:.1f}M {currency}"
  匹配: /1000

123/utils/helpers.py:21 [second_conversion]
  代码: return f"{amount/1000:.1f}K {currency}"
  匹配: /1000

123/utils/helpers.py:55 [time_time]
  代码: timestamp = time.time()
  匹配: time.time()

123/utils/helpers.py:55 [timestamp_comparison]
  代码: timestamp = time.time()
  匹配: timestamp =

123/utils/helpers.py:55 [timestamp_assignment]
  代码: timestamp = time.time()
  匹配: timestamp =

123/utils/helpers.py:143 [second_conversion]
  代码: seconds = milliseconds / 1000
  匹配: / 1000

123/utils/margin_calculator.py:116 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/utils/margin_calculator.py:116 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/utils/margin_calculator.py:728 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/utils/margin_calculator.py:728 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/utils/min_order_detector.py:58 [time_time]
  代码: if time.time() - cache_data.get("last_update", 0) < self.cache_ttl:
  匹配: time.time()

123/utils/min_order_detector.py:73 [time_time]
  代码: result["last_update"] = int(time.time())
  匹配: time.time()

123/utils/min_order_detector.py:96 [time_time]
  代码: "last_update": int(time.time()),
  匹配: time.time()

123/utils/notification.py:197 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime("%H:%M:%S")
  匹配: timestamp =

123/utils/notification.py:197 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime("%H:%M:%S")
  匹配: timestamp =

123/tests/comprehensive_fix_validation.py:163 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:171 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:204 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:212 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:275 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:283 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:300 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:308 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:336 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:344 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:425 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:433 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:448 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:451 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:480 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:488 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:508 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:516 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:552 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:560 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:585 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:593 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:657 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:660 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:683 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:691 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:706 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:708 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:730 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:732 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:751 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:759 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:774 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:776 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:794 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:802 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:838 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:846 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:883 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/comprehensive_fix_validation.py:891 [time_time]
  代码: "execution_time": time.time()
  匹配: time.time()

123/tests/ultimate_verification.py:32 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/tests/ultimate_verification.py:176 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/ultimate_verification.py:192 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/real_code_diagnosis.py:24 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/tests/websocket_fix_verification.py:394 [time_time]
  代码: result_file = f"diagnostic_results/websocket_fix_verification_{int(time.time())}.json"
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:424 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:426 [time_time]
  代码: api_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:426 [millisecond_conversion]
  代码: api_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/final_fix_quality_assurance_report.py:428 [time_comparison]
  代码: real_api_response = rule is not None and api_time < 5000  # 5秒内响应
  匹配: < 5000  # 5秒

123/tests/final_fix_quality_assurance_report.py:446 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:448 [time_time]
  代码: response_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:448 [millisecond_conversion]
  代码: response_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/final_fix_quality_assurance_report.py:474 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:477 [time_time]
  代码: concurrent_time = time.time() - start_time
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:477 [current_time]
  代码: concurrent_time = time.time() - start_time
  匹配: current_time =

123/tests/final_fix_quality_assurance_report.py:591 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:643 [time_time]
  代码: print(f"   ⏱️ 验证耗时: {time.time() - start_time:.2f}秒")
  匹配: time.time()

123/tests/final_fix_quality_assurance_report.py:679 [time_time]
  代码: "verification_time": time.time() - start_time,
  匹配: time.time()

123/tests/precise_problem_diagnosis.py:32 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/tests/comprehensive_precision_validation.py:821 [millisecond_conversion]
  代码: duration_ms = (end_time - start_time) * 1000
  匹配: * 1000

123/tests/comprehensive_precision_validation.py:832 [millisecond_conversion]
  代码: duration_ms = (end_time - start_time) * 1000
  匹配: * 1000

123/tests/comprehensive_precision_validation.py:937 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/tests/comprehensive_precision_validation.py:937 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/tests/institutional_quality_spk_usdt_fix_test.py:216 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:218 [time_time]
  代码: single_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:218 [millisecond_conversion]
  代码: single_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:232 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:235 [time_time]
  代码: batch_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:235 [millisecond_conversion]
  代码: batch_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:245 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:248 [time_time]
  代码: concurrent_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:248 [current_time]
  代码: concurrent_time = (time.time() - start_time) * 1000
  匹配: current_time =

123/tests/institutional_quality_spk_usdt_fix_test.py:248 [millisecond_conversion]
  代码: concurrent_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:256 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:258 [time_time]
  代码: first_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:258 [millisecond_conversion]
  代码: first_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:261 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:263 [time_time]
  代码: cache_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:263 [millisecond_conversion]
  代码: cache_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:355 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:366 [time_time]
  代码: stress_time = time.time() - start_time
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:367 [second_conversion]
  代码: stress_rate = (success_count / 1000) * 100
  匹配: / 1000

123/tests/institutional_quality_spk_usdt_fix_test.py:368 [time_comparison]
  代码: stress_pass = stress_rate >= 95 and stress_time < 10  # 95%成功率，10秒内完成
  匹配: < 10  # 95%成功率，10秒

123/tests/institutional_quality_spk_usdt_fix_test.py:485 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_spk_usdt_fix_test.py:505 [time_time]
  代码: print(f"   测试耗时: {time.time() - start_time:.2f}秒")
  匹配: time.time()

123/tests/spk_usdt_fix_verification.py:263 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/spk_usdt_fix_verification.py:282 [time_time]
  代码: print(f"   验证耗时: {time.time() - start_time:.2f}秒")
  匹配: time.time()

123/tests/performance_optimization_verification.py:74 [millisecond_conversion]
  代码: 'max_total_wait_time_ms': max_wait_time * 1000,
  匹配: * 1000

123/tests/performance_optimization_verification.py:141 [second_conversion]
  代码: 'optimization_effect': f'从10秒优化到{total_latency/1000:.1f}秒，提升{10000/total_latency:.1f}倍'
  匹配: /1000

123/tests/performance_optimization_verification.py:144 [second_conversion]
  代码: self.logger.info(f"✅ 整体执行链路延迟: {total_latency}ms ({total_latency/1000:.1f}秒)")
  匹配: /1000

123/tests/performance_optimization_verification.py:175 [second_conversion]
  代码: "optimized_latency": f"{self.test_results.get('overall_latency', {}).get('total_latency_ms', 0)/1000:.1f}秒",
  匹配: /1000

123/tests/performance_optimization_verification.py:200 [second_conversion]
  代码: self.logger.info(f"   优化延迟: {overall.get('total_latency_ms', 0)/1000:.1f}秒")
  匹配: /1000

123/tests/institutional_fix_verification.py:29 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/tests/institutional_fix_verification.py:298 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_fix_verification.py:307 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/spk_usdt_trading_rules_diagnosis.py:269 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/spk_usdt_trading_rules_diagnosis.py:288 [time_time]
  代码: print(f"   诊断耗时: {time.time() - start_time:.2f}秒")
  匹配: time.time()

123/tests/institutional_quality_assurance.py:120 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:170 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:180 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:222 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:232 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:284 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:294 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:344 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:354 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:390 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:437 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:487 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:497 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:541 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:551 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:570 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:573 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:583 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:618 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:628 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:678 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:688 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:726 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:773 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:802 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:804 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:824 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:834 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:872 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:882 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:909 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:912 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:921 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:949 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:959 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1004 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1012 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1047 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1057 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1067 [time_comparison]
  代码: test_duration = 30  # 30秒测试
  匹配: = 30  # 30秒

123/tests/institutional_quality_assurance.py:1068 [time_comparison]
  代码: test_interval = 1   # 每秒测试一次
  匹配: = 1   # 每秒

123/tests/institutional_quality_assurance.py:1071 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1073 [time_time]
  代码: while time.time() - start_time < test_duration:
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1074 [time_time]
  代码: iteration_start = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1077 [time_time]
  代码: iteration_end = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1086 [time_time]
  代码: iteration_end = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1123 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1133 [time_time]
  代码: "start_time": time.time(),
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1152 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1159 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/tests/institutional_quality_assurance.py:1195 [time_time]
  代码: test_result["end_time"] = time.time()
  匹配: time.time()

123/官方SDK/gatews-master/python/gate_ws/client.py:96 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/官方SDK/gatews-master/python/gate_ws/client.py:139 [time_time]
  代码: data_time = int(time.time())
  匹配: time.time()

123/官方SDK/gatews-master/python/gate_ws/client.py:172 [timestamp_field]
  代码: self.timestamp = msg.get("time")
  匹配: .timestamp

123/官方SDK/gatews-master/python/gate_ws/client.py:172 [timestamp_comparison]
  代码: self.timestamp = msg.get("time")
  匹配: timestamp =

123/官方SDK/gatews-master/python/gate_ws/client.py:172 [timestamp_assignment]
  代码: self.timestamp = msg.get("time")
  匹配: timestamp =

123/官方SDK/gatews-master/python/gate_ws/client.py:213 [time_time]
  代码: {"time": int(time.time()), "channel": "%s.ping" % self.cfg.app}
  匹配: time.time()

123/官方SDK/okx-sdk-master/okx/utils.py:53 [age_calculation]
  代码: message = str(timestamp) + str.upper(method) + request_path + str(body)
  匹配: age = str(timestamp

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:8 [timestamp_comparison]
  代码: timestamp = local_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:8 [timestamp_assignment]
  代码: timestamp = local_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:10 [timestamp_comparison]
  代码: timestamp = server_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:10 [timestamp_assignment]
  代码: timestamp = server_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:11 [age_calculation]
  代码: message = str(timestamp) + 'GET' + '/users/self/verify'
  匹配: age = str(timestamp

123/官方SDK/okx-sdk-master/okx/wsapi/wsutils.py:70 [time_time]
  代码: return int(time.time())
  匹配: time.time()

123/官方SDK/okx-sdk-master/okx/restapi/BaseClient.py:30 [timestamp_comparison]
  代码: timestamp = self.local_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/restapi/BaseClient.py:30 [timestamp_assignment]
  代码: timestamp = self.local_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/restapi/BaseClient.py:32 [timestamp_comparison]
  代码: timestamp = self.server_time()
  匹配: timestamp =

123/官方SDK/okx-sdk-master/okx/restapi/BaseClient.py:32 [timestamp_assignment]
  代码: timestamp = self.server_time()
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/api_client.py:582 [time_time]
  代码: t = time.time()
  匹配: time.time()

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/api_client(1).py:582 [time_time]
  代码: t = time.time()
  匹配: time.time()

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:65 [timestamp_comparison]
  代码: def __init__(self, id=None, txid=None, withdraw_order_id=None, timestamp=None, amount=None, currency=None, address=None, memo=None, withdraw_id=None, asset_class=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:65 [timestamp_assignment]
  代码: def __init__(self, id=None, txid=None, withdraw_order_id=None, timestamp=None, amount=None, currency=None, address=None, memo=None, withdraw_id=None, asset_class=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:75 [timestamp_comparison]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:75 [timestamp_assignment]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:93 [timestamp_field]
  代码: self.timestamp = timestamp
  匹配: .timestamp

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:93 [timestamp_comparison]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:93 [timestamp_assignment]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:198 [timestamp_comparison]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ledger_record.py:198 [timestamp_assignment]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:67 [timestamp_comparison]
  代码: def __init__(self, id=None, txid=None, block_number=None, withdraw_order_id=None, timestamp=None, amount=None, fee=None, currency=None, fail_reason=None, timestamp2=None, memo=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:67 [timestamp_assignment]
  代码: def __init__(self, id=None, txid=None, block_number=None, withdraw_order_id=None, timestamp=None, amount=None, fee=None, currency=None, fail_reason=None, timestamp2=None, memo=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:78 [timestamp_comparison]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:78 [timestamp_assignment]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:98 [timestamp_field]
  代码: self.timestamp = timestamp
  匹配: .timestamp

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:98 [timestamp_comparison]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:98 [timestamp_assignment]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:106 [timestamp_field]
  代码: self.timestamp2 = timestamp2
  匹配: .timestamp

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:226 [timestamp_comparison]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/withdrawal_record.py:226 [timestamp_assignment]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:75 [timestamp_comparison]
  代码: def __init__(self, currency_pair=None, last=None, lowest_ask=None, lowest_size=None, highest_bid=None, highest_size=None, change_percentage=None, change_utc0=None, change_utc8=None, base_volume=None, quote_volume=None, high_24h=None, low_24h=None, etf_net_value=None, etf_pre_net_value=None, etf_pre_timestamp=None, etf_leverage=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:75 [timestamp_assignment]
  代码: def __init__(self, currency_pair=None, last=None, lowest_ask=None, lowest_size=None, highest_bid=None, highest_size=None, change_percentage=None, change_utc0=None, change_utc8=None, base_volume=None, quote_volume=None, high_24h=None, low_24h=None, etf_net_value=None, etf_pre_net_value=None, etf_pre_timestamp=None, etf_leverage=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:75 [age_calculation]
  代码: def __init__(self, currency_pair=None, last=None, lowest_ask=None, lowest_size=None, highest_bid=None, highest_size=None, change_percentage=None, change_utc0=None, change_utc8=None, base_volume=None, quote_volume=None, high_24h=None, low_24h=None, etf_net_value=None, etf_pre_net_value=None, etf_pre_timestamp=None, etf_leverage=None, local_vars_configuration=None):  # noqa: E501
  匹配: age=None, change_utc0=None, change_utc8=None, base_volume=None, quote_volume=None, high_24h=None, low_24h=None, etf_net_value=None, etf_pre_net_value=None, etf_pre_timestamp

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:97 [timestamp_comparison]
  代码: self._etf_pre_timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:97 [timestamp_assignment]
  代码: self._etf_pre_timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:130 [timestamp_comparison]
  代码: self.etf_pre_timestamp = etf_pre_timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:130 [timestamp_assignment]
  代码: self.etf_pre_timestamp = etf_pre_timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:499 [timestamp_comparison]
  代码: self._etf_pre_timestamp = etf_pre_timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/ticker.py:499 [timestamp_assignment]
  代码: self._etf_pre_timestamp = etf_pre_timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:61 [timestamp_comparison]
  代码: def __init__(self, id=None, txid=None, withdraw_order_id=None, timestamp=None, amount=None, currency=None, address=None, memo=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:61 [timestamp_assignment]
  代码: def __init__(self, id=None, txid=None, withdraw_order_id=None, timestamp=None, amount=None, currency=None, address=None, memo=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
  匹配: timestamp=

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:71 [timestamp_comparison]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:71 [timestamp_assignment]
  代码: self._timestamp = None
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:87 [timestamp_field]
  代码: self.timestamp = timestamp
  匹配: .timestamp

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:87 [timestamp_comparison]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:87 [timestamp_assignment]
  代码: self.timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:188 [timestamp_comparison]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/gateapi-python-master/gateapi-python-master/gate_api/models/deposit_record.py:188 [timestamp_assignment]
  代码: self._timestamp = timestamp
  匹配: timestamp =

123/官方SDK/pybit-master/pybit-master/pybit/_helpers.py:10 [time_time]
  代码: return int(time.time() * 10**3)
  匹配: time.time()

123/官方SDK/pybit-master/pybit-master/pybit/_http_manager.py:233 [timestamp_comparison]
  代码: timestamp = _helpers.generate_timestamp()
  匹配: timestamp =

123/官方SDK/pybit-master/pybit-master/pybit/_http_manager.py:233 [timestamp_assignment]
  代码: timestamp = _helpers.generate_timestamp()
  匹配: timestamp =

123/官方SDK/pybit-master/pybit-master/pybit/_http_manager.py:237 [timestamp_comparison]
  代码: timestamp=timestamp,
  匹配: timestamp=

123/官方SDK/pybit-master/pybit-master/pybit/_http_manager.py:237 [timestamp_assignment]
  代码: timestamp=timestamp,
  匹配: timestamp=

123/官方SDK/pybit-master/pybit-master/pybit/_websocket_stream.py:203 [millisecond_conversion]
  代码: expires = _helpers.generate_timestamp() + (self.private_auth_expire * 1000)
  匹配: * 1000

123/官方SDK/pybit-master/pybit-master/tests/test_pybit.py:28 [time_time]
  代码: from_time=int(time.time()) - 60 * 60,
  匹配: time.time()

123/monitoring/position_monitor.py:63 [timestamp_field]
  代码: "timestamp": self.timestamp,
  匹配: .timestamp

123/monitoring/position_monitor.py:103 [time_time]
  代码: self.last_check_time = time.time()
  匹配: time.time()

123/monitoring/position_monitor.py:119 [time_comparison]
  代码: self.monitor_interval = 5  # 5秒检查一次
  匹配: = 5  # 5秒

123/monitoring/position_monitor.py:217 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/monitoring/position_monitor.py:217 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/monitoring/position_monitor.py:217 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/monitoring/position_monitor.py:241 [timestamp_comparison]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/position_monitor.py:241 [timestamp_assignment]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/position_monitor.py:284 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/monitoring/position_monitor.py:284 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/monitoring/position_monitor.py:284 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/monitoring/position_monitor.py:308 [timestamp_comparison]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/position_monitor.py:308 [timestamp_assignment]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/position_monitor.py:414 [time_time]
  代码: self.last_update_time[f"{exchange_name}_spot"] = time.time()
  匹配: time.time()

123/monitoring/position_monitor.py:422 [time_time]
  代码: self.last_update_time[f"{exchange_name}_futures"] = time.time()
  匹配: time.time()

123/monitoring/position_monitor.py:468 [time_time]
  代码: 'timestamp': time.time()
  匹配: time.time()

123/monitoring/position_monitor.py:545 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/monitoring/position_monitor.py:545 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/monitoring/position_monitor.py:545 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/monitoring/position_monitor.py:545 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/monitoring/position_monitor.py:559 [time_time]
  代码: timestamp=int(time.time() * 1000)
  匹配: time.time()

123/monitoring/position_monitor.py:559 [timestamp_comparison]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/monitoring/position_monitor.py:559 [millisecond_conversion]
  代码: timestamp=int(time.time() * 1000)
  匹配: * 1000

123/monitoring/position_monitor.py:559 [timestamp_assignment]
  代码: timestamp=int(time.time() * 1000)
  匹配: timestamp=

123/monitoring/position_monitor.py:602 [time_time]
  代码: 'timestamp': time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:99 [timestamp_field]
  代码: "timestamp": self.timestamp,
  匹配: .timestamp

123/monitoring/performance_monitor.py:151 [timestamp_field]
  代码: "timestamp": self.timestamp,
  匹配: .timestamp

123/monitoring/performance_monitor.py:186 [time_comparison]
  代码: self.connection_check_interval = 30  # 连接检查间隔（秒）
  匹配: = 30  # 连接检查间隔（秒

123/monitoring/performance_monitor.py:190 [time_time]
  代码: self.start_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:195 [time_comparison]
  代码: self.cache_ttl = 30  # 缓存30秒
  匹配: = 30  # 缓存30秒

123/monitoring/performance_monitor.py:244 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/monitoring/performance_monitor.py:244 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/monitoring/performance_monitor.py:244 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/monitoring/performance_monitor.py:275 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:275 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/performance_monitor.py:332 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:332 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/performance_monitor.py:379 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:383 [time_time]
  代码: rest_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/monitoring/performance_monitor.py:383 [millisecond_conversion]
  代码: rest_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/monitoring/performance_monitor.py:397 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:420 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/monitoring/performance_monitor.py:480 [time_time]
  代码: if not hasattr(self, '_last_cpu_check') or time.time() - self._last_cpu_check > 1:
  匹配: time.time()

123/monitoring/performance_monitor.py:482 [time_time]
  代码: self._last_cpu_check = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:490 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:494 [time_time]
  代码: network_latency = (time.time() - start_time) * 1000
  匹配: time.time()

123/monitoring/performance_monitor.py:494 [millisecond_conversion]
  代码: network_latency = (time.time() - start_time) * 1000
  匹配: * 1000

123/monitoring/performance_monitor.py:555 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:555 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/performance_monitor.py:559 [timestamp_field]
  代码: recent_trades = [t for t in self.trade_records if t.timestamp >= one_minute_ago]
  匹配: .timestamp

123/monitoring/performance_monitor.py:559 [timestamp_comparison]
  代码: recent_trades = [t for t in self.trade_records if t.timestamp >= one_minute_ago]
  匹配: timestamp >

123/monitoring/performance_monitor.py:729 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:729 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/performance_monitor.py:740 [timestamp_comparison]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/performance_monitor.py:740 [timestamp_assignment]
  代码: timestamp=current_time,
  匹配: timestamp=

123/monitoring/performance_monitor.py:773 [time_time]
  代码: return PerformanceSnapshot(timestamp=time.time())
  匹配: time.time()

123/monitoring/performance_monitor.py:773 [timestamp_comparison]
  代码: return PerformanceSnapshot(timestamp=time.time())
  匹配: timestamp=

123/monitoring/performance_monitor.py:773 [timestamp_assignment]
  代码: return PerformanceSnapshot(timestamp=time.time())
  匹配: timestamp=

123/monitoring/performance_monitor.py:928 [time_time]
  代码: cutoff_time = time.time() - (24 * 3600)  # 保留24小时
  匹配: time.time()

123/monitoring/performance_monitor.py:947 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/monitoring/performance_monitor.py:951 [timestamp_field]
  代码: if snapshot.timestamp >= cutoff_time
  匹配: .timestamp

123/monitoring/performance_monitor.py:951 [timestamp_comparison]
  代码: if snapshot.timestamp >= cutoff_time
  匹配: timestamp >

123/monitoring/performance_monitor.py:962 [time_time]
  代码: running_hours = (time.time() - self.start_time) / 3600
  匹配: time.time()

123/monitoring/performance_monitor.py:984 [timestamp_field]
  代码: "last_update": self.last_snapshot.timestamp
  匹配: .timestamp

123/monitoring/performance_monitor.py:989 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/monitoring/performance_monitor.py:990 [timestamp_field]
  代码: recent_trades = [t for t in self.trade_records if t.timestamp >= cutoff_time]
  匹配: .timestamp

123/monitoring/performance_monitor.py:990 [timestamp_comparison]
  代码: recent_trades = [t for t in self.trade_records if t.timestamp >= cutoff_time]
  匹配: timestamp >

123/monitoring/performance_monitor.py:1043 [time_time]
  代码: "monitoring_duration_hours": (time.time() - self.start_time) / 3600,
  匹配: time.time()

123/monitoring/performance_monitor.py:1051 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/monitoring/performance_monitor.py:1051 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/monitoring/performance_monitor.py:1055 [time_time]
  代码: "export_time": time.time(),
  匹配: time.time()

123/monitoring/performance_monitor.py:1140 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/monitoring/performance_monitor.py:1144 [timestamp_field]
  代码: self.snapshots = [s for s in self.snapshots if s.timestamp >= cutoff_time]
  匹配: .timestamp

123/monitoring/performance_monitor.py:1144 [timestamp_comparison]
  代码: self.snapshots = [s for s in self.snapshots if s.timestamp >= cutoff_time]
  匹配: timestamp >

123/monitoring/performance_monitor.py:1148 [timestamp_field]
  代码: self.trade_records = [t for t in self.trade_records if t.timestamp >= cutoff_time]
  匹配: .timestamp

123/monitoring/performance_monitor.py:1148 [timestamp_comparison]
  代码: self.trade_records = [t for t in self.trade_records if t.timestamp >= cutoff_time]
  匹配: timestamp >

123/monitoring/performance_monitor.py:1166 [time_time]
  代码: timestamp = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:1166 [timestamp_comparison]
  代码: timestamp = time.time()
  匹配: timestamp =

123/monitoring/performance_monitor.py:1166 [timestamp_assignment]
  代码: timestamp = time.time()
  匹配: timestamp =

123/monitoring/performance_monitor.py:1191 [time_time]
  代码: timestamp = time.time()
  匹配: time.time()

123/monitoring/performance_monitor.py:1191 [timestamp_comparison]
  代码: timestamp = time.time()
  匹配: timestamp =

123/monitoring/performance_monitor.py:1191 [timestamp_assignment]
  代码: timestamp = time.time()
  匹配: timestamp =

123/monitoring/performance_monitor.py:1353 [timestamp_field]
  代码: assert snapshot.timestamp > 0, "Snapshot should have valid timestamp"
  匹配: .timestamp

123/monitoring/performance_monitor.py:1353 [timestamp_comparison]
  代码: assert snapshot.timestamp > 0, "Snapshot should have valid timestamp"
  匹配: timestamp >

123/monitoring/risk_monitor.py:64 [timestamp_field]
  代码: "timestamp": self.timestamp,
  匹配: .timestamp

123/monitoring/risk_monitor.py:111 [millisecond_conversion]
  代码: spread_risk = min(self.spread_volatility * 1000, 25)  # 波动率转换为风险分
  匹配: * 1000

123/monitoring/risk_monitor.py:165 [time_comparison]
  代码: self.check_interval = 10  # 10秒检查一次
  匹配: = 10  # 10秒

123/monitoring/risk_monitor.py:287 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:287 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/risk_monitor.py:409 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:409 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:427 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:427 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:450 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:450 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/risk_monitor.py:470 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:470 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:511 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:511 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:535 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:535 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:547 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:547 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/risk_monitor.py:639 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:639 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:659 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:659 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:677 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:677 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:689 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:689 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/risk_monitor.py:700 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:713 [time_time]
  代码: latency = (time.time() - start_time) * 1000
  匹配: time.time()

123/monitoring/risk_monitor.py:713 [millisecond_conversion]
  代码: latency = (time.time() - start_time) * 1000
  匹配: * 1000

123/monitoring/risk_monitor.py:752 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:752 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:770 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:770 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:782 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:782 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/monitoring/risk_monitor.py:812 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:812 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:844 [timestamp_comparison]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:844 [timestamp_assignment]
  代码: timestamp=current_time
  匹配: timestamp=

123/monitoring/risk_monitor.py:878 [time_time]
  代码: self.current_metrics.timestamp = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:878 [timestamp_field]
  代码: self.current_metrics.timestamp = time.time()
  匹配: .timestamp

123/monitoring/risk_monitor.py:878 [timestamp_comparison]
  代码: self.current_metrics.timestamp = time.time()
  匹配: timestamp =

123/monitoring/risk_monitor.py:878 [timestamp_assignment]
  代码: self.current_metrics.timestamp = time.time()
  匹配: timestamp =

123/monitoring/risk_monitor.py:902 [timestamp_field]
  代码: abs(existing.timestamp - alert.timestamp) < 300):  # 5分钟内的相同告警
  匹配: .timestamp

123/monitoring/risk_monitor.py:902 [timestamp_field]
  代码: abs(existing.timestamp - alert.timestamp) < 300):  # 5分钟内的相同告警
  匹配: .timestamp

123/monitoring/risk_monitor.py:908 [timestamp_field]
  代码: existing_alert.timestamp = alert.timestamp
  匹配: .timestamp

123/monitoring/risk_monitor.py:908 [timestamp_field]
  代码: existing_alert.timestamp = alert.timestamp
  匹配: .timestamp

123/monitoring/risk_monitor.py:908 [timestamp_comparison]
  代码: existing_alert.timestamp = alert.timestamp
  匹配: timestamp =

123/monitoring/risk_monitor.py:908 [timestamp_assignment]
  代码: existing_alert.timestamp = alert.timestamp
  匹配: timestamp =

123/monitoring/risk_monitor.py:952 [time_time]
  代码: self.risk_alerts[alert_id].resolved_time = time.time()
  匹配: time.time()

123/monitoring/risk_monitor.py:1021 [timestamp_field]
  代码: "last_check_time": self.current_metrics.timestamp,
  匹配: .timestamp

123/monitoring/risk_monitor.py:1064 [timestamp_field]
  代码: "timestamp": self.current_metrics.timestamp
  匹配: .timestamp

123/monitoring/risk_monitor.py:1078 [timestamp_field]
  代码: "last_check_time": self.current_metrics.timestamp
  匹配: .timestamp

123/monitoring/risk_monitor.py:1083 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/monitoring/risk_monitor.py:1087 [timestamp_field]
  代码: if metrics.timestamp >= cutoff_time
  匹配: .timestamp

123/monitoring/risk_monitor.py:1087 [timestamp_comparison]
  代码: if metrics.timestamp >= cutoff_time
  匹配: timestamp >

123/monitoring/risk_monitor.py:1091 [timestamp_field]
  代码: "timestamp": metrics.timestamp,
  匹配: .timestamp

123/monitoring/risk_monitor.py:1103 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/monitoring/risk_monitor.py:1103 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/monitoring/risk_monitor.py:1107 [time_time]
  代码: "export_time": time.time(),
  匹配: time.time()

123/monitoring/risk_monitor.py:1128 [time_time]
  代码: cutoff_time = time.time() - (hours * 3600)
  匹配: time.time()

123/diagnostic_scripts/unified_fix_solution.py:234 [millisecond_conversion]
  代码: 'timestamp': int(asyncio.get_event_loop().time() * 1000),
  匹配: * 1000

123/diagnostic_scripts/unified_fix_solution.py:284 [millisecond_conversion]
  代码: execution_time = (asyncio.get_event_loop().time() - execution_start) * 1000
  匹配: * 1000

123/diagnostic_scripts/unified_fix_solution.py:307 [millisecond_conversion]
  代码: execution_time = (asyncio.get_event_loop().time() - execution_start) * 1000
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:75 [time_time]
  代码: current_time_float = time.time()
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:76 [millisecond_conversion]
  代码: current_time_ms = int(current_time_float * 1000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:77 [millisecond_conversion]
  代码: current_time_us = int(current_time_float * 1000000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:78 [millisecond_conversion]
  代码: current_time_ns = int(current_time_float * 1000000000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:110 [time_time]
  代码: "example_value": time.time(),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:116 [time_time]
  代码: "example_value": int(time.time() * 1000),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:116 [millisecond_conversion]
  代码: "example_value": int(time.time() * 1000),
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:122 [time_time]
  代码: "example_value": int(time.time() * 1000),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:122 [millisecond_conversion]
  代码: "example_value": int(time.time() * 1000),
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:132 [time_time]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:132 [millisecond_conversion]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:135 [timestamp_comparison]
  代码: potential_seconds_timestamp = suspicious_diff / 1000  # 143.552秒
  匹配: timestamp =

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:135 [second_conversion]
  代码: potential_seconds_timestamp = suspicious_diff / 1000  # 143.552秒
  匹配: / 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:135 [timestamp_assignment]
  代码: potential_seconds_timestamp = suspicious_diff / 1000  # 143.552秒
  匹配: timestamp =

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:139 [second_conversion]
  代码: "as_seconds": suspicious_diff / 1000,
  匹配: / 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:145 [second_conversion]
  代码: if 60 < suspicious_diff / 1000 < 300:  # 1-5分钟范围
  匹配: / 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:156 [second_conversion]
  代码: print(f"\n🚨 问题分析: 143552.0ms ≈ {suspicious_diff/1000:.1f}秒 ≈ {suspicious_diff/60000:.1f}分钟")
  匹配: /1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:193 [time_time]
  代码: current_time_ms = time.time() * 1000
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:193 [millisecond_conversion]
  代码: current_time_ms = time.time() * 1000
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:199 [timestamp_field]
  代码: timestamp = data.timestamp
  匹配: .timestamp

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:199 [timestamp_comparison]
  代码: timestamp = data.timestamp
  匹配: timestamp =

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:199 [timestamp_assignment]
  代码: timestamp = data.timestamp
  匹配: timestamp =

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:201 [timestamp_comparison]
  代码: if timestamp < 1e12:
  匹配: timestamp <

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:202 [millisecond_conversion]
  代码: timestamp_ms = timestamp * 1000
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:209 [timestamp_field]
  代码: "timestamp_raw": data.timestamp,
  匹配: .timestamp

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:213 [second_conversion]
  代码: "readable_time": datetime.fromtimestamp(timestamp_ms/1000).strftime('%H:%M:%S.%f')[:-3]
  匹配: /1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:272 [time_time]
  代码: ("当前秒级时间戳", time.time()),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:273 [time_time]
  代码: ("当前毫秒级时间戳", time.time() * 1000),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:273 [millisecond_conversion]
  代码: ("当前毫秒级时间戳", time.time() * 1000),
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:274 [time_time]
  代码: ("过去的秒级时间戳", time.time() - 143.552),  # 143.552秒前
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:275 [time_time]
  代码: ("过去的毫秒级时间戳", (time.time() - 143.552) * 1000),
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:275 [millisecond_conversion]
  代码: ("过去的毫秒级时间戳", (time.time() - 143.552) * 1000),
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:276 [time_time]
  代码: ("异常大的时间戳", time.time() * 1000000),  # 微秒级
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:276 [millisecond_conversion]
  代码: ("异常大的时间戳", time.time() * 1000000),  # 微秒级
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:280 [time_time]
  代码: current_time_ms = time.time() * 1000
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:280 [millisecond_conversion]
  代码: current_time_ms = time.time() * 1000
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:292 [timestamp_comparison]
  代码: "readable_original": datetime.fromtimestamp(timestamp if timestamp < 1e12 else timestamp/1000).strftime('%H:%M:%S.%f')[:-3],
  匹配: timestamp <

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:292 [second_conversion]
  代码: "readable_original": datetime.fromtimestamp(timestamp if timestamp < 1e12 else timestamp/1000).strftime('%H:%M:%S.%f')[:-3],
  匹配: /1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:293 [second_conversion]
  代码: "readable_normalized": datetime.fromtimestamp(normalized/1000).strftime('%H:%M:%S.%f')[:-3]
  匹配: /1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:333 [second_conversion]
  代码: "as_seconds": target_diff / 1000,
  匹配: / 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:340 [second_conversion]
  代码: seconds = target_diff / 1000
  匹配: / 1000

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:367 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:367 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:414 [time_time]
  代码: current_time_ms = time.time() * 1000
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_precise_diagnosis.py:414 [millisecond_conversion]
  代码: current_time_ms = time.time() * 1000
  匹配: * 1000

123/diagnostic_scripts/rule_comparison.py:38 [timestamp_field]
  代码: print(f"  timestamp: {rule.timestamp}")
  匹配: .timestamp

123/diagnostic_scripts/websocket_blockage_diagnosis.py:367 [timestamp_comparison]
  代码: last_timestamp = None
  匹配: timestamp =

123/diagnostic_scripts/websocket_blockage_diagnosis.py:367 [timestamp_assignment]
  代码: last_timestamp = None
  匹配: timestamp =

123/diagnostic_scripts/websocket_blockage_diagnosis.py:373 [timestamp_comparison]
  代码: last_timestamp = part
  匹配: timestamp =

123/diagnostic_scripts/websocket_blockage_diagnosis.py:373 [timestamp_assignment]
  代码: last_timestamp = part
  匹配: timestamp =

123/diagnostic_scripts/websocket_blockage_diagnosis.py:443 [time_time]
  代码: report_file = DIAGNOSIS_DIR / f"websocket_blockage_diagnosis_{int(time.time())}.json"
  匹配: time.time()

123/diagnostic_scripts/async_precision_diagnosis.py:33 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/diagnostic_scripts/async_precision_diagnosis.py:52 [time_time]
  代码: "timestamp": time.time()
  匹配: time.time()

123/diagnostic_scripts/async_precision_diagnosis.py:250 [time_time]
  代码: 'timestamp': int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/async_precision_diagnosis.py:250 [millisecond_conversion]
  代码: 'timestamp': int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/async_precision_diagnosis.py:256 [time_time]
  代码: 'timestamp': int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/async_precision_diagnosis.py:256 [millisecond_conversion]
  代码: 'timestamp': int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/async_precision_diagnosis.py:351 [second_conversion]
  代码: "average_error": float(accumulated_error / 1000)
  匹配: / 1000

123/diagnostic_scripts/async_precision_diagnosis.py:392 [time_time]
  代码: f"async_precision_diagnosis_{int(time.time())}.json"
  匹配: time.time()

123/diagnostic_scripts/precise_error_analysis.py:367 [time_time]
  代码: "analysis_time": time.time(),
  匹配: time.time()

123/diagnostic_scripts/simple_websocket_diagnosis.py:147 [time_time]
  代码: result_file = f"diagnostic_results/simple_diagnosis_{int(time.time())}.json"
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_diagnosis.py:57 [time_time]
  代码: diagnosis_start = time.time()
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_diagnosis.py:75 [time_time]
  代码: diagnosis_duration = time.time() - diagnosis_start
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_diagnosis.py:202 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_diagnosis.py:202 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/diagnostic_scripts/orderbook_sync_diagnosis.py:202 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_diagnosis.py:373 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/diagnostic_scripts/orderbook_sync_diagnosis.py:373 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
  匹配: timestamp =

123/diagnostic_scripts/exchange_consistency_diagnosis.py:31 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/diagnostic_scripts/exchange_consistency_diagnosis.py:58 [time_time]
  代码: "timestamp": time.time()
  匹配: time.time()

123/diagnostic_scripts/exchange_consistency_diagnosis.py:335 [timestamp_comparison]
  代码: timestamp=data["timestamp"]
  匹配: timestamp=

123/diagnostic_scripts/exchange_consistency_diagnosis.py:335 [timestamp_assignment]
  代码: timestamp=data["timestamp"]
  匹配: timestamp=

123/diagnostic_scripts/exchange_consistency_diagnosis.py:473 [time_time]
  代码: f"exchange_consistency_report_{int(time.time())}.json"
  匹配: time.time()

123/diagnostic_scripts/test_fix.py:41 [timestamp_field]
  代码: print(f"  timestamp: {rule.timestamp}")
  匹配: .timestamp

123/diagnostic_scripts/orderbook_sync_fix.py:61 [time_time]
  代码: fix_start = time.time()
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_fix.py:81 [time_time]
  代码: fix_duration = time.time() - fix_start
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_fix.py:240 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_fix.py:240 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/diagnostic_scripts/orderbook_sync_fix.py:240 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/orderbook_sync_fix.py:384 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/diagnostic_scripts/orderbook_sync_fix.py:384 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/diagnostic_scripts/orderbook_sync_fix.py:384 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/diagnostic_scripts/simple_diagnosis.py:27 [time_time]
  代码: "timestamp": time.time()
  匹配: time.time()

123/diagnostic_scripts/simple_diagnosis.py:119 [time_time]
  代码: f"simple_diagnosis_{int(time.time())}.json"
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:161 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:161 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:186 [time_time]
  代码: connection_id = f"{exchange}_{market_type}_{int(time.time())}"
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:207 [time_time]
  代码: setattr(self, f"_last_create_{exchange}_{market_type}", time.time())
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:228 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:228 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:347 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:347 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:384 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:384 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:404 [current_time]
  代码: current_time = time.localtime()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:422 [time_time]
  代码: wait_hours = (next_restart_time - time.time()) / 3600
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:457 [timestamp_field]
  代码: return next_restart.timestamp()
  匹配: .timestamp

123/websocket/unified_connection_pool_manager.py:481 [time_time]
  代码: connection.created_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:586 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:586 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:612 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:612 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:872 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_connection_pool_manager.py:872 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_connection_pool_manager.py:924 [time_time]
  代码: self.data_flow_monitor[connection_id][symbol] = time.time()
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:20 [time_comparison]
  代码: sync_interval_seconds: int = 20   # 🔥 修复：按要求缩短到20秒
  匹配: = 20   # 🔥 修复：按要求缩短到20秒

123/websocket/unified_timestamp_processor.py:22 [time_comparison]
  代码: sync_timeout_seconds: int = 5    # 🔥 修复：同步请求超时5秒
  匹配: = 5    # 🔥 修复：同步请求超时5秒

123/websocket/unified_timestamp_processor.py:26 [time_comparison]
  代码: retry_interval_seconds: int = 1  # 🔥 修复：缩短重试间隔到1秒，提高响应速度
  匹配: = 1  # 🔥 修复：缩短重试间隔到1秒

123/websocket/unified_timestamp_processor.py:41 [time_comparison]
  代码: self.time_offset = 0  # 时间偏移量(毫秒)
  匹配: = 0  # 时间偏移量(毫秒

123/websocket/unified_timestamp_processor.py:70 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:70 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/unified_timestamp_processor.py:93 [time_time]
  代码: local_time = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:93 [millisecond_conversion]
  代码: local_time = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:194 [second_conversion]
  代码: return int(result["timeNano"]) // 1000000
  匹配: / 1000

123/websocket/unified_timestamp_processor.py:201 [millisecond_conversion]
  代码: return int(result["timeSecond"]) * 1000
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:239 [timestamp_comparison]
  代码: server_timestamp = self._extract_server_timestamp_for_monitoring(data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:239 [timestamp_assignment]
  代码: server_timestamp = self._extract_server_timestamp_for_monitoring(data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:242 [timestamp_comparison]
  代码: normalized_timestamp = self._normalize_timestamp_format(server_timestamp)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:242 [timestamp_assignment]
  代码: normalized_timestamp = self._normalize_timestamp_format(server_timestamp)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:245 [time_time]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:245 [millisecond_conversion]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:246 [time_diff]
  代码: time_diff = abs(normalized_timestamp - current_time_ms)
  匹配: time_diff =

123/websocket/unified_timestamp_processor.py:250 [time_comparison]
  代码: max_age_ms = 2000  # 最大允许2秒的数据年龄，严格控制过期时间戳
  匹配: = 2000  # 最大允许2秒

123/websocket/unified_timestamp_processor.py:263 [timestamp_comparison]
  代码: discarded_timestamp=normalized_timestamp)
  匹配: timestamp=

123/websocket/unified_timestamp_processor.py:263 [timestamp_assignment]
  代码: discarded_timestamp=normalized_timestamp)
  匹配: timestamp=

123/websocket/unified_timestamp_processor.py:268 [time_time]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:268 [millisecond_conversion]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:271 [timestamp_comparison]
  代码: aligned_timestamp = self._align_timestamp_to_global_base(current_time_ms)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:271 [timestamp_assignment]
  代码: aligned_timestamp = self._align_timestamp_to_global_base(current_time_ms)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:278 [timestamp_comparison]
  代码: final_timestamp = aligned_timestamp + self.time_offset
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:278 [timestamp_assignment]
  代码: final_timestamp = aligned_timestamp + self.time_offset
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:309 [time_time]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:309 [millisecond_conversion]
  代码: current_time_ms = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:327 [timestamp_comparison]
  代码: if timestamp < 1e10:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:328 [millisecond_conversion]
  代码: return int(timestamp * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:330 [timestamp_comparison]
  代码: elif timestamp < 1e13:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:333 [timestamp_comparison]
  代码: elif timestamp < 1e16:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:334 [second_conversion]
  代码: return int(timestamp / 1000)  # 微秒转毫秒
  匹配: / 1000

123/websocket/unified_timestamp_processor.py:336 [second_conversion]
  代码: return int(timestamp / 1000000)  # 纳秒转毫秒
  匹配: / 1000

123/websocket/unified_timestamp_processor.py:341 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:341 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:353 [timestamp_comparison]
  代码: extracted_timestamp = None
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:353 [timestamp_assignment]
  代码: extracted_timestamp = None
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:360 [timestamp_comparison]
  代码: extracted_timestamp = float(data['time_ms'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:360 [timestamp_assignment]
  代码: extracted_timestamp = float(data['time_ms'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:363 [timestamp_comparison]
  代码: extracted_timestamp = float(data['t'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:363 [timestamp_assignment]
  代码: extracted_timestamp = float(data['t'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:366 [timestamp_comparison]
  代码: extracted_timestamp = float(data['create_time_ms'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:366 [timestamp_assignment]
  代码: extracted_timestamp = float(data['create_time_ms'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:369 [timestamp_comparison]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:369 [timestamp_assignment]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:370 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:370 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:370 [millisecond_conversion]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:370 [timestamp_assignment]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:376 [timestamp_comparison]
  代码: extracted_timestamp = float(data['ts'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:376 [timestamp_assignment]
  代码: extracted_timestamp = float(data['ts'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:379 [timestamp_comparison]
  代码: extracted_timestamp = float(data['cts'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:379 [timestamp_assignment]
  代码: extracted_timestamp = float(data['cts'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:382 [timestamp_comparison]
  代码: extracted_timestamp = float(data['T'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:382 [timestamp_assignment]
  代码: extracted_timestamp = float(data['T'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:385 [timestamp_comparison]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:385 [timestamp_assignment]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:386 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:386 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:386 [millisecond_conversion]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:386 [timestamp_assignment]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:395 [timestamp_comparison]
  代码: extracted_timestamp = float(ts_value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:395 [timestamp_assignment]
  代码: extracted_timestamp = float(ts_value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:397 [timestamp_comparison]
  代码: extracted_timestamp = float(ts_value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:397 [timestamp_assignment]
  代码: extracted_timestamp = float(ts_value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:400 [timestamp_comparison]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:400 [timestamp_assignment]
  代码: timestamp = float(data['timestamp'])
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:401 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:401 [timestamp_comparison]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:401 [millisecond_conversion]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:401 [timestamp_assignment]
  代码: extracted_timestamp = timestamp * 1000 if timestamp < 1e12 else timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:410 [timestamp_comparison]
  代码: nested_timestamp = self._extract_server_timestamp_for_monitoring(nested_data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:410 [timestamp_assignment]
  代码: nested_timestamp = self._extract_server_timestamp_for_monitoring(nested_data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:412 [timestamp_comparison]
  代码: extracted_timestamp = nested_timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:412 [timestamp_assignment]
  代码: extracted_timestamp = nested_timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:418 [timestamp_comparison]
  代码: result_timestamp = self._extract_server_timestamp_for_monitoring(result_data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:418 [timestamp_assignment]
  代码: result_timestamp = self._extract_server_timestamp_for_monitoring(result_data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:420 [timestamp_comparison]
  代码: extracted_timestamp = result_timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:420 [timestamp_assignment]
  代码: extracted_timestamp = result_timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:430 [timestamp_comparison]
  代码: timestamp = float(value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:430 [timestamp_assignment]
  代码: timestamp = float(value)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:432 [timestamp_comparison]
  代码: if timestamp < 1e12:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:434 [timestamp_comparison]
  代码: extracted_timestamp = timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:434 [timestamp_assignment]
  代码: extracted_timestamp = timestamp
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:442 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:442 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/websocket/unified_timestamp_processor.py:442 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:443 [time_diff]
  代码: time_diff = abs(extracted_timestamp - current_time)
  匹配: time_diff =

123/websocket/unified_timestamp_processor.py:446 [time_comparison]
  代码: max_age_ms = 2000  # 最大允许2秒的数据年龄，严格阈值
  匹配: = 2000  # 最大允许2秒

123/websocket/unified_timestamp_processor.py:457 [timestamp_comparison]
  代码: discarded_timestamp=extracted_timestamp)
  匹配: timestamp=

123/websocket/unified_timestamp_processor.py:457 [timestamp_assignment]
  代码: discarded_timestamp=extracted_timestamp)
  匹配: timestamp=

123/websocket/unified_timestamp_processor.py:486 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:486 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/websocket/unified_timestamp_processor.py:486 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:489 [timestamp_comparison]
  代码: if timestamp < 1e12:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:492 [potential_unit_mismatch]
  代码: age_ms = abs(current_time - timestamp)
  匹配: 可能的单位不一致

123/websocket/unified_timestamp_processor.py:508 [time_time]
  代码: "sync_age_seconds": time.time() - self.last_sync_time if self.last_sync_time > 0 else -1,
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:549 [timestamp_comparison]
  代码: corrected_timestamp = max(aligned_timestamp1, aligned_timestamp2)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:549 [timestamp_assignment]
  代码: corrected_timestamp = max(aligned_timestamp1, aligned_timestamp2)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:550 [time_time]
  代码: current_time = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:550 [current_time]
  代码: current_time = int(time.time() * 1000)
  匹配: current_time =

123/websocket/unified_timestamp_processor.py:550 [millisecond_conversion]
  代码: current_time = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:563 [potential_unit_mismatch]
  代码: time_diff_ms = abs(current_time - min(aligned_timestamp1, aligned_timestamp2))
  匹配: 可能的单位不一致

123/websocket/unified_timestamp_processor.py:600 [timestamp_comparison]
  代码: if timestamp < 1e12:
  匹配: timestamp <

123/websocket/unified_timestamp_processor.py:605 [timestamp_comparison]
  代码: aligned_timestamp = (timestamp // 10) * 10
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:605 [timestamp_assignment]
  代码: aligned_timestamp = (timestamp // 10) * 10
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:736 [timestamp_comparison]
  代码: raw_timestamp = processor.get_synced_timestamp(data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:736 [timestamp_assignment]
  代码: raw_timestamp = processor.get_synced_timestamp(data)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:739 [timestamp_comparison]
  代码: aligned_timestamp = processor._align_timestamp_to_global_base(raw_timestamp)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:739 [timestamp_assignment]
  代码: aligned_timestamp = processor._align_timestamp_to_global_base(raw_timestamp)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:746 [time_time]
  代码: raw_timestamp = int(time.time() * 1000)
  匹配: time.time()

123/websocket/unified_timestamp_processor.py:746 [timestamp_comparison]
  代码: raw_timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/websocket/unified_timestamp_processor.py:746 [millisecond_conversion]
  代码: raw_timestamp = int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_timestamp_processor.py:746 [timestamp_assignment]
  代码: raw_timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/websocket/orderbook_validator.py:297 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/websocket/orderbook_validator.py:297 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/websocket/orderbook_validator.py:297 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/websocket/orderbook_validator.py:300 [timestamp_comparison]
  代码: spot_timestamp = spot_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/websocket/orderbook_validator.py:300 [timestamp_assignment]
  代码: spot_timestamp = spot_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/websocket/orderbook_validator.py:301 [timestamp_comparison]
  代码: futures_timestamp = futures_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/websocket/orderbook_validator.py:301 [timestamp_assignment]
  代码: futures_timestamp = futures_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/websocket/orderbook_validator.py:304 [timestamp_comparison]
  代码: spot_timestamp = _normalize_timestamp_format(spot_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:304 [timestamp_assignment]
  代码: spot_timestamp = _normalize_timestamp_format(spot_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:305 [timestamp_comparison]
  代码: futures_timestamp = _normalize_timestamp_format(futures_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:305 [timestamp_assignment]
  代码: futures_timestamp = _normalize_timestamp_format(futures_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:317 [timestamp_comparison]
  代码: corrected_timestamp = max(spot_timestamp, futures_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:317 [timestamp_assignment]
  代码: corrected_timestamp = max(spot_timestamp, futures_timestamp)
  匹配: timestamp =

123/websocket/orderbook_validator.py:318 [time_time]
  代码: current_check_time = int(time.time() * 1000)
  匹配: time.time()

123/websocket/orderbook_validator.py:318 [millisecond_conversion]
  代码: current_check_time = int(time.time() * 1000)
  匹配: * 1000

123/websocket/orderbook_validator.py:358 [potential_unit_mismatch]
  代码: data_age_spot = current_time - spot_timestamp
  匹配: 可能的单位不一致

123/websocket/orderbook_validator.py:359 [potential_unit_mismatch]
  代码: data_age_futures = current_time - futures_timestamp
  匹配: 可能的单位不一致

123/websocket/orderbook_validator.py:362 [data_age]
  代码: max_data_age = _calculate_adaptive_data_age_threshold(spot_orderbook, futures_orderbook)
  匹配: data_age =

123/websocket/orderbook_validator.py:432 [timestamp_comparison]
  代码: if timestamp < 1e10:
  匹配: timestamp <

123/websocket/orderbook_validator.py:433 [millisecond_conversion]
  代码: return int(timestamp * 1000)
  匹配: * 1000

123/websocket/orderbook_validator.py:435 [timestamp_comparison]
  代码: elif timestamp < 1e13:
  匹配: timestamp <

123/websocket/orderbook_validator.py:438 [timestamp_comparison]
  代码: elif timestamp < 1e16:
  匹配: timestamp <

123/websocket/orderbook_validator.py:439 [second_conversion]
  代码: return int(timestamp / 1000)  # 微秒转毫秒
  匹配: / 1000

123/websocket/orderbook_validator.py:441 [second_conversion]
  代码: return int(timestamp / 1000000)  # 纳秒转毫秒
  匹配: / 1000

123/websocket/orderbook_validator.py:445 [time_time]
  代码: return int(time.time() * 1000)
  匹配: time.time()

123/websocket/orderbook_validator.py:445 [millisecond_conversion]
  代码: return int(time.time() * 1000)
  匹配: * 1000

123/websocket/unified_data_formatter.py:76 [timestamp_comparison]
  代码: timestamp = get_synced_timestamp(exchange, None)
  匹配: timestamp =

123/websocket/unified_data_formatter.py:76 [timestamp_assignment]
  代码: timestamp = get_synced_timestamp(exchange, None)
  匹配: timestamp =

123/websocket/unified_data_formatter.py:126 [millisecond_conversion]
  代码: result["spread_bps"] = (spread / best_bid) * 10000  # 基点
  匹配: * 1000

123/websocket/error_handler.py:162 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/websocket/error_handler.py:162 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/websocket/error_handler.py:162 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/websocket/error_handler.py:318 [time_time]
  代码: recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
  匹配: time.time()

123/websocket/error_handler.py:318 [timestamp_field]
  代码: recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
  匹配: .timestamp

123/websocket/error_handler.py:318 [timestamp_comparison]
  代码: recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
  匹配: timestamp <

123/websocket/error_handler.py:318 [potential_unit_mismatch]
  代码: recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
  匹配: 可能的单位不一致

123/websocket/ws_manager.py:408 [time_time]
  代码: self.stats["last_orderbook_time"][key] = time.time()
  匹配: time.time()

123/websocket/ws_manager.py:487 [time_time]
  代码: 'timestamp': time.time()
  匹配: time.time()

123/websocket/ws_manager.py:777 [time_time]
  代码: last_health_check = time.time()
  匹配: time.time()

123/websocket/ws_manager.py:778 [time_comparison]
  代码: health_check_interval = 60  # 每60秒进行一次全面健康检查
  匹配: = 60  # 每60秒

123/websocket/ws_manager.py:788 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/ws_manager.py:788 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/ws_manager.py:931 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/ws_manager.py:931 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/gate_ws.py:43 [time_comparison]
  代码: self.time_offset = 0  # 时间偏移（毫秒）
  匹配: = 0  # 时间偏移（毫秒

123/websocket/gate_ws.py:61 [time_comparison]
  代码: self.connection_timeout = 10  # 🔥 修复：连接超时统一为10秒，与Bybit、OKX保持一致
  匹配: = 10  # 🔥 修复：连接超时统一为10秒

123/websocket/gate_ws.py:62 [time_comparison]
  代码: self.heartbeat_interval = 5  # 🔥 官方文档修复：Gate.io官方支持5秒心跳间隔
  匹配: = 5  # 🔥 官方文档修复：Gate.io官方支持5秒

123/websocket/gate_ws.py:163 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/websocket/gate_ws.py:171 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/websocket/gate_ws.py:215 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/gate_ws.py:215 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/gate_ws.py:246 [time_time]
  代码: pong_msg = {"event": "pong", "time": int(time.time())}
  匹配: time.time()

123/websocket/gate_ws.py:317 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/websocket/gate_ws.py:331 [timestamp_comparison]
  代码: timestamp = get_synced_timestamp("gate", data)
  匹配: timestamp =

123/websocket/gate_ws.py:331 [timestamp_assignment]
  代码: timestamp = get_synced_timestamp("gate", data)
  匹配: timestamp =

123/websocket/gate_ws.py:526 [timestamp_comparison]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/gate_ws.py:526 [timestamp_assignment]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/gate_ws.py:559 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/websocket/gate_ws.py:567 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/websocket/gate_ws.py:594 [time_time]
  代码: "timestamp": trade.get("create_time_ms", trade.get("timestamp", trade.get("t", int(time.time() * 1000)))),
  匹配: time.time()

123/websocket/gate_ws.py:594 [millisecond_conversion]
  代码: "timestamp": trade.get("create_time_ms", trade.get("timestamp", trade.get("t", int(time.time() * 1000)))),
  匹配: * 1000

123/websocket/gate_ws.py:608 [time_time]
  代码: "time": int(time.time()),
  匹配: time.time()

123/websocket/gate_ws.py:614 [time_time]
  代码: self.last_message_time = time.time()  # 更新最后消息时间
  匹配: time.time()

123/websocket/bybit_ws.py:45 [time_comparison]
  代码: self.time_offset = 0  # 时间偏移（毫秒）
  匹配: = 0  # 时间偏移（毫秒

123/websocket/bybit_ws.py:83 [time_comparison]
  代码: self.connection_timeout = 10  # 🔥 修复：连接超时统一为10秒，与Gate.io、OKX保持一致
  匹配: = 10  # 🔥 修复：连接超时统一为10秒

123/websocket/bybit_ws.py:85 [time_time]
  代码: self.last_stats_time = time.time()
  匹配: time.time()

123/websocket/bybit_ws.py:257 [time_time]
  代码: #     current_time = time.time()
  匹配: time.time()

123/websocket/bybit_ws.py:257 [current_time]
  代码: #     current_time = time.time()
  匹配: current_time =

123/websocket/bybit_ws.py:309 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/websocket/bybit_ws.py:458 [timestamp_comparison]
  代码: timestamp = get_synced_timestamp("bybit", data)
  匹配: timestamp =

123/websocket/bybit_ws.py:458 [timestamp_assignment]
  代码: timestamp = get_synced_timestamp("bybit", data)
  匹配: timestamp =

123/websocket/bybit_ws.py:466 [timestamp_comparison]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/bybit_ws.py:466 [timestamp_assignment]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/bybit_ws.py:500 [time_time]
  代码: timestamp = trade.get("T", int(time.time() * 1000))
  匹配: time.time()

123/websocket/bybit_ws.py:500 [timestamp_comparison]
  代码: timestamp = trade.get("T", int(time.time() * 1000))
  匹配: timestamp =

123/websocket/bybit_ws.py:500 [millisecond_conversion]
  代码: timestamp = trade.get("T", int(time.time() * 1000))
  匹配: * 1000

123/websocket/bybit_ws.py:500 [timestamp_assignment]
  代码: timestamp = trade.get("T", int(time.time() * 1000))
  匹配: timestamp =

123/websocket/bybit_ws.py:516 [time_time]
  代码: "timestamp": int(time.time() * 1000),
  匹配: time.time()

123/websocket/bybit_ws.py:516 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000),
  匹配: * 1000

123/websocket/bybit_ws.py:550 [timestamp_comparison]
  代码: timestamp = kline.get("start", 0)
  匹配: timestamp =

123/websocket/bybit_ws.py:550 [timestamp_assignment]
  代码: timestamp = kline.get("start", 0)
  匹配: timestamp =

123/websocket/bybit_ws.py:570 [time_time]
  代码: "timestamp": int(time.time() * 1000),
  匹配: time.time()

123/websocket/bybit_ws.py:570 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000),
  匹配: * 1000

123/websocket/bybit_ws.py:600 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/bybit_ws.py:600 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/bybit_ws.py:612 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/bybit_ws.py:612 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/performance_monitor.py:57 [time_time]
  代码: self.start_time = time.time()
  匹配: time.time()

123/websocket/performance_monitor.py:58 [time_time]
  代码: self.last_throughput_check = time.time()
  匹配: time.time()

123/websocket/performance_monitor.py:91 [time_time]
  代码: end_time = time.time()
  匹配: time.time()

123/websocket/performance_monitor.py:93 [millisecond_conversion]
  代码: latency_ms = (end_time - start_time) * 1000
  匹配: * 1000

123/websocket/performance_monitor.py:118 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/websocket/performance_monitor.py:138 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/websocket/performance_monitor.py:226 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/performance_monitor.py:226 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/performance_monitor.py:227 [time_diff]
  代码: time_diff = current_time - self.last_throughput_check
  匹配: time_diff =

123/websocket/performance_monitor.py:257 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/performance_monitor.py:257 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/performance_monitor.py:263 [potential_unit_mismatch]
  代码: if current_time - error["timestamp"] <= 3600:  # 1小时
  匹配: 可能的单位不一致

123/websocket/performance_monitor.py:293 [time_time]
  代码: total_time = time.time() - self.start_time
  匹配: time.time()

123/websocket/performance_monitor.py:310 [time_time]
  代码: downtime += time.time() - current_downtime_start
  匹配: time.time()

123/websocket/performance_monitor.py:346 [time_time]
  代码: "timestamp": int(time.time() * 1000),
  匹配: time.time()

123/websocket/performance_monitor.py:346 [millisecond_conversion]
  代码: "timestamp": int(time.time() * 1000),
  匹配: * 1000

123/websocket/enhanced_ws_client_base.py:119 [time_time]
  代码: self.metrics["connection_start_time"] = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:139 [time_time]
  代码: self.error_timestamps.append(time.time())
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:144 [time_time]
  代码: "timestamp": time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:162 [time_time]
  代码: self.metrics["last_message_time"] = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:170 [time_time]
  代码: "timestamp": time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:196 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:196 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/enhanced_ws_client_base.py:249 [time_time]
  代码: ping_time = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:253 [time_time]
  代码: pong_time = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:254 [millisecond_conversion]
  代码: latency_ms = (pong_time - ping_time) * 1000
  匹配: * 1000

123/websocket/enhanced_ws_client_base.py:269 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/websocket/enhanced_ws_client_base.py:269 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/websocket/ws_client.py:47 [time_time]
  代码: self.last_message_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:55 [second_conversion]
  代码: self.connect_warning_threshold = getattr(settings.system, 'ws_connect_timeout', 1.0) / 1000.0
  匹配: / 1000

123/websocket/ws_client.py:61 [time_comparison]
  代码: self.heartbeat_interval = 20  # 🔥 官方文档修复：Bybit/OKX官方建议20秒
  匹配: = 20  # 🔥 官方文档修复：Bybit/OKX官方建议20秒

123/websocket/ws_client.py:68 [time_time]
  代码: self.last_stats_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:121 [timestamp_comparison]
  代码: timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
  匹配: timestamp =

123/websocket/ws_client.py:121 [timestamp_assignment]
  代码: timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
  匹配: timestamp =

123/websocket/ws_client.py:226 [time_time]
  代码: now = time.time()
  匹配: time.time()

123/websocket/ws_client.py:253 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:269 [time_time]
  代码: self.connect_time = time.time() - start_time
  匹配: time.time()

123/websocket/ws_client.py:280 [time_time]
  代码: self.last_message_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:287 [time_comparison]
  代码: subscribe_timeout = 25  # 25秒订阅超时
  匹配: = 25  # 25秒

123/websocket/ws_client.py:289 [time_comparison]
  代码: subscribe_timeout = 15   # 其他交易所15秒
  匹配: = 15   # 其他交易所15秒

123/websocket/ws_client.py:312 [time_time]
  代码: self.connect_time = time.time() - start_time if 'start_time' in locals() else 0
  匹配: time.time()

123/websocket/ws_client.py:354 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:366 [time_time]
  代码: reconnect_time = time.time() - start_time
  匹配: time.time()

123/websocket/ws_client.py:375 [time_time]
  代码: self.last_message_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:388 [time_time]
  代码: self.last_message_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:392 [time_time]
  代码: if time.time() - self.last_stats_time > self.stats_interval:
  匹配: time.time()

123/websocket/ws_client.py:423 [time_time]
  代码: now = time.time()
  匹配: time.time()

123/websocket/ws_client.py:431 [time_time]
  代码: self.last_message_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:461 [time_time]
  代码: self.last_stats_time = time.time()
  匹配: time.time()

123/websocket/ws_client.py:462 [time_time]
  代码: self.last_message_time = time.time()  # 初始化为当前时间
  匹配: time.time()

123/websocket/ws_client.py:498 [time_time]
  代码: now = time.time()
  匹配: time.time()

123/websocket/ws_client.py:573 [time_time]
  代码: now = time.time()
  匹配: time.time()

123/websocket/okx_ws.py:44 [time_comparison]
  代码: self.time_offset = 0  # 时间偏移（秒）
  匹配: = 0  # 时间偏移（秒

123/websocket/okx_ws.py:269 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/websocket/okx_ws.py:355 [timestamp_comparison]
  代码: timestamp = int(book.get("ts", get_synced_timestamp("okx", book)))
  匹配: timestamp =

123/websocket/okx_ws.py:355 [timestamp_assignment]
  代码: timestamp = int(book.get("ts", get_synced_timestamp("okx", book)))
  匹配: timestamp =

123/websocket/okx_ws.py:371 [timestamp_comparison]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/okx_ws.py:371 [timestamp_assignment]
  代码: timestamp=timestamp
  匹配: timestamp=

123/websocket/okx_ws.py:401 [timestamp_comparison]
  代码: timestamp = int(trade_data.get("ts", get_synced_timestamp("okx", trade_data)))
  匹配: timestamp =

123/websocket/okx_ws.py:401 [timestamp_assignment]
  代码: timestamp = int(trade_data.get("ts", get_synced_timestamp("okx", trade_data)))
  匹配: timestamp =

123/websocket/okx_ws.py:434 [time_time]
  代码: self.last_message_time = time.time()  # 更新最后消息时间
  匹配: time.time()

123/fund_management/fund_manager.py:74 [time_time]
  代码: self.last_check_time = time.time()
  匹配: time.time()

123/fund_management/fund_manager.py:294 [time_time]
  代码: "last_updated": time.time(),
  匹配: time.time()

123/fund_management/fund_manager.py:301 [time_time]
  代码: "last_updated": time.time(),
  匹配: time.time()

123/fund_management/fund_manager.py:309 [time_time]
  代码: "last_updated": time.time(),
  匹配: time.time()

123/fund_management/fund_manager.py:683 [time_time]
  代码: restore_start = time.time()
  匹配: time.time()

123/fund_management/fund_manager.py:695 [time_time]
  代码: restore_time = (time.time() - restore_start) * 1000 if 'restore_start' in locals() else 0
  匹配: time.time()

123/fund_management/fund_manager.py:695 [millisecond_conversion]
  代码: restore_time = (time.time() - restore_start) * 1000 if 'restore_start' in locals() else 0
  匹配: * 1000

123/fund_management/fund_manager.py:736 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/fund_management/fund_transfer_service.py:64 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/fund_management/fund_transfer_service.py:144 [time_time]
  代码: time.time() - start_time
  匹配: time.time()

123/fund_management/fund_transfer_service.py:163 [time_time]
  代码: time.time() - start_time, f"验证异常: {verify_error}"
  匹配: time.time()

123/fund_management/fund_transfer_service.py:178 [time_time]
  代码: time.time() - start_time, f"余额验证失败: from={from_diff:.4f}, to={to_diff:.4f}"
  匹配: time.time()

123/fund_management/fund_transfer_service.py:187 [time_time]
  代码: time.time() - start_time, "API调用失败"
  匹配: time.time()

123/fund_management/fund_transfer_service.py:205 [time_time]
  代码: time.time() - start_time, str(e)
  匹配: time.time()

123/core/api_call_optimizer.py:98 [time_time]
  代码: return {"status": "success", "timestamp": time.time()}
  匹配: time.time()

123/core/api_call_optimizer.py:101 [time_time]
  代码: test_start = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:105 [time_time]
  代码: call_start = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:107 [time_time]
  代码: call_end = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:116 [time_time]
  代码: test_duration = time.time() - test_start
  匹配: time.time()

123/core/api_call_optimizer.py:143 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:143 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/api_call_optimizer.py:163 [time_time]
  代码: setattr(self, f"_{exchange_name}_last_call", time.time())
  匹配: time.time()

123/core/api_call_optimizer.py:205 [time_time]
  代码: batch_start = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:207 [time_time]
  代码: batch_duration = time.time() - batch_start
  匹配: time.time()

123/core/api_call_optimizer.py:291 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:291 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/api_call_optimizer.py:303 [time_time]
  代码: call_start_time = time.time()
  匹配: time.time()

123/core/api_call_optimizer.py:309 [time_time]
  代码: call_duration = time.time() - call_start_time
  匹配: time.time()

123/core/api_call_optimizer.py:316 [time_time]
  代码: call_duration = time.time() - call_start_time
  匹配: time.time()

123/core/unified_opening_manager.py:92 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/unified_opening_manager.py:130 [time_time]
  代码: preparation_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_opening_manager.py:130 [millisecond_conversion]
  代码: preparation_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_opening_manager.py:154 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/unified_opening_manager.py:166 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_opening_manager.py:166 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_opening_manager.py:181 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_opening_manager.py:181 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_opening_manager.py:294 [time_time]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_opening_manager.py:294 [millisecond_conversion]
  代码: execution_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_opening_manager.py:353 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: time.time()

123/core/unified_opening_manager.py:353 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: * 1000

123/core/unified_opening_manager.py:363 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: time.time()

123/core/unified_opening_manager.py:363 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: * 1000

123/core/arbitrage_engine.py:680 [time_time]
  代码: session_duration = time.time() - self.current_session.start_time
  匹配: time.time()

123/core/arbitrage_engine.py:810 [time_time]
  代码: self.current_session.end_time = time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:923 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:923 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/arbitrage_engine.py:952 [time_time]
  代码: self.current_session.end_time = time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:1379 [time_time]
  代码: session_id = f"ARB_{int(time.time())}"
  匹配: time.time()

123/core/arbitrage_engine.py:1394 [time_time]
  代码: start_time=time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:1647 [time_time]
  代码: self.current_session.end_time = time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:1680 [time_time]
  代码: self.current_session.end_time = time.time()
  匹配: time.time()

123/core/arbitrage_engine.py:1735 [time_time]
  代码: 'duration': time.time() - self.current_session.start_time,
  匹配: time.time()

123/core/execution_params_preparer.py:55 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/unified_order_spread_calculator.py:69 [timestamp_comparison]
  代码: self._snapshot_timestamp = None
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:69 [timestamp_assignment]
  代码: self._snapshot_timestamp = None
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:655 [time_time]
  代码: unified_timestamp = int(time.time() * 1000)
  匹配: time.time()

123/core/unified_order_spread_calculator.py:655 [timestamp_comparison]
  代码: unified_timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:655 [millisecond_conversion]
  代码: unified_timestamp = int(time.time() * 1000)
  匹配: * 1000

123/core/unified_order_spread_calculator.py:655 [timestamp_assignment]
  代码: unified_timestamp = int(time.time() * 1000)
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:676 [timestamp_comparison]
  代码: self._snapshot_timestamp = unified_timestamp
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:676 [timestamp_assignment]
  代码: self._snapshot_timestamp = unified_timestamp
  匹配: timestamp =

123/core/unified_order_spread_calculator.py:706 [current_time]
  代码: current_time = get_synced_timestamp("system", None)
  匹配: current_time =

123/core/unified_order_spread_calculator.py:707 [age_calculation]
  代码: snapshot_age = current_time - snapshot['snapshot_timestamp']
  匹配: age = current_time - snapshot['snapshot_timestamp

123/core/unified_order_spread_calculator.py:707 [potential_unit_mismatch]
  代码: snapshot_age = current_time - snapshot['snapshot_timestamp']
  匹配: 可能的单位不一致

123/core/parallel_arbitrage_controller.py:111 [time_time]
  代码: 'start_time': time.time(),
  匹配: time.time()

123/core/parallel_arbitrage_controller.py:113 [time_time]
  代码: 'order_id': f"ARB_{symbol}_{int(time.time())}",  # 生成订单编号
  匹配: time.time()

123/core/parallel_arbitrage_controller.py:141 [time_time]
  代码: duration = time.time() - arbitrage_info['start_time']
  匹配: time.time()

123/core/parallel_arbitrage_controller.py:159 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/parallel_arbitrage_controller.py:159 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/parallel_arbitrage_controller.py:258 [time_time]
  代码: duration = time.time() - info['start_time']
  匹配: time.time()

123/core/order_pairing_manager.py:68 [time_time]
  代码: pair_id = f"pair_{self.pair_counter}_{int(time.time())}"
  匹配: time.time()

123/core/execution_engine.py:251 [timestamp_field]
  代码: 'timestamp': market_data.timestamp,
  匹配: .timestamp

123/core/execution_engine.py:266 [timestamp_comparison]
  代码: timestamp=basic_orderbook['timestamp'],
  匹配: timestamp=

123/core/execution_engine.py:266 [timestamp_assignment]
  代码: timestamp=basic_orderbook['timestamp'],
  匹配: timestamp=

123/core/execution_engine.py:299 [timestamp_comparison]
  代码: timestamp=orderbook.get('timestamp'),
  匹配: timestamp=

123/core/execution_engine.py:299 [timestamp_assignment]
  代码: timestamp=orderbook.get('timestamp'),
  匹配: timestamp=

123/core/execution_engine.py:310 [second_conversion]
  代码: # self.logger.info(f"   数据延迟: {validation_result.data_age_ms/1000:.2f}秒, 质量评分: {validation_result.quality_score:.2f}")
  匹配: /1000

123/core/execution_engine.py:667 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:773 [time_time]
  代码: while time.time() < end_time:
  匹配: time.time()

123/core/execution_engine.py:775 [time_time]
  代码: loop_start = time.time()  # 🚀 新增：循环开始时间
  匹配: time.time()

123/core/execution_engine.py:790 [time_time]
  代码: convergence_start = time.time()  # 🚀 新增：趋同检测开始时间
  匹配: time.time()

123/core/execution_engine.py:792 [time_time]
  代码: convergence_time = (time.time() - convergence_start) * 1000  # 🚀 新增：趋同检测耗时
  匹配: time.time()

123/core/execution_engine.py:792 [millisecond_conversion]
  代码: convergence_time = (time.time() - convergence_start) * 1000  # 🚀 新增：趋同检测耗时
  匹配: * 1000

123/core/execution_engine.py:795 [time_time]
  代码: loop_time = (time.time() - loop_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:795 [millisecond_conversion]
  代码: loop_time = (time.time() - loop_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:796 [time_time]
  代码: elapsed_total = time.time() - start_time
  匹配: time.time()

123/core/execution_engine.py:805 [time_time]
  代码: current_spread, start_time, time.time()
  匹配: time.time()

123/core/execution_engine.py:832 [time_time]
  代码: threshold_info = convergence_monitor.dynamic_threshold.get_threshold_info(start_time, time.time())
  匹配: time.time()

123/core/execution_engine.py:847 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:847 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/execution_engine.py:872 [time_time]
  代码: remaining_time = end_time - time.time()
  匹配: time.time()

123/core/execution_engine.py:881 [time_time]
  代码: monitoring_time = time.time() - start_time
  匹配: time.time()

123/core/execution_engine.py:882 [timestamp_comparison]
  代码: end_timestamp = time.strftime('%H:%M:%S.%f', time.localtime())[:-3]
  匹配: timestamp =

123/core/execution_engine.py:882 [timestamp_assignment]
  代码: end_timestamp = time.strftime('%H:%M:%S.%f', time.localtime())[:-3]
  匹配: timestamp =

123/core/execution_engine.py:939 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:950 [time_time]
  代码: end_time=time.time(),
  匹配: time.time()

123/core/execution_engine.py:1038 [time_time]
  代码: parallel_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1040 [time_time]
  代码: parallel_time = (time.time() - parallel_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1040 [millisecond_conversion]
  代码: parallel_time = (time.time() - parallel_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1086 [time_time]
  代码: closing_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1088 [time_time]
  代码: closing_time = (time.time() - closing_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1088 [millisecond_conversion]
  代码: closing_time = (time.time() - closing_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1098 [time_time]
  代码: self.current_execution.end_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:1105 [millisecond_conversion]
  代码: total_time_ms = self.current_execution.execution_time * 1000
  匹配: * 1000

123/core/execution_engine.py:1403 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/core/execution_engine.py:1403 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/core/execution_engine.py:1403 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/core/execution_engine.py:1404 [timestamp_field]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1404 [timestamp_field]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1404 [timestamp_field]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1404 [timestamp_comparison]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: timestamp <

123/core/execution_engine.py:1404 [millisecond_conversion]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: * 1000

123/core/execution_engine.py:1404 [age_calculation]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp

123/core/execution_engine.py:1404 [potential_unit_mismatch]
  代码: spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
  匹配: 可能的单位不一致

123/core/execution_engine.py:1405 [timestamp_field]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1405 [timestamp_field]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1405 [timestamp_field]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: .timestamp

123/core/execution_engine.py:1405 [timestamp_comparison]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: timestamp <

123/core/execution_engine.py:1405 [millisecond_conversion]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: * 1000

123/core/execution_engine.py:1405 [age_calculation]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp

123/core/execution_engine.py:1405 [potential_unit_mismatch]
  代码: futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)
  匹配: 可能的单位不一致

123/core/execution_engine.py:1504 [time_time]
  代码: execution_start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:1512 [time_time]
  代码: prep_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1614 [time_time]
  代码: prep_time = (time.time() - prep_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1614 [millisecond_conversion]
  代码: prep_time = (time.time() - prep_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1618 [time_time]
  代码: parallel_prep_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1715 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/core/execution_engine.py:1715 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/core/execution_engine.py:1715 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/core/execution_engine.py:1716 [timestamp_comparison]
  代码: spot_timestamp = spot_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/core/execution_engine.py:1716 [timestamp_assignment]
  代码: spot_timestamp = spot_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/core/execution_engine.py:1717 [timestamp_comparison]
  代码: futures_timestamp = futures_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/core/execution_engine.py:1717 [timestamp_assignment]
  代码: futures_timestamp = futures_orderbook.get('timestamp', current_time)
  匹配: timestamp =

123/core/execution_engine.py:1720 [timestamp_comparison]
  代码: if spot_timestamp < 1e12:
  匹配: timestamp <

123/core/execution_engine.py:1722 [timestamp_comparison]
  代码: if futures_timestamp < 1e12:
  匹配: timestamp <

123/core/execution_engine.py:1726 [potential_unit_mismatch]
  代码: data_age_spot = current_time - spot_timestamp
  匹配: 可能的单位不一致

123/core/execution_engine.py:1727 [potential_unit_mismatch]
  代码: data_age_futures = current_time - futures_timestamp
  匹配: 可能的单位不一致

123/core/execution_engine.py:1733 [time_time]
  代码: parallel_prep_time = (time.time() - parallel_prep_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1733 [millisecond_conversion]
  代码: parallel_prep_time = (time.time() - parallel_prep_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1738 [time_time]
  代码: validation_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1810 [time_time]
  代码: revalidation_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1812 [time_time]
  代码: revalidation_time = (time.time() - revalidation_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1812 [millisecond_conversion]
  代码: revalidation_time = (time.time() - revalidation_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1867 [time_time]
  代码: validation_time = (time.time() - validation_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1867 [millisecond_conversion]
  代码: validation_time = (time.time() - validation_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1873 [time_time]
  代码: trading_start = time.time()
  匹配: time.time()

123/core/execution_engine.py:1895 [time_time]
  代码: trading_time = (time.time() - trading_start) * 1000
  匹配: time.time()

123/core/execution_engine.py:1895 [millisecond_conversion]
  代码: trading_time = (time.time() - trading_start) * 1000
  匹配: * 1000

123/core/execution_engine.py:1896 [time_time]
  代码: total_time = (time.time() - execution_start_time) * 1000
  匹配: time.time()

123/core/execution_engine.py:1896 [millisecond_conversion]
  代码: total_time = (time.time() - execution_start_time) * 1000
  匹配: * 1000

123/core/execution_engine.py:1991 [time_time]
  代码: total_time = (time.time() - execution_start_time) * 1000 if 'execution_start_time' in locals() else 0
  匹配: time.time()

123/core/execution_engine.py:1991 [millisecond_conversion]
  代码: total_time = (time.time() - execution_start_time) * 1000 if 'execution_start_time' in locals() else 0
  匹配: * 1000

123/core/execution_engine.py:2390 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/core/execution_engine.py:2390 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/core/execution_engine.py:2390 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/core/execution_engine.py:2391 [data_age]
  代码: data_age = current_time - orderbook.get('timestamp', current_time)
  匹配: data_age =

123/core/execution_engine.py:2391 [age_calculation]
  代码: data_age = current_time - orderbook.get('timestamp', current_time)
  匹配: age = current_time - orderbook.get('timestamp

123/core/execution_engine.py:2391 [potential_unit_mismatch]
  代码: data_age = current_time - orderbook.get('timestamp', current_time)
  匹配: 可能的单位不一致

123/core/execution_engine.py:2392 [data_age]
  代码: max_data_age = 2000  # 2秒内的数据才算新鲜
  匹配: data_age =

123/core/execution_engine.py:2392 [time_comparison]
  代码: max_data_age = 2000  # 2秒内的数据才算新鲜
  匹配: = 2000  # 2秒

123/core/execution_engine.py:2506 [time_time]
  代码: leverage_start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:2510 [time_time]
  代码: leverage_time = (time.time() - leverage_start_time) * 1000
  匹配: time.time()

123/core/execution_engine.py:2510 [millisecond_conversion]
  代码: leverage_time = (time.time() - leverage_start_time) * 1000
  匹配: * 1000

123/core/execution_engine.py:2549 [time_time]
  代码: leverage_start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:2555 [time_time]
  代码: leverage_time = (time.time() - leverage_start_time) * 1000
  匹配: time.time()

123/core/execution_engine.py:2555 [millisecond_conversion]
  代码: leverage_time = (time.time() - leverage_start_time) * 1000
  匹配: * 1000

123/core/execution_engine.py:2689 [time_time]
  代码: close_start_time = time.time()
  匹配: time.time()

123/core/execution_engine.py:2729 [time_time]
  代码: start_time = time.time()  # 默认值
  匹配: time.time()

123/core/execution_engine.py:2823 [second_conversion]
  代码: await asyncio.sleep(retry_delay / 1000.0)  # 转换为秒
  匹配: / 1000

123/core/execution_engine.py:2843 [time_time]
  代码: current_time = time.time() * 1000
  匹配: time.time()

123/core/execution_engine.py:2843 [current_time]
  代码: current_time = time.time() * 1000
  匹配: current_time =

123/core/execution_engine.py:2843 [millisecond_conversion]
  代码: current_time = time.time() * 1000
  匹配: * 1000

123/core/execution_engine.py:2844 [data_age]
  代码: spot_data_age = current_time - spot_orderbook.get('timestamp', current_time)
  匹配: data_age =

123/core/execution_engine.py:2844 [age_calculation]
  代码: spot_data_age = current_time - spot_orderbook.get('timestamp', current_time)
  匹配: age = current_time - spot_orderbook.get('timestamp

123/core/execution_engine.py:2844 [potential_unit_mismatch]
  代码: spot_data_age = current_time - spot_orderbook.get('timestamp', current_time)
  匹配: 可能的单位不一致

123/core/execution_engine.py:2845 [data_age]
  代码: futures_data_age = current_time - futures_orderbook.get('timestamp', current_time)
  匹配: data_age =

123/core/execution_engine.py:2845 [age_calculation]
  代码: futures_data_age = current_time - futures_orderbook.get('timestamp', current_time)
  匹配: age = current_time - futures_orderbook.get('timestamp

123/core/execution_engine.py:2845 [potential_unit_mismatch]
  代码: futures_data_age = current_time - futures_orderbook.get('timestamp', current_time)
  匹配: 可能的单位不一致

123/core/execution_engine.py:2950 [time_time]
  代码: close_time_ms = (time.time() - close_start_time) * 1000
  匹配: time.time()

123/core/execution_engine.py:2950 [millisecond_conversion]
  代码: close_time_ms = (time.time() - close_start_time) * 1000
  匹配: * 1000

123/core/execution_engine.py:3008 [timestamp_comparison]
  代码: timestamp = spot_orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/execution_engine.py:3008 [timestamp_assignment]
  代码: timestamp = spot_orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/execution_engine.py:3009 [time_time]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: time.time()

123/core/execution_engine.py:3009 [data_age]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: data_age =

123/core/execution_engine.py:3009 [millisecond_conversion]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: * 1000

123/core/execution_engine.py:3009 [age_calculation]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: age = (time.time() * 1000 - timestamp) if timestamp

123/core/system_monitor.py:41 [time_comparison]
  代码: self.check_interval = 30  # 30秒检查一次
  匹配: = 30  # 30秒

123/core/system_monitor.py:68 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/system_monitor.py:68 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/system_monitor.py:130 [time_time]
  代码: 'timestamp': time.time(),
  匹配: time.time()

123/core/system_monitor.py:149 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:149 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:149 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:164 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:164 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:164 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:173 [time_time]
  代码: if time.time() - error_time > 60:  # 错误状态超过60秒
  匹配: time.time()

123/core/system_monitor.py:179 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:179 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:179 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:209 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:209 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:209 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:228 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:228 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:228 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:242 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:242 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:242 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:253 [time_time]
  代码: timestamp=time.time(),
  匹配: time.time()

123/core/system_monitor.py:253 [timestamp_comparison]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:253 [timestamp_assignment]
  代码: timestamp=time.time(),
  匹配: timestamp=

123/core/system_monitor.py:465 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/system_monitor.py:465 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/system_monitor.py:469 [timestamp_field]
  代码: if current_time - issue.timestamp < 300
  匹配: .timestamp

123/core/system_monitor.py:469 [timestamp_comparison]
  代码: if current_time - issue.timestamp < 300
  匹配: timestamp <

123/core/system_monitor.py:469 [potential_unit_mismatch]
  代码: if current_time - issue.timestamp < 300
  匹配: 可能的单位不一致

123/core/system_monitor.py:749 [time_time]
  代码: 'timestamp': time.time()
  匹配: time.time()

123/core/system_monitor.py:765 [time_time]
  代码: 'timestamp': time.time()
  匹配: time.time()

123/core/trading_system_initializer.py:43 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/trading_system_initializer.py:79 [time_time]
  代码: preheat_start_time = time.time()
  匹配: time.time()

123/core/trading_system_initializer.py:88 [time_time]
  代码: preheat_time = (time.time() - preheat_start_time) * 1000
  匹配: time.time()

123/core/trading_system_initializer.py:88 [millisecond_conversion]
  代码: preheat_time = (time.time() - preheat_start_time) * 1000
  匹配: * 1000

123/core/trading_system_initializer.py:123 [time_time]
  代码: self.initialization_time = time.time() - start_time
  匹配: time.time()

123/core/trading_system_initializer.py:549 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/trading_system_initializer.py:549 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/unified_leverage_manager.py:52 [time_time]
  代码: return (time.time() - cache_entry.get('timestamp', 0)) < self.cache_ttl
  匹配: time.time()

123/core/unified_leverage_manager.py:72 [time_time]
  代码: 'timestamp': time.time(),
  匹配: time.time()

123/core/unified_leverage_manager.py:437 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/unified_leverage_manager.py:437 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/unified_leverage_manager.py:441 [potential_unit_mismatch]
  代码: if (current_time - cache_entry.get('timestamp', 0)) >= self.cache_ttl:
  匹配: 可能的单位不一致

123/core/unified_closing_manager.py:155 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/unified_closing_manager.py:169 [time_time]
  代码: result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_closing_manager.py:169 [millisecond_conversion]
  代码: result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_closing_manager.py:196 [time_time]
  代码: retry_result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_closing_manager.py:196 [millisecond_conversion]
  代码: retry_result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_closing_manager.py:207 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: time.time()

123/core/unified_closing_manager.py:207 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000,
  匹配: * 1000

123/core/unified_closing_manager.py:213 [time_time]
  代码: result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_closing_manager.py:213 [millisecond_conversion]
  代码: result.execution_time_ms = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_closing_manager.py:221 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_closing_manager.py:221 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_closing_manager.py:231 [time_time]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_closing_manager.py:231 [millisecond_conversion]
  代码: execution_time_ms=(time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_closing_manager.py:270 [timestamp_comparison]
  代码: timestamp = orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/unified_closing_manager.py:270 [timestamp_assignment]
  代码: timestamp = orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/unified_closing_manager.py:271 [time_time]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: time.time()

123/core/unified_closing_manager.py:271 [data_age]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: data_age =

123/core/unified_closing_manager.py:271 [millisecond_conversion]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: * 1000

123/core/unified_closing_manager.py:271 [age_calculation]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: age = (time.time() * 1000 - timestamp) if timestamp

123/core/unified_closing_manager.py:287 [timestamp_comparison]
  代码: timestamp = orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/unified_closing_manager.py:287 [timestamp_assignment]
  代码: timestamp = orderbook.get('timestamp', 0)
  匹配: timestamp =

123/core/unified_closing_manager.py:288 [time_time]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: time.time()

123/core/unified_closing_manager.py:288 [data_age]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: data_age =

123/core/unified_closing_manager.py:288 [millisecond_conversion]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: * 1000

123/core/unified_closing_manager.py:288 [age_calculation]
  代码: data_age = (time.time() * 1000 - timestamp) if timestamp else 0
  匹配: age = (time.time() * 1000 - timestamp) if timestamp

123/core/unified_http_session_manager.py:150 [time_time]
  代码: self._session_created_time[exchange_name] = time.time()
  匹配: time.time()

123/core/unified_http_session_manager.py:271 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/unified_http_session_manager.py:283 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:283 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:288 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:288 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:296 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:296 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:299 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:299 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:303 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:303 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:308 [time_time]
  代码: delay = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/unified_http_session_manager.py:308 [millisecond_conversion]
  代码: delay = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/unified_http_session_manager.py:392 [time_time]
  代码: "age_seconds": time.time() - created_time,
  匹配: time.time()

123/core/convergence_monitor.py:116 [time_time]
  代码: "start_time": time.time(),  # 使用时间戳
  匹配: time.time()

123/core/convergence_monitor.py:120 [time_time]
  代码: "last_update_time": time.time(),
  匹配: time.time()

123/core/convergence_monitor.py:141 [time_time]
  代码: elapsed = time.time() - self.active_monitors[symbol]["start_time"]
  匹配: time.time()

123/core/convergence_monitor.py:206 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/convergence_monitor.py:288 [time_time]
  代码: monitor_info["last_update_time"] = time.time()
  匹配: time.time()

123/core/convergence_monitor.py:294 [time_time]
  代码: elapsed_ms = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/convergence_monitor.py:294 [millisecond_conversion]
  代码: elapsed_ms = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/convergence_monitor.py:295 [time_time]
  代码: elapsed_total = time.time() - monitor_info["start_time"]
  匹配: time.time()

123/core/convergence_monitor.py:320 [time_time]
  代码: detection_start = time.time()
  匹配: time.time()

123/core/convergence_monitor.py:329 [time_time]
  代码: elapsed_total = time.time() - monitor_info["start_time"]
  匹配: time.time()

123/core/convergence_monitor.py:351 [time_time]
  代码: detection_time = (time.time() - detection_start) * 1000
  匹配: time.time()

123/core/convergence_monitor.py:351 [millisecond_conversion]
  代码: detection_time = (time.time() - detection_start) * 1000
  匹配: * 1000

123/core/convergence_monitor.py:387 [time_time]
  代码: elapsed = time.time() - monitor_info["start_time"]
  匹配: time.time()

123/core/convergence_monitor.py:411 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/convergence_monitor.py:411 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/convergence_monitor.py:449 [time_time]
  代码: elapsed_time = time.time() - monitor_info["start_time"]
  匹配: time.time()

123/core/dynamic_convergence_threshold.py:76 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/dynamic_convergence_threshold.py:76 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/dynamic_convergence_threshold.py:164 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/dynamic_convergence_threshold.py:164 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/dynamic_convergence_threshold.py:264 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/dynamic_convergence_threshold.py:264 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:283 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:283 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:331 [millisecond_conversion]
  代码: current_ms = int((current_time % 1) * 1000)  # 获取当前时间的毫秒部分
  匹配: * 1000

123/core/opportunity_scanner.py:381 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:381 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:417 [time_time]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: time.time()

123/core/opportunity_scanner.py:417 [current_time]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: current_time =

123/core/opportunity_scanner.py:417 [millisecond_conversion]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: * 1000

123/core/opportunity_scanner.py:418 [timestamp_field]
  代码: data_age = current_time - stored_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:418 [data_age]
  代码: data_age = current_time - stored_data.timestamp
  匹配: data_age =

123/core/opportunity_scanner.py:418 [age_calculation]
  代码: data_age = current_time - stored_data.timestamp
  匹配: age = current_time - stored_data.timestamp

123/core/opportunity_scanner.py:418 [potential_unit_mismatch]
  代码: data_age = current_time - stored_data.timestamp
  匹配: 可能的单位不一致

123/core/opportunity_scanner.py:470 [time_time]
  代码: timestamp=time.time() * 1000,
  匹配: time.time()

123/core/opportunity_scanner.py:470 [timestamp_comparison]
  代码: timestamp=time.time() * 1000,
  匹配: timestamp=

123/core/opportunity_scanner.py:470 [millisecond_conversion]
  代码: timestamp=time.time() * 1000,
  匹配: * 1000

123/core/opportunity_scanner.py:470 [timestamp_assignment]
  代码: timestamp=time.time() * 1000,
  匹配: timestamp=

123/core/opportunity_scanner.py:508 [time_time]
  代码: return f"invalid_{int(time.time())}"
  匹配: time.time()

123/core/opportunity_scanner.py:523 [time_time]
  代码: return f"emergency_{exchange}_{market_type}_{symbol}_{int(time.time())}"
  匹配: time.time()

123/core/opportunity_scanner.py:726 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:726 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:763 [timestamp_field]
  代码: data_age = (current_time * 1000) - market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:763 [data_age]
  代码: data_age = (current_time * 1000) - market_data.timestamp
  匹配: data_age =

123/core/opportunity_scanner.py:763 [millisecond_conversion]
  代码: data_age = (current_time * 1000) - market_data.timestamp
  匹配: * 1000

123/core/opportunity_scanner.py:763 [age_calculation]
  代码: data_age = (current_time * 1000) - market_data.timestamp
  匹配: age = (current_time * 1000) - market_data.timestamp

123/core/opportunity_scanner.py:848 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:848 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:923 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:923 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:991 [time_time]
  代码: timestamp=time.time() * 1000,
  匹配: time.time()

123/core/opportunity_scanner.py:991 [timestamp_comparison]
  代码: timestamp=time.time() * 1000,
  匹配: timestamp=

123/core/opportunity_scanner.py:991 [millisecond_conversion]
  代码: timestamp=time.time() * 1000,
  匹配: * 1000

123/core/opportunity_scanner.py:991 [timestamp_assignment]
  代码: timestamp=time.time() * 1000,
  匹配: timestamp=

123/core/opportunity_scanner.py:1165 [millisecond_conversion]
  代码: self.logger.info(f"🔍 orderbook详情: asks={len(asks)}档, bids={len(bids)}档, 最优价差={(best_ask-best_bid)/best_bid*10000:.2f}bps")
  匹配: *1000

123/core/opportunity_scanner.py:1263 [time_time]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: time.time()

123/core/opportunity_scanner.py:1263 [millisecond_conversion]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: * 1000

123/core/opportunity_scanner.py:1284 [time_time]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: time.time()

123/core/opportunity_scanner.py:1284 [millisecond_conversion]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: * 1000

123/core/opportunity_scanner.py:1308 [time_time]
  代码: 'timestamp': nested_orderbook.get('timestamp', int(time.time() * 1000)),
  匹配: time.time()

123/core/opportunity_scanner.py:1308 [millisecond_conversion]
  代码: 'timestamp': nested_orderbook.get('timestamp', int(time.time() * 1000)),
  匹配: * 1000

123/core/opportunity_scanner.py:1321 [time_time]
  代码: 'timestamp': nested_orderbook.get('timestamp', int(time.time() * 1000)),
  匹配: time.time()

123/core/opportunity_scanner.py:1321 [millisecond_conversion]
  代码: 'timestamp': nested_orderbook.get('timestamp', int(time.time() * 1000)),
  匹配: * 1000

123/core/opportunity_scanner.py:1339 [time_time]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: time.time()

123/core/opportunity_scanner.py:1339 [millisecond_conversion]
  代码: 'timestamp': data.get('timestamp', int(time.time() * 1000)),
  匹配: * 1000

123/core/opportunity_scanner.py:1351 [timestamp_comparison]
  代码: data_timestamp = get_synced_timestamp(exchange, data)
  匹配: timestamp =

123/core/opportunity_scanner.py:1351 [timestamp_assignment]
  代码: data_timestamp = get_synced_timestamp(exchange, data)
  匹配: timestamp =

123/core/opportunity_scanner.py:1361 [timestamp_comparison]
  代码: timestamp=data_timestamp,  # 🔥 修复：使用统一的毫秒时间戳
  匹配: timestamp=

123/core/opportunity_scanner.py:1361 [timestamp_assignment]
  代码: timestamp=data_timestamp,  # 🔥 修复：使用统一的毫秒时间戳
  匹配: timestamp=

123/core/opportunity_scanner.py:1393 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:1393 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:1415 [time_time]
  代码: self._last_price_log[unsupported_key] = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:1455 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:1455 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:1473 [timestamp_field]
  代码: # 🔥 关键修复：统一时间戳单位 - market_data.timestamp是毫秒，需要转换为秒
  匹配: .timestamp

123/core/opportunity_scanner.py:1474 [timestamp_field]
  代码: timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:1474 [timestamp_field]
  代码: timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:1474 [timestamp_field]
  代码: timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:1474 [timestamp_comparison]
  代码: timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp
  匹配: timestamp >

123/core/opportunity_scanner.py:1474 [second_conversion]
  代码: timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp
  匹配: / 1000

123/core/opportunity_scanner.py:1475 [data_age]
  代码: data_age = current_time - timestamp_seconds
  匹配: data_age =

123/core/opportunity_scanner.py:1475 [age_calculation]
  代码: data_age = current_time - timestamp_seconds
  匹配: age = current_time - timestamp

123/core/opportunity_scanner.py:1475 [potential_unit_mismatch]
  代码: data_age = current_time - timestamp_seconds
  匹配: 可能的单位不一致

123/core/opportunity_scanner.py:1506 [data_age]
  代码: data_age = current_time - last_update if last_update > 0 else 999
  匹配: data_age =

123/core/opportunity_scanner.py:1717 [time_time]
  代码: self.last_scan_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:1853 [time_time]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: time.time()

123/core/opportunity_scanner.py:1853 [current_time]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: current_time =

123/core/opportunity_scanner.py:1853 [millisecond_conversion]
  代码: current_time = time.time() * 1000  # 转换为毫秒
  匹配: * 1000

123/core/opportunity_scanner.py:1855 [timestamp_field]
  代码: spot_timestamp = spot_market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:1855 [timestamp_comparison]
  代码: spot_timestamp = spot_market_data.timestamp
  匹配: timestamp =

123/core/opportunity_scanner.py:1855 [timestamp_assignment]
  代码: spot_timestamp = spot_market_data.timestamp
  匹配: timestamp =

123/core/opportunity_scanner.py:1856 [timestamp_field]
  代码: futures_timestamp = futures_market_data.timestamp
  匹配: .timestamp

123/core/opportunity_scanner.py:1856 [timestamp_comparison]
  代码: futures_timestamp = futures_market_data.timestamp
  匹配: timestamp =

123/core/opportunity_scanner.py:1856 [timestamp_assignment]
  代码: futures_timestamp = futures_market_data.timestamp
  匹配: timestamp =

123/core/opportunity_scanner.py:1879 [timestamp_comparison]
  代码: spot_timestamp=spot_timestamp,
  匹配: timestamp=

123/core/opportunity_scanner.py:1879 [timestamp_assignment]
  代码: spot_timestamp=spot_timestamp,
  匹配: timestamp=

123/core/opportunity_scanner.py:1880 [timestamp_comparison]
  代码: futures_timestamp=futures_timestamp)
  匹配: timestamp=

123/core/opportunity_scanner.py:1880 [timestamp_assignment]
  代码: futures_timestamp=futures_timestamp)
  匹配: timestamp=

123/core/opportunity_scanner.py:1884 [potential_unit_mismatch]
  代码: data_age_spot = current_time - spot_timestamp
  匹配: 可能的单位不一致

123/core/opportunity_scanner.py:1885 [potential_unit_mismatch]
  代码: data_age_futures = current_time - futures_timestamp
  匹配: 可能的单位不一致

123/core/opportunity_scanner.py:1887 [data_age]
  代码: max_data_age = 500  # 🔥 关键修复：降低到500ms内，确保数据实时性，防止过期数据导致虚假套利
  匹配: data_age =

123/core/opportunity_scanner.py:1941 [time_time]
  代码: timestamp=int(time.time())
  匹配: time.time()

123/core/opportunity_scanner.py:1941 [timestamp_comparison]
  代码: timestamp=int(time.time())
  匹配: timestamp=

123/core/opportunity_scanner.py:1941 [timestamp_assignment]
  代码: timestamp=int(time.time())
  匹配: timestamp=

123/core/opportunity_scanner.py:1955 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:1955 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/opportunity_scanner.py:2003 [timestamp_comparison]
  代码: timestamp=int(current_time)
  匹配: timestamp=

123/core/opportunity_scanner.py:2003 [timestamp_assignment]
  代码: timestamp=int(current_time)
  匹配: timestamp=

123/core/opportunity_scanner.py:2136 [timestamp_comparison]
  代码: unified_timestamp = get_synced_timestamp(exchange.lower(), None)  # 🔥 修复：添加data参数
  匹配: timestamp =

123/core/opportunity_scanner.py:2136 [timestamp_assignment]
  代码: unified_timestamp = get_synced_timestamp(exchange.lower(), None)  # 🔥 修复：添加data参数
  匹配: timestamp =

123/core/opportunity_scanner.py:2142 [timestamp_comparison]
  代码: timestamp=unified_timestamp,  # 🔥 修复：使用统一时间戳处理器
  匹配: timestamp=

123/core/opportunity_scanner.py:2142 [timestamp_assignment]
  代码: timestamp=unified_timestamp,  # 🔥 修复：使用统一时间戳处理器
  匹配: timestamp=

123/core/opportunity_scanner.py:2156 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/opportunity_scanner.py:2156 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/data_snapshot_validator.py:67 [current_time]
  代码: current_time = get_synced_timestamp("system", None)
  匹配: current_time =

123/core/data_snapshot_validator.py:84 [age_calculation]
  代码: snapshot_age = current_time - snapshot['snapshot_timestamp']
  匹配: age = current_time - snapshot['snapshot_timestamp

123/core/data_snapshot_validator.py:84 [potential_unit_mismatch]
  代码: snapshot_age = current_time - snapshot['snapshot_timestamp']
  匹配: 可能的单位不一致

123/core/data_snapshot_validator.py:119 [timestamp_field]
  代码: spot_timestamp = snapshot['spot_data'].timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:119 [timestamp_comparison]
  代码: spot_timestamp = snapshot['spot_data'].timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:119 [timestamp_assignment]
  代码: spot_timestamp = snapshot['spot_data'].timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:120 [timestamp_field]
  代码: futures_timestamp = snapshot['futures_data'].timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:120 [timestamp_comparison]
  代码: futures_timestamp = snapshot['futures_data'].timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:120 [timestamp_assignment]
  代码: futures_timestamp = snapshot['futures_data'].timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:249 [timestamp_comparison]
  代码: unified_timestamp = get_synced_timestamp("system", None)
  匹配: timestamp =

123/core/data_snapshot_validator.py:249 [timestamp_assignment]
  代码: unified_timestamp = get_synced_timestamp("system", None)
  匹配: timestamp =

123/core/data_snapshot_validator.py:252 [current_time]
  代码: current_time = unified_timestamp
  匹配: current_time =

123/core/data_snapshot_validator.py:256 [timestamp_field]
  代码: spot_age = current_time - spot_data.timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:256 [age_calculation]
  代码: spot_age = current_time - spot_data.timestamp
  匹配: age = current_time - spot_data.timestamp

123/core/data_snapshot_validator.py:256 [potential_unit_mismatch]
  代码: spot_age = current_time - spot_data.timestamp
  匹配: 可能的单位不一致

123/core/data_snapshot_validator.py:260 [timestamp_field]
  代码: spot_data.timestamp = unified_timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:260 [timestamp_comparison]
  代码: spot_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:260 [timestamp_assignment]
  代码: spot_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:263 [timestamp_field]
  代码: spot_data.timestamp = min(spot_data.timestamp, unified_timestamp)
  匹配: .timestamp

123/core/data_snapshot_validator.py:263 [timestamp_field]
  代码: spot_data.timestamp = min(spot_data.timestamp, unified_timestamp)
  匹配: .timestamp

123/core/data_snapshot_validator.py:263 [timestamp_comparison]
  代码: spot_data.timestamp = min(spot_data.timestamp, unified_timestamp)
  匹配: timestamp =

123/core/data_snapshot_validator.py:263 [timestamp_assignment]
  代码: spot_data.timestamp = min(spot_data.timestamp, unified_timestamp)
  匹配: timestamp =

123/core/data_snapshot_validator.py:267 [timestamp_field]
  代码: spot_data.timestamp = unified_timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:267 [timestamp_comparison]
  代码: spot_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:267 [timestamp_assignment]
  代码: spot_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:274 [timestamp_field]
  代码: futures_age = current_time - futures_data.timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:274 [age_calculation]
  代码: futures_age = current_time - futures_data.timestamp
  匹配: age = current_time - futures_data.timestamp

123/core/data_snapshot_validator.py:274 [potential_unit_mismatch]
  代码: futures_age = current_time - futures_data.timestamp
  匹配: 可能的单位不一致

123/core/data_snapshot_validator.py:278 [timestamp_field]
  代码: futures_data.timestamp = unified_timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:278 [timestamp_comparison]
  代码: futures_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:278 [timestamp_assignment]
  代码: futures_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:281 [timestamp_field]
  代码: futures_data.timestamp = min(futures_data.timestamp, unified_timestamp)
  匹配: .timestamp

123/core/data_snapshot_validator.py:281 [timestamp_field]
  代码: futures_data.timestamp = min(futures_data.timestamp, unified_timestamp)
  匹配: .timestamp

123/core/data_snapshot_validator.py:281 [timestamp_comparison]
  代码: futures_data.timestamp = min(futures_data.timestamp, unified_timestamp)
  匹配: timestamp =

123/core/data_snapshot_validator.py:281 [timestamp_assignment]
  代码: futures_data.timestamp = min(futures_data.timestamp, unified_timestamp)
  匹配: timestamp =

123/core/data_snapshot_validator.py:285 [timestamp_field]
  代码: futures_data.timestamp = unified_timestamp
  匹配: .timestamp

123/core/data_snapshot_validator.py:285 [timestamp_comparison]
  代码: futures_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/data_snapshot_validator.py:285 [timestamp_assignment]
  代码: futures_data.timestamp = unified_timestamp
  匹配: timestamp =

123/core/universal_token_system.py:29 [timestamp_comparison]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:29 [timestamp_assignment]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:71 [timestamp_comparison]
  代码: self._cache_timestamp = cache_data.get("timestamp", 0)
  匹配: timestamp =

123/core/universal_token_system.py:71 [timestamp_assignment]
  代码: self._cache_timestamp = cache_data.get("timestamp", 0)
  匹配: timestamp =

123/core/universal_token_system.py:74 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/universal_token_system.py:74 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/universal_token_system.py:75 [timestamp_comparison]
  代码: if current_time - self._cache_timestamp < self._cache_ttl:
  匹配: timestamp <

123/core/universal_token_system.py:75 [potential_unit_mismatch]
  代码: if current_time - self._cache_timestamp < self._cache_ttl:
  匹配: 可能的单位不一致

123/core/universal_token_system.py:81 [timestamp_comparison]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:81 [timestamp_assignment]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:88 [timestamp_comparison]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:88 [timestamp_assignment]
  代码: self._cache_timestamp = 0
  匹配: timestamp =

123/core/universal_token_system.py:97 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/core/universal_token_system.py:136 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/universal_token_system.py:136 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/universal_token_system.py:137 [potential_unit_mismatch]
  代码: return (current_time - self._cache_timestamp) < self._cache_ttl
  匹配: 可能的单位不一致

123/core/universal_token_system.py:145 [time_time]
  代码: "added_time": time.time(),
  匹配: time.time()

123/core/universal_token_system.py:149 [time_time]
  代码: self._cache_timestamp = time.time()
  匹配: time.time()

123/core/universal_token_system.py:149 [timestamp_comparison]
  代码: self._cache_timestamp = time.time()
  匹配: timestamp =

123/core/universal_token_system.py:149 [timestamp_assignment]
  代码: self._cache_timestamp = time.time()
  匹配: timestamp =

123/core/unified_balance_manager.py:62 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/unified_balance_manager.py:62 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/unified_balance_manager.py:202 [time_time]
  代码: "cache_age": time.time() - self.last_update_time,
  匹配: time.time()

123/core/trading_rules_preloader.py:522 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:561 [time_time]
  代码: duration = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/trading_rules_preloader.py:561 [millisecond_conversion]
  代码: duration = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/trading_rules_preloader.py:563 [time_time]
  代码: self.stats["last_preload"] = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:635 [time_time]
  代码: api_start_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:644 [time_time]
  代码: api_duration = (time.time() - api_start_time) * 1000
  匹配: time.time()

123/core/trading_rules_preloader.py:644 [millisecond_conversion]
  代码: api_duration = (time.time() - api_start_time) * 1000
  匹配: * 1000

123/core/trading_rules_preloader.py:670 [time_time]
  代码: timestamp=time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:670 [timestamp_comparison]
  代码: timestamp=time.time()
  匹配: timestamp=

123/core/trading_rules_preloader.py:670 [timestamp_assignment]
  代码: timestamp=time.time()
  匹配: timestamp=

123/core/trading_rules_preloader.py:711 [time_time]
  代码: if time.time() - rule.timestamp < self.trading_rules_ttl:
  匹配: time.time()

123/core/trading_rules_preloader.py:711 [timestamp_field]
  代码: if time.time() - rule.timestamp < self.trading_rules_ttl:
  匹配: .timestamp

123/core/trading_rules_preloader.py:711 [timestamp_comparison]
  代码: if time.time() - rule.timestamp < self.trading_rules_ttl:
  匹配: timestamp <

123/core/trading_rules_preloader.py:711 [potential_unit_mismatch]
  代码: if time.time() - rule.timestamp < self.trading_rules_ttl:
  匹配: 可能的单位不一致

123/core/trading_rules_preloader.py:911 [time_time]
  代码: cache_age = time.time() - cached_data.get("cache_time", 0)
  匹配: time.time()

123/core/trading_rules_preloader.py:984 [time_time]
  代码: "cache_time": time.time(),
  匹配: time.time()

123/core/trading_rules_preloader.py:1000 [time_time]
  代码: "cache_time": time.time(),
  匹配: time.time()

123/core/trading_rules_preloader.py:1020 [time_time]
  代码: "cache_time": time.time(),
  匹配: time.time()

123/core/trading_rules_preloader.py:1160 [time_time]
  代码: timestamp=time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1160 [timestamp_comparison]
  代码: timestamp=time.time()
  匹配: timestamp=

123/core/trading_rules_preloader.py:1160 [timestamp_assignment]
  代码: timestamp=time.time()
  匹配: timestamp=

123/core/trading_rules_preloader.py:1378 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1403 [time_time]
  代码: processing_time = (time.time() - start_time) * 1000
  匹配: time.time()

123/core/trading_rules_preloader.py:1403 [millisecond_conversion]
  代码: processing_time = (time.time() - start_time) * 1000
  匹配: * 1000

123/core/trading_rules_preloader.py:1523 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1523 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/trading_rules_preloader.py:1551 [time_time]
  代码: start_calc_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1588 [time_time]
  代码: calc_time = (time.time() - start_calc_time) * 1000
  匹配: time.time()

123/core/trading_rules_preloader.py:1588 [millisecond_conversion]
  代码: calc_time = (time.time() - start_calc_time) * 1000
  匹配: * 1000

123/core/trading_rules_preloader.py:1622 [time_time]
  代码: current_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1622 [current_time]
  代码: current_time = time.time()
  匹配: current_time =

123/core/trading_rules_preloader.py:1634 [time_time]
  代码: api_start_time = time.time()
  匹配: time.time()

123/core/trading_rules_preloader.py:1641 [time_time]
  代码: api_duration = (time.time() - api_start_time) * 1000
  匹配: time.time()

123/core/trading_rules_preloader.py:1641 [millisecond_conversion]
  代码: api_duration = (time.time() - api_start_time) * 1000
  匹配: * 1000

123/core/trading_rules_preloader.py:1893 [time_time]
  代码: "timestamp": time.time(),
  匹配: time.time()

123/core/trading_rules_preloader.py:1948 [time_time]
  代码: start_time = time.time()
  匹配: time.time()

