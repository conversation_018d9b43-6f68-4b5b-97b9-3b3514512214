#!/usr/bin/env python3
"""
🏛️ 机构级别时间戳统一性审查脚本
按照修复质量保证.md的严格标准进行全面审查
"""

import sys
import os
import re
import ast
import time
from typing import Dict, List, Tuple, Set
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '123')
sys.path.insert(0, os.path.join(os.getcwd(), '123'))

class InstitutionalTimestampAuditor:
    """机构级别时间戳审查器"""
    
    def __init__(self):
        # 检测当前工作目录
        if os.path.exists('core') and os.path.exists('websocket'):
            self.project_root = Path('.')  # 已经在123目录中
        else:
            self.project_root = Path('123')  # 在上级目录
        self.issues = []
        self.stats = {
            'files_scanned': 0,
            'timestamp_usages': 0,
            'unified_function_usages': 0,
            'potential_issues': 0,
            'critical_issues': 0
        }
        
    def audit_complete_system(self) -> Dict:
        """完整系统审查"""
        print("🏛️ 开始机构级别时间戳统一性审查...")
        print("=" * 80)
        
        # 1. 扫描所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        print(f"📁 发现 {len(python_files)} 个Python文件")
        
        # 2. 审查每个文件
        for file_path in python_files:
            self._audit_file(file_path)
            
        # 3. 生成审查报告
        return self._generate_audit_report()
    
    def _audit_file(self, file_path: Path):
        """审查单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats['files_scanned'] += 1
            
            # 检查时间戳相关模式
            self._check_timestamp_patterns(file_path, content)
            self._check_unified_function_usage(file_path, content)
            self._check_potential_issues(file_path, content)
            
        except Exception as e:
            self.issues.append({
                'type': 'FILE_READ_ERROR',
                'file': str(file_path),
                'message': f"文件读取错误: {e}",
                'severity': 'HIGH'
            })
    
    def _check_timestamp_patterns(self, file_path: Path, content: str):
        """检查时间戳使用模式"""
        patterns = [
            (r'time\.time\(\)', 'time.time()调用'),
            (r'\.timestamp', '时间戳字段访问'),
            (r'timestamp\s*=', '时间戳赋值'),
            (r'current_time', '当前时间变量'),
            (r'data_age', '数据年龄计算'),
            (r'\*\s*1000', '时间戳单位转换'),
            (r'/\s*1000', '时间戳单位转换'),
        ]
        
        for pattern, desc in patterns:
            matches = re.findall(pattern, content)
            if matches:
                self.stats['timestamp_usages'] += len(matches)
                
                # 检查是否在关键文件中
                if any(key in str(file_path) for key in ['opportunity_scanner', 'execution_engine', 'data_snapshot_validator']):
                    print(f"  📍 {file_path.name}: 发现 {len(matches)} 个 {desc}")
    
    def _check_unified_function_usage(self, file_path: Path, content: str):
        """检查统一函数使用情况"""
        unified_functions = [
            'ensure_milliseconds_timestamp',
            'calculate_data_age',
            'get_synced_timestamp',
            'unified_timestamp_processor'
        ]
        
        for func in unified_functions:
            if func in content:
                self.stats['unified_function_usages'] += content.count(func)
                print(f"  ✅ {file_path.name}: 使用统一函数 {func}")
    
    def _check_potential_issues(self, file_path: Path, content: str):
        """检查潜在问题"""
        # 危险模式：直接时间戳计算
        dangerous_patterns = [
            (r'current_time\s*-\s*.*\.timestamp', '直接时间戳减法'),
            (r'time\.time\(\)\s*-\s*timestamp', '秒级减毫秒级'),
            (r'timestamp\s*-\s*time\.time\(\)', '毫秒级减秒级'),
            (r'if\s+.*timestamp.*<\s*1e12', '时间戳单位判断'),
        ]
        
        for pattern, desc in dangerous_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                # 检查是否已经修复（使用统一函数）
                if 'calculate_data_age' not in content and 'ensure_milliseconds_timestamp' not in content:
                    self.issues.append({
                        'type': 'POTENTIAL_TIMESTAMP_ISSUE',
                        'file': str(file_path),
                        'pattern': pattern,
                        'description': desc,
                        'matches': len(matches),
                        'severity': 'HIGH'
                    })
                    self.stats['potential_issues'] += 1
                else:
                    print(f"  🔧 {file_path.name}: 已修复的 {desc} (使用统一函数)")
    
    def _generate_audit_report(self) -> Dict:
        """生成审查报告"""
        print("\n" + "=" * 80)
        print("📋 机构级别审查报告")
        print("=" * 80)
        
        print(f"📊 统计信息:")
        print(f"  - 扫描文件数: {self.stats['files_scanned']}")
        print(f"  - 时间戳使用点: {self.stats['timestamp_usages']}")
        print(f"  - 统一函数使用: {self.stats['unified_function_usages']}")
        print(f"  - 潜在问题: {self.stats['potential_issues']}")
        
        # 关键文件检查
        if self.project_root == Path('.'):
            critical_files = [
                'core/opportunity_scanner.py',
                'core/execution_engine.py',
                'core/data_snapshot_validator.py',
                'websocket/unified_timestamp_processor.py'
            ]
        else:
            critical_files = [
                '123/core/opportunity_scanner.py',
                '123/core/execution_engine.py',
                '123/core/data_snapshot_validator.py',
                '123/websocket/unified_timestamp_processor.py'
            ]
        
        print(f"\n🔍 关键文件检查:")
        for file_path in critical_files:
            if os.path.exists(file_path):
                print(f"  ✅ {file_path} - 存在")
                self._check_critical_file_compliance(file_path)
            else:
                print(f"  ❌ {file_path} - 缺失")
                self.stats['critical_issues'] += 1
        
        # 问题汇总
        if self.issues:
            print(f"\n⚠️ 发现问题:")
            for issue in self.issues:
                severity_icon = "🔥" if issue['severity'] == 'HIGH' else "⚠️"
                print(f"  {severity_icon} {issue['type']}: {issue['file']}")
                print(f"     {issue.get('description', issue.get('message', ''))}")
        else:
            print(f"\n✅ 未发现严重问题")
        
        return {
            'stats': self.stats,
            'issues': self.issues,
            'compliance_score': self._calculate_compliance_score()
        }
    
    def _check_critical_file_compliance(self, file_path: str):
        """检查关键文件合规性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否使用统一函数
            if 'opportunity_scanner.py' in file_path:
                if 'calculate_data_age' in content:
                    print(f"    ✅ 使用统一数据年龄计算函数")
                else:
                    print(f"    ❌ 未使用统一数据年龄计算函数")
                    self.stats['critical_issues'] += 1
                    
            elif 'execution_engine.py' in file_path:
                if 'calculate_data_age' in content:
                    print(f"    ✅ 使用统一数据年龄计算函数")
                else:
                    print(f"    ❌ 未使用统一数据年龄计算函数")
                    self.stats['critical_issues'] += 1
                    
            elif 'data_snapshot_validator.py' in file_path:
                if 'calculate_data_age' in content and 'ensure_milliseconds_timestamp' in content:
                    print(f"    ✅ 使用统一时间戳处理函数")
                else:
                    print(f"    ❌ 未完全使用统一时间戳处理函数")
                    self.stats['critical_issues'] += 1
                    
            elif 'unified_timestamp_processor.py' in file_path:
                required_functions = ['ensure_milliseconds_timestamp', 'calculate_data_age']
                missing_functions = [func for func in required_functions if func not in content]
                if not missing_functions:
                    print(f"    ✅ 包含所有必需的统一函数")
                else:
                    print(f"    ❌ 缺少函数: {missing_functions}")
                    self.stats['critical_issues'] += 1
                    
        except Exception as e:
            print(f"    ❌ 文件检查失败: {e}")
            self.stats['critical_issues'] += 1
    
    def _calculate_compliance_score(self) -> float:
        """计算合规分数"""
        if self.stats['critical_issues'] == 0 and self.stats['potential_issues'] == 0:
            return 100.0
        
        total_issues = self.stats['critical_issues'] + self.stats['potential_issues']
        max_score = 100.0
        penalty_per_issue = 10.0
        
        score = max(0.0, max_score - (total_issues * penalty_per_issue))
        return score

def main():
    """主函数"""
    auditor = InstitutionalTimestampAuditor()
    report = auditor.audit_complete_system()
    
    print(f"\n🎯 最终评估:")
    print(f"合规分数: {report['compliance_score']:.1f}/100")
    
    if report['compliance_score'] >= 95:
        print("🏆 机构级别质量标准: 优秀")
        print("✅ 时间戳统一性修复完美！")
        return True
    elif report['compliance_score'] >= 80:
        print("⚠️ 机构级别质量标准: 良好，需要改进")
        return False
    else:
        print("❌ 机构级别质量标准: 不合格，需要重大修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
