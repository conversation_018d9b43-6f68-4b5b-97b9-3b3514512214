#!/usr/bin/env python3
"""
系统范围时间戳统一性验证脚本
验证所有时间戳计算使用统一的毫秒级标准
"""

import sys
import time
import os
from typing import Dict, Any, List, Tuple

# 添加项目路径
sys.path.insert(0, '123')
sys.path.insert(0, os.path.join(os.getcwd(), '123'))

def test_unified_timestamp_functions():
    """测试统一时间戳处理函数"""
    print("🔍 统一时间戳处理函数测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp, calculate_data_age
        
        # 测试ensure_milliseconds_timestamp
        print("\n1️⃣ ensure_milliseconds_timestamp 测试:")
        test_cases = [
            (time.time(), "当前秒级时间戳"),
            (int(time.time() * 1000), "当前毫秒级时间戳"),
            (1754028976.668, "秒级时间戳"),
            (1754028976668, "毫秒级时间戳"),
            (1754028976668000, "微秒级时间戳"),
            (None, "None值"),
        ]
        
        for timestamp, desc in test_cases:
            try:
                result = ensure_milliseconds_timestamp(timestamp)
                print(f"  {desc}: {timestamp} → {result}")
                print(f"    格式: {'毫秒级' if result > 1e12 else '秒级'}")
            except Exception as e:
                print(f"  {desc}: 错误 - {e}")
        
        # 测试calculate_data_age
        print("\n2️⃣ calculate_data_age 测试:")
        current_time = time.time()
        test_timestamps = [
            current_time,  # 秒级当前时间
            int(current_time * 1000),  # 毫秒级当前时间
            current_time - 1,  # 1秒前
            int((current_time - 1) * 1000),  # 1秒前毫秒级
        ]
        
        for ts in test_timestamps:
            try:
                age = calculate_data_age(ts, current_time)
                print(f"  时间戳 {ts}: 年龄 {age:.3f}秒")
            except Exception as e:
                print(f"  时间戳 {ts}: 错误 - {e}")
                
    except ImportError as e:
        print(f"❌ 无法导入统一时间戳处理函数: {e}")
        return False
    
    return True

def test_market_data_timestamp():
    """测试MarketData时间戳类型"""
    print("\n🔍 MarketData时间戳类型测试")
    print("=" * 60)
    
    try:
        from core.opportunity_scanner import MarketData
        
        # 创建测试数据
        current_ms = int(time.time() * 1000)
        market_data = MarketData(
            exchange="gate",
            symbol="BTC-USDT",
            price=50000.0,
            timestamp=current_ms,  # 现在应该是int类型
            orderbook={}
        )
        
        print(f"MarketData.timestamp: {market_data.timestamp}")
        print(f"类型: {type(market_data.timestamp)}")
        print(f"格式: {'毫秒级' if market_data.timestamp > 1e12 else '秒级'}")
        
        # 验证类型
        if isinstance(market_data.timestamp, int):
            print("✅ 时间戳类型正确 (int)")
        else:
            print(f"❌ 时间戳类型错误: {type(market_data.timestamp)}")
            return False
            
    except Exception as e:
        print(f"❌ MarketData测试失败: {e}")
        return False
    
    return True

def test_opportunity_scanner_fix():
    """测试OpportunityScanner修复效果"""
    print("\n🔍 OpportunityScanner修复效果测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age
        
        # 模拟修复后的逻辑
        current_time = time.time()  # 秒级
        market_data_timestamp = int(time.time() * 1000)  # 毫秒级
        
        print(f"当前时间(秒): {current_time}")
        print(f"数据时间戳(毫秒): {market_data_timestamp}")
        
        # 使用修复后的计算方式
        data_age = calculate_data_age(market_data_timestamp, current_time)
        print(f"数据年龄: {data_age:.3f}秒")
        print(f"是否活跃 (< 1秒): {data_age < 1}")
        
        # 对比修复前的错误计算
        wrong_age = current_time - market_data_timestamp
        print(f"\n对比修复前错误计算:")
        print(f"错误年龄: {wrong_age}")
        print(f"错误判断 (< 1): {wrong_age < 1}")
        
    except Exception as e:
        print(f"❌ OpportunityScanner测试失败: {e}")
        return False
    
    return True

def test_execution_engine_fix():
    """测试ExecutionEngine修复效果"""
    print("\n🔍 ExecutionEngine修复效果测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age
        
        # 模拟orderbook数据
        orderbook = {
            'timestamp': int(time.time() * 1000),  # 毫秒级时间戳
            'bids': [[50000, 1.0]],
            'asks': [[50100, 1.0]]
        }
        
        print(f"订单簿时间戳: {orderbook['timestamp']}")
        
        # 使用修复后的计算方式
        data_age_seconds = calculate_data_age(orderbook['timestamp'])
        data_age_ms = data_age_seconds * 1000
        
        print(f"数据年龄: {data_age_ms:.1f}ms")
        print(f"是否新鲜 (< 2000ms): {data_age_ms < 2000}")
        
    except Exception as e:
        print(f"❌ ExecutionEngine测试失败: {e}")
        return False
    
    return True

def test_data_snapshot_validator_fix():
    """测试DataSnapshotValidator修复效果"""
    print("\n🔍 DataSnapshotValidator修复效果测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age, ensure_milliseconds_timestamp
        
        # 模拟快照数据
        snapshot = {
            'snapshot_timestamp': int(time.time() * 1000),  # 毫秒级
            'spot_data': type('obj', (object,), {'timestamp': int(time.time() * 1000)})(),
            'futures_data': type('obj', (object,), {'timestamp': int(time.time() * 1000)})(),
            'spot_orderbook': {},
            'futures_orderbook': {}
        }
        
        current_time = int(time.time() * 1000)  # 毫秒级
        
        print(f"快照时间戳: {snapshot['snapshot_timestamp']}")
        print(f"当前时间: {current_time}")
        
        # 使用修复后的计算方式
        snapshot_age_seconds = calculate_data_age(snapshot['snapshot_timestamp'], current_time / 1000)
        snapshot_age_ms = snapshot_age_seconds * 1000
        
        print(f"快照年龄: {snapshot_age_ms:.1f}ms")
        
        # 测试数据时间戳标准化
        spot_timestamp_ms = ensure_milliseconds_timestamp(snapshot['spot_data'].timestamp)
        print(f"现货数据时间戳标准化: {snapshot['spot_data'].timestamp} → {spot_timestamp_ms}")
        
    except Exception as e:
        print(f"❌ DataSnapshotValidator测试失败: {e}")
        return False
    
    return True

def run_comprehensive_validation():
    """运行全面验证"""
    print("🚀 开始系统范围时间戳统一性验证...")
    print("=" * 80)
    
    tests = [
        ("统一时间戳处理函数", test_unified_timestamp_functions),
        ("MarketData时间戳类型", test_market_data_timestamp),
        ("OpportunityScanner修复", test_opportunity_scanner_fix),
        ("ExecutionEngine修复", test_execution_engine_fix),
        ("DataSnapshotValidator修复", test_data_snapshot_validator_fix),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name}: ❌ 异常 - {e}")
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 验证结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！时间戳统一性修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

def main():
    """主函数"""
    success = run_comprehensive_validation()
    
    if success:
        print("\n🎯 修复验证完成！")
        print("📋 关键修复点:")
        print("1. ✅ MarketData.timestamp 类型从 float 改为 int")
        print("2. ✅ 添加统一时间戳处理函数 ensure_milliseconds_timestamp()")
        print("3. ✅ 添加统一数据年龄计算函数 calculate_data_age()")
        print("4. ✅ OpportunityScanner 使用统一函数修复时间戳单位问题")
        print("5. ✅ ExecutionEngine 使用统一函数修复时间戳单位问题")
        print("6. ✅ DataSnapshotValidator 使用统一函数修复时间戳单位问题")
        print("\n🔥 系统现在使用统一的毫秒级时间戳标准！")
    else:
        print("\n❌ 验证失败，需要进一步检查和修复")
    
    return success

if __name__ == "__main__":
    main()
