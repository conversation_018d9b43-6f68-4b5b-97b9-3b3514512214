#!/usr/bin/env python3
"""
网络监控日志修复 - 复杂系统级联测试
测试模块交互逻辑、状态联动、多币种切换、多交易所分支
验证系统协同一致性
"""

import sys
import os
import asyncio
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123"))

from websocket.websocket_logger import (
    get_websocket_logger, log_websocket_performance, log_websocket_connection,
    log_websocket_error_recovery, log_websocket_silent_disconnect,
    log_websocket_subscription_failure
)

class NetworkMonitoringSystemIntegrationTest:
    """网络监控日志修复复杂系统级联测试"""
    
    def __init__(self):
        self.test_results = []
        self.test_exchanges = ["gate", "bybit", "okx"]
        self.test_symbols = ["BTC/USDT", "ETH/USDT", "SOL/USDT"]
        
    def test_multi_exchange_logging_consistency(self):
        """测试1：多交易所日志记录一致性"""
        print("\n🧪 测试1: 多交易所日志记录一致性")
        
        try:
            # 模拟三个交易所的各种日志场景
            test_scenarios = [
                {"type": "connection", "level": "info", "message": "连接建立"},
                {"type": "subscription_failure", "level": "error", "message": "订阅失败"},
                {"type": "silent_disconnect", "level": "warning", "message": "静默断开"},
                {"type": "error_recovery", "level": "error", "message": "错误恢复"},
                {"type": "performance", "level": "info", "message": "性能监控"}
            ]
            
            logged_events = []
            
            for exchange in self.test_exchanges:
                for scenario in test_scenarios:
                    # 根据日志类型调用相应的日志函数
                    if scenario["type"] == "connection":
                        log_websocket_connection(scenario["level"], scenario["message"], 
                                               exchange=exchange, test_id=f"{exchange}_{scenario['type']}")
                    elif scenario["type"] == "subscription_failure":
                        log_websocket_subscription_failure(scenario["level"], scenario["message"],
                                                         exchange=exchange, symbol="BTC/USDT")
                    elif scenario["type"] == "silent_disconnect":
                        log_websocket_silent_disconnect(scenario["level"], scenario["message"],
                                                      exchange=exchange, silent_duration=30)
                    elif scenario["type"] == "error_recovery":
                        log_websocket_error_recovery(scenario["level"], scenario["message"],
                                                   exchange=exchange, error_type="connection_lost")
                    elif scenario["type"] == "performance":
                        log_websocket_performance(scenario["level"], scenario["message"],
                                                exchange=exchange, latency_ms=5.2)
                    
                    logged_events.append(f"{exchange}_{scenario['type']}")
            
            # 验证所有交易所的所有场景都被记录
            expected_events = len(self.test_exchanges) * len(test_scenarios)
            actual_events = len(logged_events)
            
            if actual_events == expected_events:
                print(f"   ✅ 多交易所日志一致性验证通过: {actual_events}/{expected_events} 事件记录")
                self.test_results.append({"test": "multi_exchange_logging_consistency", "status": "PASS", 
                                        "message": f"所有{expected_events}个事件记录成功"})
                return True
            else:
                print(f"   ❌ 多交易所日志一致性验证失败: {actual_events}/{expected_events} 事件记录")
                self.test_results.append({"test": "multi_exchange_logging_consistency", "status": "FAIL",
                                        "message": f"只有{actual_events}/{expected_events}个事件记录成功"})
                return False
                
        except Exception as e:
            print(f"   ❌ 多交易所日志一致性测试失败: {e}")
            self.test_results.append({"test": "multi_exchange_logging_consistency", "status": "FAIL",
                                    "message": f"测试执行失败: {e}"})
            return False
    
    def test_cross_module_interaction(self):
        """测试2：跨模块交互验证"""
        print("\n🧪 测试2: 跨模块交互验证")
        
        try:
            # 模拟WebSocket管理器与日志系统的交互
            print("   模拟ws_manager.py静默断开检测...")
            
            # 模拟ws_manager.py中的静默断开检测逻辑
            current_time = time.time()
            last_update_time = current_time - 65  # 65秒前的更新
            delay = current_time - last_update_time
            
            # 模拟实际修复中的日志调用
            exchange = "gate"
            key = "gate_spot_BTCUSDT"
            log_websocket_silent_disconnect("warning", f"检测到静默断开",
                                          exchange=exchange, silent_duration=delay,
                                          last_update_time=last_update_time, key=key)
            
            print("   模拟gate_ws.py订阅失败...")
            
            # 模拟gate_ws.py中的订阅失败处理
            symbol = "ETH/USDT"
            market_type = "spot"
            log_websocket_subscription_failure("error", f"深度订阅失败",
                                             exchange="gate", symbol=symbol,
                                             market_type=market_type, channel="order_book")
            
            print("   模拟unified_timestamp_processor.py时间戳处理...")
            
            # 模拟时间戳处理器的日志记录
            log_websocket_performance("warning", f"时间戳过期数据丢弃",
                                    exchange="bybit", 
                                    timestamp_age_ms=5000,
                                    max_age_ms=2000,
                                    discarded_timestamp=int(time.time() * 1000))
            
            print("   模拟opportunity_scanner.py套利扫描...")
            
            # 模拟套利扫描器的时间戳同步日志
            log_websocket_performance("debug", f"价格数据时间戳不同步，丢弃套利机会",
                                    combo_name="A组合",
                                    spot_exchange="gate",
                                    futures_exchange="okx",
                                    time_diff_ms=250,
                                    max_diff_ms=200)
            
            print("   ✅ 跨模块交互验证通过 - 所有模块间日志调用正常")
            self.test_results.append({"test": "cross_module_interaction", "status": "PASS",
                                    "message": "跨模块交互验证通过"})
            return True
            
        except Exception as e:
            print(f"   ❌ 跨模块交互验证失败: {e}")
            self.test_results.append({"test": "cross_module_interaction", "status": "FAIL",
                                    "message": f"跨模块交互验证失败: {e}"})
            return False
    
    def test_multi_symbol_state_coordination(self):
        """测试3：多币种状态协调"""
        print("\n🧪 测试3: 多币种状态协调")
        
        try:
            # 模拟多个交易对的状态变化和日志记录
            state_changes = []
            
            for symbol in self.test_symbols:
                for exchange in self.test_exchanges:
                    # 模拟订阅状态变化
                    log_websocket_connection("info", f"{symbol}订阅建立", 
                                           exchange=exchange, symbol=symbol, status="subscribed")
                    state_changes.append(f"{exchange}_{symbol}_subscribed")
                    
                    # 模拟数据流状态
                    log_websocket_performance("info", f"{symbol}数据流正常",
                                            exchange=exchange, symbol=symbol, 
                                            data_rate=100, latency_ms=10)
                    state_changes.append(f"{exchange}_{symbol}_data_flow")
                    
                    # 模拟偶发的连接问题
                    if symbol == "SOL/USDT" and exchange == "bybit":
                        log_websocket_error_recovery("warning", f"{symbol}连接异常恢复",
                                                   exchange=exchange, symbol=symbol,
                                                   error_type="connection_timeout")
                        state_changes.append(f"{exchange}_{symbol}_recovery")
            
            # 验证状态协调的完整性
            expected_base_states = len(self.test_symbols) * len(self.test_exchanges) * 2  # 订阅+数据流
            expected_recovery_states = 1  # 一个恢复事件
            expected_total = expected_base_states + expected_recovery_states
            
            actual_states = len(state_changes)
            
            if actual_states == expected_total:
                print(f"   ✅ 多币种状态协调验证通过: {actual_states}/{expected_total} 状态变化记录")
                self.test_results.append({"test": "multi_symbol_state_coordination", "status": "PASS",
                                        "message": f"所有{expected_total}个状态变化记录成功"})
                return True
            else:
                print(f"   ❌ 多币种状态协调验证失败: {actual_states}/{expected_total} 状态变化记录")
                self.test_results.append({"test": "multi_symbol_state_coordination", "status": "FAIL",
                                        "message": f"只有{actual_states}/{expected_total}个状态变化记录成功"})
                return False
                
        except Exception as e:
            print(f"   ❌ 多币种状态协调测试失败: {e}")
            self.test_results.append({"test": "multi_symbol_state_coordination", "status": "FAIL",
                                    "message": f"测试执行失败: {e}"})
            return False
    
    def test_concurrent_logging_safety(self):
        """测试4：并发日志记录安全性"""
        print("\n🧪 测试4: 并发日志记录安全性")
        
        try:
            import threading
            import concurrent.futures
            
            def concurrent_logging_task(task_id):
                """并发日志记录任务"""
                try:
                    exchange = self.test_exchanges[task_id % len(self.test_exchanges)]
                    symbol = self.test_symbols[task_id % len(self.test_symbols)]
                    
                    # 并发记录不同类型的日志
                    log_websocket_performance("info", f"并发任务{task_id}性能日志",
                                            exchange=exchange, task_id=task_id, thread_id=threading.current_thread().ident)
                    
                    log_websocket_connection("info", f"并发任务{task_id}连接日志",
                                           exchange=exchange, task_id=task_id)
                    
                    log_websocket_subscription_failure("warning", f"并发任务{task_id}订阅日志",
                                                     exchange=exchange, symbol=symbol, task_id=task_id)
                    
                    return f"task_{task_id}_success"
                except Exception as e:
                    return f"task_{task_id}_failed: {e}"
            
            # 启动并发任务
            num_tasks = 20
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(concurrent_logging_task, i) for i in range(num_tasks)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # 验证并发安全性
            successful_tasks = [r for r in results if "success" in r]
            failed_tasks = [r for r in results if "failed" in r]
            
            if len(successful_tasks) == num_tasks:
                print(f"   ✅ 并发日志记录安全性验证通过: {len(successful_tasks)}/{num_tasks} 任务成功")
                self.test_results.append({"test": "concurrent_logging_safety", "status": "PASS",
                                        "message": f"所有{num_tasks}个并发任务成功"})
                return True
            else:
                print(f"   ❌ 并发日志记录安全性验证失败: {len(successful_tasks)}/{num_tasks} 任务成功")
                print(f"   失败任务: {failed_tasks}")
                self.test_results.append({"test": "concurrent_logging_safety", "status": "FAIL",
                                        "message": f"只有{len(successful_tasks)}/{num_tasks}个任务成功"})
                return False
                
        except Exception as e:
            print(f"   ❌ 并发日志记录安全性测试失败: {e}")
            self.test_results.append({"test": "concurrent_logging_safety", "status": "FAIL",
                                    "message": f"测试执行失败: {e}"})
            return False
    
    def run_all_tests(self):
        """运行所有复杂系统级联测试"""
        print("🚀 开始网络监控日志修复 - 复杂系统级联测试")
        print("=" * 60)
        
        # 运行所有测试
        tests = [
            self.test_multi_exchange_logging_consistency,
            self.test_cross_module_interaction,
            self.test_multi_symbol_state_coordination,
            self.test_concurrent_logging_safety
        ]
        
        passed_tests = 0
        for test in tests:
            if test():
                passed_tests += 1
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 复杂系统级联测试结果汇总")
        print("=" * 60)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test']}: {result['message']}")
        
        success_rate = (passed_tests / len(tests)) * 100
        print(f"\n📈 测试统计:")
        print(f"   总测试数: {len(tests)}")
        print(f"   通过: {passed_tests} ({success_rate:.1f}%)")
        print(f"   失败: {len(tests) - passed_tests}")
        
        if passed_tests == len(tests):
            print(f"\n🎉 复杂系统级联测试 100% 通过！系统协同一致性验证成功")
            return True
        else:
            print(f"\n⚠️ 复杂系统级联测试未完全通过，需要修复")
            return False

def main():
    """主函数"""
    tester = NetworkMonitoringSystemIntegrationTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 网络监控日志修复复杂系统级联测试完成 - 所有测试通过")
        return 0
    else:
        print("\n❌ 网络监控日志修复复杂系统级联测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
