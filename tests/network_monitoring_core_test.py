#!/usr/bin/env python3
"""
网络监控日志修复 - 基础核心测试
测试修复点的单元功能：参数输入输出、边界检查、错误处理
确保修复点本身100%稳定
"""

import sys
import os
import asyncio
import time
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123"))

from websocket.websocket_logger import (
    get_websocket_logger, log_websocket_performance, log_websocket_connection,
    log_websocket_error_recovery, log_websocket_silent_disconnect,
    log_websocket_subscription_failure
)

class NetworkMonitoringCoreTest:
    """网络监控日志修复基础核心测试"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.original_logs_dir = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建临时日志目录
        self.temp_dir = tempfile.mkdtemp(prefix="network_monitoring_test_")
        print(f"   临时日志目录: {self.temp_dir}")
        
        # 备份原始日志目录设置
        from websocket.websocket_logger import _websocket_logger
        if _websocket_logger:
            self.original_logs_dir = str(_websocket_logger.logs_dir)
            _websocket_logger.logs_dir = Path(self.temp_dir)
            _websocket_logger._setup_loggers()  # 重新设置日志器
        
        return True
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        
        # 恢复原始日志目录
        if self.original_logs_dir:
            from websocket.websocket_logger import _websocket_logger
            if _websocket_logger:
                _websocket_logger.logs_dir = Path(self.original_logs_dir)
                _websocket_logger._setup_loggers()
        
        # 删除临时目录
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"   已删除临时目录: {self.temp_dir}")
    
    def test_websocket_logger_initialization(self):
        """测试1：WebSocket日志器初始化"""
        print("\n🧪 测试1: WebSocket日志器初始化")
        
        try:
            # 测试获取日志器实例
            logger = get_websocket_logger()
            assert logger is not None, "日志器实例不能为空"
            
            # 测试日志器属性
            assert hasattr(logger, 'performance_logger'), "缺少性能日志器"
            assert hasattr(logger, 'connection_logger'), "缺少连接日志器"
            assert hasattr(logger, 'error_recovery_logger'), "缺少错误恢复日志器"
            assert hasattr(logger, 'silent_disconnect_logger'), "缺少静默断开日志器"
            assert hasattr(logger, 'subscription_failure_logger'), "缺少订阅失败日志器"
            
            # 测试日志目录创建
            assert logger.logs_dir.exists(), "日志目录未创建"
            
            self.test_results.append({"test": "websocket_logger_initialization", "status": "PASS", "message": "日志器初始化正常"})
            print("   ✅ 日志器初始化正常")
            return True
            
        except Exception as e:
            self.test_results.append({"test": "websocket_logger_initialization", "status": "FAIL", "message": f"日志器初始化失败: {e}"})
            print(f"   ❌ 日志器初始化失败: {e}")
            return False
    
    def test_log_function_parameters(self):
        """测试2：日志函数参数验证"""
        print("\n🧪 测试2: 日志函数参数验证")
        
        test_cases = [
            # 性能日志测试
            {
                "function": log_websocket_performance,
                "args": ("info", "测试性能日志"),
                "kwargs": {"latency_ms": 5.2, "throughput": 1500},
                "name": "性能日志"
            },
            # 连接日志测试
            {
                "function": log_websocket_connection,
                "args": ("info", "测试连接日志"),
                "kwargs": {"exchange": "gate", "status": "connected"},
                "name": "连接日志"
            },
            # 错误恢复日志测试
            {
                "function": log_websocket_error_recovery,
                "args": ("warning", "测试错误恢复日志"),
                "kwargs": {"exchange": "bybit", "error_type": "connection_lost"},
                "name": "错误恢复日志"
            },
            # 静默断开日志测试
            {
                "function": log_websocket_silent_disconnect,
                "args": ("error", "测试静默断开日志"),
                "kwargs": {"exchange": "okx", "silent_duration": 30},
                "name": "静默断开日志"
            },
            # 订阅失败日志测试
            {
                "function": log_websocket_subscription_failure,
                "args": ("error", "测试订阅失败日志"),
                "kwargs": {"exchange": "gate", "symbol": "BTC/USDT"},
                "name": "订阅失败日志"
            }
        ]
        
        passed_tests = 0
        for test_case in test_cases:
            try:
                # 调用日志函数
                test_case["function"](*test_case["args"], **test_case["kwargs"])
                print(f"   ✅ {test_case['name']}参数验证通过")
                passed_tests += 1
                
            except Exception as e:
                print(f"   ❌ {test_case['name']}参数验证失败: {e}")
                self.test_results.append({
                    "test": f"log_function_parameters_{test_case['name']}", 
                    "status": "FAIL", 
                    "message": f"{test_case['name']}参数验证失败: {e}"
                })
                return False
        
        if passed_tests == len(test_cases):
            self.test_results.append({"test": "log_function_parameters", "status": "PASS", "message": f"所有{len(test_cases)}个日志函数参数验证通过"})
            print(f"   ✅ 所有{len(test_cases)}个日志函数参数验证通过")
            return True
        
        return False
    
    def test_log_file_creation(self):
        """测试3：日志文件创建验证"""
        print("\n🧪 测试3: 日志文件创建验证")

        # 🔥 修复：使用实际的日志目录而不是临时目录进行测试
        logger = get_websocket_logger()
        actual_logs_dir = logger.logs_dir
        print(f"   实际日志目录: {actual_logs_dir}")

        # 记录一些测试日志
        log_websocket_performance("info", "测试性能日志文件创建", test_param="test_value")
        log_websocket_connection("info", "测试连接日志文件创建", exchange="test")
        log_websocket_error_recovery("warning", "测试错误恢复日志文件创建", error_type="test")
        log_websocket_silent_disconnect("error", "测试静默断开日志文件创建", exchange="test")
        log_websocket_subscription_failure("error", "测试订阅失败日志文件创建", exchange="test")

        # 🔥 修复：强制刷新所有日志处理器
        for handler in logger.performance_logger.handlers:
            handler.flush()
        for handler in logger.connection_logger.handlers:
            handler.flush()
        for handler in logger.error_recovery_logger.handlers:
            handler.flush()
        for handler in logger.silent_disconnect_logger.handlers:
            handler.flush()
        for handler in logger.subscription_failure_logger.handlers:
            handler.flush()

        # 等待日志写入
        time.sleep(0.2)

        # 检查日志文件是否创建
        from datetime import datetime
        date_str = datetime.now().strftime("%Y%m%d")

        expected_files = [
            f"websocket_performance_{date_str}.log",
            f"websocket_connection_{date_str}.log",
            f"websocket_error_recovery_{date_str}.log",
            f"websocket_silent_disconnect_{date_str}.log",
            f"websocket_subscription_failure_{date_str}.log"
        ]

        created_files = 0
        for filename in expected_files:
            filepath = actual_logs_dir / filename
            if filepath.exists() and filepath.stat().st_size > 0:
                print(f"   ✅ {filename} 创建成功，大小: {filepath.stat().st_size} 字节")
                created_files += 1
            else:
                print(f"   ❌ {filename} 创建失败或为空 (路径: {filepath})")

        if created_files == len(expected_files):
            self.test_results.append({"test": "log_file_creation", "status": "PASS", "message": f"所有{len(expected_files)}个日志文件创建成功"})
            print(f"   ✅ 所有{len(expected_files)}个日志文件创建成功")
            return True
        else:
            self.test_results.append({"test": "log_file_creation", "status": "FAIL", "message": f"只有{created_files}/{len(expected_files)}个日志文件创建成功"})
            print(f"   ❌ 只有{created_files}/{len(expected_files)}个日志文件创建成功")
            return False
    
    def test_error_handling(self):
        """测试4：错误处理验证"""
        print("\n🧪 测试4: 错误处理验证")
        
        try:
            # 测试无效参数处理
            log_websocket_performance(None, None)  # 传入None参数
            log_websocket_connection("", "")  # 传入空字符串
            log_websocket_error_recovery("invalid_level", "测试")  # 无效日志级别
            
            # 测试异常参数处理
            log_websocket_silent_disconnect("info", "测试", invalid_param={"complex": "object"})
            log_websocket_subscription_failure("info", "测试", exchange=123)  # 非字符串exchange
            
            print("   ✅ 错误处理验证通过 - 日志系统能够处理异常参数")
            self.test_results.append({"test": "error_handling", "status": "PASS", "message": "错误处理验证通过"})
            return True
            
        except Exception as e:
            print(f"   ❌ 错误处理验证失败: {e}")
            self.test_results.append({"test": "error_handling", "status": "FAIL", "message": f"错误处理验证失败: {e}"})
            return False
    
    def test_boundary_conditions(self):
        """测试5：边界条件验证"""
        print("\n🧪 测试5: 边界条件验证")
        
        try:
            # 测试极长消息
            long_message = "测试消息" * 1000  # 4000字符的长消息
            log_websocket_performance("info", long_message, test_param="long_message")
            
            # 测试大量参数
            many_params = {f"param_{i}": f"value_{i}" for i in range(100)}
            log_websocket_connection("info", "测试大量参数", **many_params)
            
            # 测试特殊字符
            special_message = "测试特殊字符: !@#$%^&*()_+-=[]{}|;':\",./<>?"
            log_websocket_error_recovery("info", special_message, special_param="特殊值")
            
            # 测试数值边界
            log_websocket_silent_disconnect("info", "测试数值边界", 
                                          silent_duration=999999999,  # 大数值
                                          small_value=0.000001)  # 小数值
            
            print("   ✅ 边界条件验证通过")
            self.test_results.append({"test": "boundary_conditions", "status": "PASS", "message": "边界条件验证通过"})
            return True
            
        except Exception as e:
            print(f"   ❌ 边界条件验证失败: {e}")
            self.test_results.append({"test": "boundary_conditions", "status": "FAIL", "message": f"边界条件验证失败: {e}"})
            return False
    
    def run_all_tests(self):
        """运行所有基础核心测试"""
        print("🚀 开始网络监控日志修复 - 基础核心测试")
        print("=" * 60)
        
        # 设置测试环境
        if not self.setup_test_environment():
            print("❌ 测试环境设置失败")
            return False
        
        try:
            # 运行所有测试
            tests = [
                self.test_websocket_logger_initialization,
                self.test_log_function_parameters,
                self.test_log_file_creation,
                self.test_error_handling,
                self.test_boundary_conditions
            ]
            
            passed_tests = 0
            for test in tests:
                if test():
                    passed_tests += 1
            
            # 输出测试结果
            print("\n" + "=" * 60)
            print("📊 基础核心测试结果汇总")
            print("=" * 60)
            
            for result in self.test_results:
                status_icon = "✅" if result["status"] == "PASS" else "❌"
                print(f"{status_icon} {result['test']}: {result['message']}")
            
            success_rate = (passed_tests / len(tests)) * 100
            print(f"\n📈 测试统计:")
            print(f"   总测试数: {len(tests)}")
            print(f"   通过: {passed_tests} ({success_rate:.1f}%)")
            print(f"   失败: {len(tests) - passed_tests}")
            
            if passed_tests == len(tests):
                print(f"\n🎉 基础核心测试 100% 通过！修复点本身100%稳定")
                return True
            else:
                print(f"\n⚠️ 基础核心测试未完全通过，需要修复")
                return False
                
        finally:
            # 清理测试环境
            self.cleanup_test_environment()

def main():
    """主函数"""
    tester = NetworkMonitoringCoreTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 网络监控日志修复基础核心测试完成 - 所有测试通过")
        return 0
    else:
        print("\n❌ 网络监控日志修复基础核心测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
