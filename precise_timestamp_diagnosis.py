#!/usr/bin/env python3
"""
精确时间戳诊断脚本
模拟失败场景，精准定位时间戳单位不一致问题
"""

import sys
import time
import os
from typing import Dict, Any

# 添加项目路径
sys.path.append('123')

def test_timestamp_unit_consistency():
    """测试时间戳单位一致性"""
    print("🔍 精确时间戳单位一致性诊断")
    print("=" * 60)

    # 获取当前时间
    current_time_s = time.time()
    current_time_ms = int(time.time() * 1000)

    print(f"当前时间(秒): {current_time_s}")
    print(f"当前时间(毫秒): {current_time_ms}")

    # 1. 测试统一时间戳处理器
    print("\n1️⃣ 统一时间戳处理器测试:")
    print("-" * 40)

    try:
        # 直接测试时间戳处理逻辑
        print("测试时间戳处理逻辑...")

        # 模拟统一时间戳处理器的行为
        def mock_get_synced_timestamp(exchange_name: str) -> int:
            return int(time.time() * 1000)  # 返回毫秒级时间戳

        # 测试各交易所时间戳
        exchanges = ['gate', 'bybit', 'okx']
        for exchange in exchanges:
            timestamp = mock_get_synced_timestamp(exchange)
            print(f"{exchange.upper()}: {timestamp}")
            print(f"  格式: {'毫秒级' if timestamp > 1e12 else '秒级'}")
            print(f"  与当前时间差: {abs(timestamp - current_time_ms):.1f}ms")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

    # 2. 测试MarketData时间戳
    print("\n2️⃣ MarketData时间戳测试:")
    print("-" * 40)
    
    try:
        # 模拟MarketData结构
        class MockMarketData:
            def __init__(self, exchange, symbol, price, timestamp, orderbook):
                self.exchange = exchange
                self.symbol = symbol
                self.price = price
                self.timestamp = timestamp
                self.orderbook = orderbook

        # 创建测试数据
        test_data = MockMarketData(
            exchange="gate",
            symbol="BTC-USDT",
            price=50000.0,
            timestamp=current_time_ms,  # 毫秒级
            orderbook={}
        )
        
        print(f"MarketData.timestamp: {test_data.timestamp}")
        print(f"格式: {'毫秒级' if test_data.timestamp > 1e12 else '秒级'}")
        
        # 模拟组合检测中的计算
        current_time_for_check = time.time()  # 秒级
        
        # 错误的计算（原始BUG）
        wrong_data_age = current_time_for_check - test_data.timestamp
        print(f"\n❌ 错误计算: {current_time_for_check} - {test_data.timestamp} = {wrong_data_age}")
        print(f"错误判断: data_age < 1 = {wrong_data_age < 1} (应该是False但实际是True)")
        
        # 正确的计算（修复后）
        timestamp_seconds = test_data.timestamp / 1000 if test_data.timestamp > 1e12 else test_data.timestamp
        correct_data_age = current_time_for_check - timestamp_seconds
        print(f"\n✅ 正确计算: {current_time_for_check} - {timestamp_seconds} = {correct_data_age}")
        print(f"正确判断: data_age < 1 = {correct_data_age < 1}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 3. 模拟实际失败场景
    print("\n3️⃣ 实际失败场景模拟:")
    print("-" * 40)
    
    # 模拟日志中的实际时间戳
    log_timestamps = [1753988900000, 1753988900250, 1753988897000, 1753990316266]
    
    for i, ts in enumerate(log_timestamps, 1):
        print(f"\n场景{i}: 时间戳 {ts}")
        
        # 模拟错误的组合检测逻辑
        current_time_s = time.time()
        wrong_age = current_time_s - ts
        
        print(f"  错误计算: {current_time_s} - {ts} = {wrong_age}")
        print(f"  错误结果: data_age < 1 = {wrong_age < 1} (错误判断为活跃)")
        
        # 正确的计算
        correct_age = current_time_s - (ts / 1000)
        print(f"  正确计算: {current_time_s} - {ts/1000} = {correct_age}")
        print(f"  正确结果: data_age < 1 = {correct_age < 1} (正确判断)")

def test_system_wide_timestamp_usage():
    """测试系统范围的时间戳使用"""
    print("\n🔍 系统范围时间戳使用测试")
    print("=" * 60)
    
    # 检查关键文件中的时间戳使用
    critical_patterns = [
        ("OpportunityScanner组合检测", "123/core/opportunity_scanner.py", 1475),
        ("ExecutionEngine数据验证", "123/core/execution_engine.py", 1404),
        ("DataSnapshotValidator", "123/core/data_snapshot_validator.py", 256),
    ]
    
    for name, file_path, line_num in critical_patterns:
        print(f"\n📋 {name}:")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if line_num <= len(lines):
                    code_line = lines[line_num - 1].strip()
                    print(f"  文件: {file_path}:{line_num}")
                    print(f"  代码: {code_line}")
                    
                    # 分析是否存在单位不一致
                    if "time.time()" in code_line and "timestamp" in code_line:
                        if "* 1000" not in code_line and "/ 1000" not in code_line:
                            print(f"  ⚠️  可能存在单位不一致问题")
                        else:
                            print(f"  ✅ 已有单位转换处理")
                    else:
                        print(f"  ℹ️  需要进一步检查上下文")
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n🎯 修复建议")
    print("=" * 60)
    
    recommendations = [
        {
            "问题": "MarketData.timestamp定义为float但应该是int",
            "文件": "123/core/opportunity_scanner.py:63",
            "修复": "timestamp: int  # 统一为毫秒级整数时间戳"
        },
        {
            "问题": "组合检测中秒级与毫秒级时间戳直接相减",
            "文件": "123/core/opportunity_scanner.py:1475",
            "修复": "timestamp_seconds = market_data.timestamp / 1000 if market_data.timestamp > 1e12 else market_data.timestamp"
        },
        {
            "问题": "ExecutionEngine中orderbook时间戳单位不确定",
            "文件": "123/core/execution_engine.py:2391",
            "修复": "添加时间戳单位检查和转换逻辑"
        },
        {
            "问题": "系统缺乏统一的时间戳验证函数",
            "文件": "全系统",
            "修复": "创建统一的时间戳单位检查和转换函数"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec['问题']}")
        print(f"   文件: {rec['文件']}")
        print(f"   修复: {rec['修复']}")

def main():
    """主函数"""
    print("🚀 开始精确时间戳诊断...")
    
    test_timestamp_unit_consistency()
    test_system_wide_timestamp_usage()
    generate_fix_recommendations()
    
    print("\n" + "=" * 60)
    print("✅ 诊断完成！")
    print("📋 关键发现:")
    print("1. 统一时间戳处理器返回毫秒级时间戳")
    print("2. MarketData.timestamp存储毫秒级时间戳")
    print("3. 组合检测中使用time.time()（秒级）与毫秒级时间戳直接比较")
    print("4. 导致data_age计算出巨大负数，错误判断为活跃")
    print("5. 需要在所有时间差计算处添加单位转换逻辑")

if __name__ == "__main__":
    main()
