{"websocket_log_analysis": {"websocket_connection_20250731.log": {"exists": true, "size_bytes": 206, "line_count": 2, "is_empty": false, "sample_content": ["2025-07-31 20:56:25,913 [CONN] INFO - [ALL] WebSocket管理器启动 | {'event_type': 'manager_start'}", "2025-07-31 20:56:50,902 [CONN] INFO - [ALL] WebSocket管理器启动 | {'event_type': 'manager_start'}"]}, "websocket_error_recovery_20250731.log": {"exists": true, "size_bytes": 0, "line_count": 0, "is_empty": true, "sample_content": []}, "websocket_performance_20250731.log": {"exists": true, "size_bytes": 0, "line_count": 0, "is_empty": true, "sample_content": []}, "websocket_silent_disconnect_20250731.log": {"exists": true, "size_bytes": 0, "line_count": 0, "is_empty": true, "sample_content": []}, "websocket_subscription_failure_20250731.log": {"exists": true, "size_bytes": 0, "line_count": 0, "is_empty": true, "sample_content": []}}, "timestamp_log_analysis": {"found_mentions": false, "files_with_mentions": {}, "total_log_files_scanned": 13}, "code_analysis": {"websocket_logger_usage": true, "timestamp_logging": true, "performance_logging": true, "error_recovery_logging": true}, "critical_gaps": [{"type": "EMPTY_LOG_FILE", "severity": "CRITICAL", "file": "websocket_error_recovery_20250731.log", "description": "websocket_error_recovery_20250731.log为空，无任何监控记录"}, {"type": "EMPTY_LOG_FILE", "severity": "CRITICAL", "file": "websocket_performance_20250731.log", "description": "websocket_performance_20250731.log为空，无任何监控记录"}, {"type": "EMPTY_LOG_FILE", "severity": "CRITICAL", "file": "websocket_silent_disconnect_20250731.log", "description": "websocket_silent_disconnect_20250731.log为空，无任何监控记录"}, {"type": "EMPTY_LOG_FILE", "severity": "CRITICAL", "file": "websocket_subscription_failure_20250731.log", "description": "websocket_subscription_failure_20250731.log为空，无任何监控记录"}, {"type": "NO_TIMESTAMP_LOGS", "severity": "CRITICAL", "description": "完全没有时间戳丢弃、数据新鲜度、时间戳同步相关的日志记录"}], "recommendations": ["激活WebSocket专用日志记录，确保所有监控事件都被记录", "添加时间戳处理相关的日志记录，包括数据丢弃和同步失败"]}