{"issues_found": [{"type": "配置参数名称不匹配", "severity": "HIGH", "description": ".env使用SCAN_INTERVAL，代码使用OPPORTUNITY_SCAN_INTERVAL", "env_config": "SCAN_INTERVAL", "code_config": "OPPORTUNITY_SCAN_INTERVAL"}, {"type": "硬编码扫描间隔", "severity": "CRITICAL", "description": "_market_data_loop中硬编码0.3秒，未使用配置", "location": "第1696行"}], "recommendations": ["统一配置参数名称为SCAN_INTERVAL", "移除硬编码，使用self.scan_interval配置"], "config_analysis": {"env_file": {"SCAN_INTERVAL": {"value": "0.3                 # 扫描间隔(秒) - 🔥 修复：统一扫描间隔，300ms间隔", "line": 91}, "LOG_INTERVAL": {"value": "0.01                 # 日志记录间隔(秒) - 🔥 优化：真实日志，10ms间隔，显示100%数据", "line": 92}}}, "code_analysis": {"opportunity_scanner": {"OPPORTUNITY_SCAN_INTERVAL": {"line": 233, "content": "self.logger.warning(\"OPPORTUNITY_SCAN_INTERVAL配置无效，使用默认值0.2秒\")"}, "HARDCODED_SLEEP": {"line": 1696, "content": "await asyncio.sleep(0.3)  # 🔥 修复：错误处理也使用0.3秒间隔，保持一致性"}}}}