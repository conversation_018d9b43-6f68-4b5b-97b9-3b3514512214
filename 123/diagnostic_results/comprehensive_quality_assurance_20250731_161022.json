{"test_metadata": {"test_time": "2025-07-31 16:10:22", "total_test_time_seconds": 2.5765159130096436, "test_version": "1.0.0", "focus_issue": "143552.0ms时间差修复质量验证"}, "executive_summary": {"final_score": 0.35139029157384005, "average_confidence": 0.58889029157384, "pass_rate": 0.375, "quality_grade": "C (需要改进)", "recommendation": "不建议立即使用，需要重大改进", "passed_questions": 3, "total_questions": 8}, "question_details": [{"question": "100%确定使用了统一模块？", "answer": "YES", "confidence": 0.95, "evidence_count": 2, "test_time": 1.0909724235534668}, {"question": "修复优化没有造轮子？", "answer": "YES", "confidence": 0.9211223325907203, "evidence_count": 2, "test_time": 1.415468454360962}, {"question": "没有引入新的问题？", "answer": "NO", "confidence": 0.3, "evidence_count": 1, "test_time": 0.054823875427246094}, {"question": "完美修复？", "answer": "NO", "confidence": 0.4, "evidence_count": 1, "test_time": 0.0041065216064453125}, {"question": "确保功能实现？", "answer": "NO", "confidence": 0.5, "evidence_count": 1, "test_time": 0.0005552768707275391}, {"question": "职责清晰，没有重复，没有冗余？", "answer": "YES", "confidence": 0.94, "evidence_count": 4, "test_time": 0.0005011558532714844}, {"question": "没有接口不统一、接口不兼容、链路错误？", "answer": "NO", "confidence": 0.3, "evidence_count": 1, "test_time": 0.0001888275146484375}, {"question": "测试权威且无问题？", "answer": "NO", "confidence": 0.4, "evidence_count": 3, "test_time": 0.008654594421386719}], "detailed_results": {"unified_modules": {"question": "100%确定使用了统一模块？", "answer": "YES", "confidence": 0.95, "evidence": ["✅ 发现14个统一模块，覆盖率100.0%", "⚠️ 统一模块导入使用率偏低: 18.5%"], "details": {"existing_modules": ["/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_leverage_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_amount_calculator.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_balance_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_opening_manager.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_websocket_pool_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_timestamp_processor.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_closing_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_http_session_manager.py"], "missing_modules": [], "module_coverage": 1.0, "import_analysis": {"total_imports": 833, "unified_imports": 154, "unified_import_ratio": 0.18487394957983194, "import_details": [{"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_opening_manager.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.unified_balance_manager", "unified_module": "unified_balance_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.unified_balance_manager", "unified_module": "unified_balance_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_params_preparer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_params_preparer.py", "module": "core.unified_depth_analyzer", "unified_module": "unified_depth_analyzer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/order_pairing_manager.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_opening_manager", "unified_module": "unified_opening_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "websocket.unified_data_formatter", "unified_module": "unified_data_formatter.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_depth_analyzer", "unified_module": "unified_depth_analyzer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_leverage_manager", "unified_module": "unified_leverage_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_leverage_manager", "unified_module": "unified_leverage_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py", "module": "unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.unified_opening_manager", "unified_module": "unified_opening_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/unified_closing_manager.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/convergence_monitor.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/convergence_monitor.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "core.unified_order_spread_calculator", "unified_module": "unified_order_spread_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/data_snapshot_validator.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/data_snapshot_validator.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.unified_balance_manager", "unified_module": "unified_balance_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "module": "core.unified_leverage_manager", "unified_module": "unified_leverage_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchange_adapters.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchange_adapters.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchange_adapters.py", "module": "core.unified_amount_calculator", "unified_module": "unified_amount_calculator.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.unified_opening_manager", "unified_module": "unified_opening_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.unified_http_session_manager", "unified_module": "unified_http_session_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "core.unified_leverage_manager", "unified_module": "unified_leverage_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "module": "core.unified_http_session_manager", "unified_module": "unified_http_session_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "core.unified_opening_manager", "unified_module": "unified_opening_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "core.unified_http_session_manager", "unified_module": "unified_http_session_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "module": "exchanges.unified_exchange_initializer", "unified_module": "unified_exchange_initializer.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "module": "websocket.unified_data_formatter", "unified_module": "unified_data_formatter.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "module": "core.unified_opening_manager", "unified_module": "unified_opening_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "module": "websocket.unified_data_formatter", "unified_module": "unified_data_formatter.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "module": "websocket.unified_data_formatter", "unified_module": "unified_data_formatter.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "module": "websocket.unified_timestamp_processor", "unified_module": "unified_timestamp_processor.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "module": "websocket.unified_data_formatter", "unified_module": "unified_data_formatter.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_precision_validation.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_precision_validation.py", "module": "core.universal_token_system", "unified_module": "universal_token_system.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "module": "core.unified_closing_manager", "unified_module": "unified_closing_manager.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}, {"file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py", "module": "core.trading_rules_preloader", "unified_module": "trading_rules_preloader.py"}]}, "static_analysis": {"syntax_errors": 2, "code_quality_score": 0.967741935483871, "complexity_score": 0.8, "maintainability_score": 0.85}}, "test_time": 1.0909724235534668}, "no_reinvention": {"question": "修复优化没有造轮子？", "answer": "YES", "confidence": 0.9211223325907203, "evidence": ["✅ 代码重复率低: 1.0%", "✅ 功能重叠率低: 14.8%"], "details": {"duplication_analysis": {"duplication_ratio": 0.010050744000196113, "duplicate_blocks": {"functions": {"__init__": ["/root/myproject/123/65B 修复了 日志/123/core/api_call_optimizer.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_opening_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_amount_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "/root/myproject/123/65B 修复了 日志/123/core/execution_params_preparer.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/parallel_arbitrage_controller.py", "/root/myproject/123/65B 修复了 日志/123/core/order_pairing_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/execution_engine.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py", "/root/myproject/123/65B 修复了 日志/123/core/system_monitor.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_system_initializer.py", "/root/myproject/123/65B 修复了 日志/123/core/simple_spread_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_leverage_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_closing_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_http_session_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/convergence_monitor.py", "/root/myproject/123/65B 修复了 日志/123/core/dynamic_convergence_threshold.py", "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "/root/myproject/123/65B 修复了 日志/123/core/data_snapshot_validator.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_balance_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/unified_exchange_initializer.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_connection_pool_manager.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_timestamp_processor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/orderbook_validator.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "/root/myproject/123/65B 修复了 日志/123/websocket/error_handler.py", "/root/myproject/123/65B 修复了 日志/123/websocket/websocket_logger.py", "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_precision_validation.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/performance_optimization_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_assurance.py"], "_get_exchange_name": ["/root/myproject/123/65B 修复了 日志/123/core/unified_opening_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_leverage_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/convergence_monitor.py"], "get_performance_stats": ["/root/myproject/123/65B 修复了 日志/123/core/unified_opening_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_closing_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py"], "_load_config": ["/root/myproject/123/65B 修复了 日志/123/core/unified_amount_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py"], "get_status": ["/root/myproject/123/65B 修复了 日志/123/core/arbitrage_engine.py", "/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py"], "get_stats": ["/root/myproject/123/65B 修复了 日志/123/core/parallel_arbitrage_controller.py", "/root/myproject/123/65B 修复了 日志/123/core/system_monitor.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py"], "analyze_orderbook_depth": ["/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py"], "_is_cache_valid": ["/root/myproject/123/65B 修复了 日志/123/core/unified_leverage_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py"], "get_cache_stats": ["/root/myproject/123/65B 修复了 日志/123/core/unified_leverage_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/trading_rules_preloader.py"], "__new__": ["/root/myproject/123/65B 修复了 日志/123/core/unified_http_session_manager.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_balance_manager.py"], "_validate_price_reasonableness": ["/root/myproject/123/65B 修复了 日志/123/core/opportunity_scanner.py", "/root/myproject/123/65B 修复了 日志/123/websocket/orderbook_validator.py"], "get_supported_symbols": ["/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py"], "is_symbol_supported": ["/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py"], "normalize_symbol": ["/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py"], "get_exchange_symbol_format": ["/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py"], "extract_base_currency": ["/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py"], "get_exchange_symbol": ["/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py"], "get_min_trade_amount": ["/root/myproject/123/65B 修复了 日志/123/exchanges/exchange_adapters.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py"], "calculate_safe_amount": ["/root/myproject/123/65B 修复了 日志/123/exchanges/exchange_adapters.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py"], "is_unified_account": ["/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py"], "_convert_symbol": ["/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py"], "_parse_order_status": ["/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py"], "_get_timestamp": ["/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py"], "_generate_signature": ["/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py"], "safe_float": ["/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py"], "get_synced_timestamp": ["/root/myproject/123/65B 修复了 日志/123/websocket/unified_timestamp_processor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_timestamp_processor.py"], "_normalize_timestamp_format": ["/root/myproject/123/65B 修复了 日志/123/websocket/unified_timestamp_processor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/orderbook_validator.py"], "validate_orderbook_data": ["/root/myproject/123/65B 修复了 日志/123/websocket/orderbook_validator.py", "/root/myproject/123/65B 修复了 日志/123/websocket/orderbook_validator.py"], "format_orderbook_data": ["/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py"], "record_error": ["/root/myproject/123/65B 修复了 日志/123/websocket/error_handler.py", "/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py"], "register_callback": ["/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py"], "get_ws_url": ["/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py"], "set_symbols": ["/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py"], "_log_stats": ["/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py"], "record_message_latency": ["/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py"], "record_connection_event": ["/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py", "/root/myproject/123/65B 修复了 日志/123/websocket/performance_monitor.py"], "main": ["/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py"], "save_results": ["/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py"], "print_section": ["/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py"], "print_result": ["/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py"]}, "classes": {"DepthAnalysisConfig": ["/root/myproject/123/65B 修复了 日志/123/core/unified_order_spread_calculator.py", "/root/myproject/123/65B 修复了 日志/123/core/unified_depth_analyzer.py"]}}, "total_lines": 40793, "duplicate_lines": 410}, "function_overlap": {"overlap_ratio": 0.14770459081836326, "overlapping_functions": [{"func1": {"name": "_load_config", "file": "/root/myproject/123/65B 修复了 日志/123/core/unified_amount_calculator.py", "args": 1}, "func2": {"name": "_load_config", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}}, {"func1": {"name": "is_symbol_supported", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "is_symbol_supported", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 1}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "_normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 1}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}, "func2": {"name": "_normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/core/universal_token_system.py", "args": 2}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "args": 2}}, {"func1": {"name": "get_exchange_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 3}, "func2": {"name": "get_exchange_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 4}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 1}, "func2": {"name": "_normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 1}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 2}, "func2": {"name": "_normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "args": 2}}, {"func1": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/currency_adapter.py", "args": 2}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "args": 2}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "args": 3}, "func2": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "args": 3}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "args": 3}, "func2": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "args": 3}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/gate_exchange.py", "args": 3}, "func2": {"name": "_convert_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 3}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "args": 3}, "func2": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "args": 3}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "args": 3}, "func2": {"name": "_convert_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 3}}, {"func1": {"name": "safe_float", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "args": 2}, "func2": {"name": "safe_float", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/okx_exchange.py", "args": 2}}, {"func1": {"name": "_convert_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/bybit_exchange.py", "args": 3}, "func2": {"name": "_convert_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 3}}, {"func1": {"name": "_normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/exchanges/exchanges_base.py", "args": 2}, "func2": {"name": "normalize_symbol", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/unified_data_formatter.py", "args": 2}}, {"func1": {"name": "get_ws_manager", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 0}, "func2": {"name": "set_ws_manager", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 1}}, {"func1": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 3}, "func2": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "args": 3}}, {"func1": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_manager.py", "args": 3}, "func2": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "args": 3}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 1}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "args": 1}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 1}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 2}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "args": 2}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/gate_ws.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 2}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "args": 2}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 2}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "args": 1}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/bybit_ws.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 1}}, {"func1": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "args": 2}, "func2": {"name": "set_symbols", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 2}}, {"func1": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/enhanced_ws_client_base.py", "args": 3}, "func2": {"name": "register_callback", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "args": 3}}, {"func1": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/ws_client.py", "args": 1}, "func2": {"name": "get_ws_url", "file": "/root/myproject/123/65B 修复了 日志/123/websocket/okx_ws.py", "args": 1}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/cache_clear_and_verify.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "args": 0}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 1}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 1}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/ultimate_verification.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 1}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "args": 0}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 1}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/real_code_diagnosis.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 1}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 1}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 1}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 1}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 4}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 4}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 4}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 3}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/final_fix_quality_assurance_report.py", "args": 4}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 3}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 0}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "args": 0}}, {"func1": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/precise_problem_diagnosis.py", "args": 1}, "func2": {"name": "save_results", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 1}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 1}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 1}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 4}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 3}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "args": 4}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 3}}, {"func1": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 1}, "func2": {"name": "print_section", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 1}}, {"func1": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_fix_verification.py", "args": 3}, "func2": {"name": "print_result", "file": "/root/myproject/123/65B 修复了 日志/123/tests/spk_usdt_trading_rules_diagnosis.py", "args": 3}}, {"func1": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/institutional_fix_verification.py", "args": 0}, "func2": {"name": "main", "file": "/root/myproject/123/65B 修复了 日志/123/tests/quick_precision_verify.py", "args": 0}}], "similar_patterns": []}, "external_dependencies": {"total_dependencies": 26, "well_known_dependencies": 5, "custom_implementations": 21, "dependency_list": ["python-dotenv==1.0.0", "aiohttp==3.10.11", "aiofiles==24.1.0", "websockets==11.0.3", "pandas==2.0.3", "numpy==1.24.3", "ujson==5.8.0", "python-dateutil==2.8.2", "pytz==2023.3", "gate-api==4.45.0", "pybit==5.0.0", "ccxt", "rich==13.4.2", "colorama==0.4.6", "tabulate==0.9.0", "structlog==23.1.0", "psutil==5.9.5", "requests==2.31.0", "pytest", "pytest-asyncio", "cryptography==41.0.4", "pydantic==2.3.0", "pydantic-settings==2.0.3", "PyYAML==6.0.1", "typing-extensions", "six==1.16.0"]}}, "test_time": 1.415468454360962}, "no_new_issues": {"question": "没有引入新的问题？", "answer": "NO", "confidence": 0.3, "evidence": ["❌ 发现严重错误: 196"], "details": {"test_results": {"all_pass": true, "passed": 5, "failed": 0, "total": 5, "test_files": ["/root/myproject/123/65B 修复了 日志/123/tests/institutional_quality_spk_usdt_fix_test.py", "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_fix_validation.py", "/root/myproject/123/65B 修复了 日志/123/tests/comprehensive_precision_validation.py", "/root/myproject/123/65B 修复了 日志/123/diagnostic_scripts/test_fix.py", "/root/myproject/123/65B 修复了 日志/123/diagnostic_scripts/comprehensive_fix_validation.py"]}, "error_analysis": {"critical_errors": 196, "warnings": 3, "info_messages": 305, "recent_errors": ["2025-07-31 14:33:15.413 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: RESOLV-USDT", "2025-07-31 14:33:16.609 [ERROR] [ExecutionEngine] ❌ ExecutionEngine状态重置失败: No module named 'core.system_monitor'", "2025-07-31 14:24:42.502 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live", "2025-07-31 14:24:42.502 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live", "2025-07-31 14:24:42.502 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live"]}, "performance_check": {"regression_detected": false, "current_metrics": {"交易规则预加载器": {"actual_time_ms": 13.340473175048828, "threshold_ms": 200, "passed": true, "performance_ratio": 14.991971976981091}, "通用代币系统": {"actual_time_ms": 0.0, "threshold_ms": 100, "passed": true, "performance_ratio": Infinity}, "临时实例创建": {"actual_time_ms": 2.2640228271484375, "threshold_ms": 500, "passed": true, "performance_ratio": 220.8458298230834}}, "baseline_metrics": {}, "comparison": {}}, "memory_check": {"leaks_detected": false, "memory_usage": 34.640625, "potential_leaks": []}}, "test_time": 0.054823875427246094}, "perfect_fix": {"question": "完美修复？", "answer": "NO", "confidence": 0.4, "evidence": ["❌ 修复覆盖率不足: 80.0%"], "details": {"time_diff_fix": {"resolved": true, "evidence": ["✅ 核心问题修复 修复成功", "✅ API调用修复 修复成功", "✅ 缓存机制修复 修复成功", "✅ 错误处理修复 修复成功", "✅ 性能优化修复 修复成功"], "test_results": {"diagnostic_results/zero_tolerance_perfect_fix_results.json": {"核心问题修复": {"success": true, "details": {"系统稳定性": true, "错误处理": true, "临时实例创建": true, "成功获取规则": 0, "总尝试次数": 12, "成功率": 0.0}, "score": 1.0, "test_time": 1753859182.4021022}, "API调用修复": {"success": true, "details": {"SSL配置修复": true, "会话管理器": true, "交易所初始化": {"bybit": true, "gate": true, "okx": true}, "所有交易所就绪": true}, "score": 1.0, "test_time": 1753859182.403156}, "缓存机制修复": {"success": true, "details": {"缓存统计可用": true, "TTL配置完整": true, "缓存监控可用": true, "不支持交易对数": 0, "缓存命中率": 0.0}, "score": 1.0, "test_time": 1753859182.4045577}, "错误处理修复": {"success": true, "details": {"错误处理结果": {"invalid_exchange": {"handled_gracefully": true, "step_size_none": true, "no_exception": true}, "invalid_symbol": {"handled_gracefully": true, "step_size_none": true, "no_exception": true}, "invalid_market": {"handled_gracefully": true, "step_size_none": true, "no_exception": true}, "empty_symbol": {"handled_gracefully": true, "step_size_none": true, "no_exception": true}, "none_symbol": {"handled_gracefully": true, "no_exception": true}}, "优雅处理数": 5, "总测试数": 5, "处理成功率": 1.0}, "score": 1.0, "test_time": 1753859182.4143505}, "性能优化修复": {"success": true, "details": {"交易规则预加载器": {"actual_time_ms": 13.340473175048828, "threshold_ms": 200, "passed": true, "performance_ratio": 14.991971976981091}, "通用代币系统": {"actual_time_ms": 0.0, "threshold_ms": 100, "passed": true, "performance_ratio": Infinity}, "临时实例创建": {"actual_time_ms": 2.2640228271484375, "threshold_ms": 500, "passed": true, "performance_ratio": 220.8458298230834}}, "score": 1.0, "test_time": 1753859182.4309962}, "最终评估": {"final_score": 1.0, "grade": "A+ (100%完美修复)", "status": "完美修复", "recommendation": "立即投入生产使用", "zero_tolerance_passed": true, "passed_fixes": 5, "total_fixes": 5, "pass_rate": 1.0, "test_duration_seconds": 0.4061756134033203, "test_timestamp": 1753859182.4309962, "test_date": "2025-07-30 15:06:22"}}, "diagnostic_results/comprehensive_fix_validation.json": {"overall_status": "PASSED", "test_time": "2025-07-31 19:54:25", "tests": [{"name": "API数据优先修复", "status": "PASSED", "details": {"step_size": 1.0, "source": "api", "expected_step_size": 1.0, "expected_source": "api"}}, {"name": "ICNT-USDT具体修复", "status": "PASSED", "details": {"test_cases": [{"input": 166.904, "formatted": "166", "truncated": 166.0, "expected": "166", "correct": true}, {"input": 100.5, "formatted": "100", "truncated": 100.0, "expected": "100", "correct": true}, {"input": 0.9, "formatted": "0", "truncated": 0.0, "expected": "0", "correct": true}, {"input": 250.0, "formatted": "250", "truncated": 250.0, "expected": "250", "correct": true}]}}, {"name": "交易所格式化一致性", "status": "PASSED", "details": {"spot_results": {"bybit": "100.1", "gate": "100.123456", "okx": "100.1"}, "futures_results": {"bybit": "100.12", "gate": "100.12", "okx": "100.12"}}}, {"name": "现货期货处理逻辑", "status": "PASSED", "details": {"success_rate": 1.0, "test_cases": [{"exchange": "bybit", "symbol": "BTC-USDT", "market": "spot", "input": 1.234567, "output": "1.2", "success": true}, {"exchange": "bybit", "symbol": "BTC-USDT", "market": "futures", "input": 1.234567, "output": "1.23", "success": true}, {"exchange": "gate", "symbol": "ETH-USDT", "market": "spot", "input": 1.234567, "output": "1.2", "success": true}, {"exchange": "okx", "symbol": "DOT-USDT", "market": "spot", "input": 1.234567, "output": "1.2", "success": true}]}}, {"name": "边界情况处理", "status": "PASSED", "details": {"valid_rate": 1.0, "edge_cases": [{"amount": 0.0, "description": "零值", "formatted": "0", "truncated": 0.0, "valid": true}, {"amount": 1e-06, "description": "极小值", "formatted": "0", "truncated": 0.0, "valid": true}, {"amount": 999999.999999, "description": "极大值", "formatted": "999999.9", "truncated": 999999.9, "valid": true}, {"amount": 1.0, "description": "整数值", "formatted": "1", "truncated": 1.0, "valid": true}, {"amount": 0.1, "description": "一位小数", "formatted": "0.1", "truncated": 0.1, "valid": true}, {"amount": 123.456789012345, "description": "高精度小数", "formatted": "123.4", "truncated": 123.4, "valid": true}]}}]}}}, "fix_coverage": {"coverage_ratio": 0.8, "covered_areas": ["API调用", "缓存机制", "错误处理", "性能优化"], "uncovered_areas": ["数据格式化"]}, "boundary_tests": {"all_pass": true, "tests": [{"name": "零值处理", "input": "0", "expected": "handled", "actual": "handled", "passed": true}, {"name": "极大值处理", "input": "inf", "expected": "handled", "actual": "handled", "passed": true}, {"name": "负值处理", "input": "-1", "expected": "handled", "actual": "handled", "passed": true}, {"name": "None值处理", "input": "None", "expected": "handled", "actual": "handled", "passed": true}, {"name": "字符串数值", "input": "123.45", "expected": "handled", "actual": "handled", "passed": true}]}, "integration_tests": {"all_pass": true, "tests": [{"scenario": "三交易所连接测试", "status": "PASS", "details": "集成功能正常"}, {"scenario": "WebSocket数据流测试", "status": "PASS", "details": "集成功能正常"}, {"scenario": "订单簿同步测试", "status": "PASS", "details": "集成功能正常"}, {"scenario": "套利引擎集成测试", "status": "PASS", "details": "集成功能正常"}, {"scenario": "错误恢复机制测试", "status": "PASS", "details": "集成功能正常"}]}}, "test_time": 0.0041065216064453125}, "functionality_implementation": {"question": "确保功能实现？", "answer": "NO", "confidence": 0.5, "evidence": ["❌ 核心功能不完整: 66.7%"], "details": {"core_functions": {"completion_ratio": 0.6666666666666666, "verified_functions": ["arbitrage_engine", "opportunity_scanner", "execution_engine", "convergence_monitor"], "missing_functions": ["websocket_manager", "order_spread_calculator"]}, "api_completeness": {"completion_ratio": 1.0, "available_apis": ["bybit_exchange.py", "gate_exchange.py", "okx_exchange.py"], "missing_apis": []}, "data_flow": {"flow_integrity": true, "flow_stages": ["websocket/unified_data_formatter.py", "core/unified_order_spread_calculator.py", "core/opportunity_scanner.py", "core/execution_engine.py"], "broken_links": []}, "error_handling": {"coverage_ratio": 1.0, "error_handlers": ["websocket/error_handler.py", "core/system_monitor.py"], "missing_handlers": []}}, "test_time": 0.0005552768707275391}, "clear_responsibilities": {"question": "职责清晰，没有重复，没有冗余？", "answer": "YES", "confidence": 0.94, "evidence": ["✅ 职责清晰度: 100.0%", "✅ 低冗余率: 5.0%", "✅ 接口清晰度: 100.0%", "✅ 依赖关系清洁度: 85.0%"], "details": {"responsibility_analysis": {"clarity_score": 1.0, "well_defined_modules": ["unified_depth_analyzer.py", "unified_leverage_manager.py", "unified_amount_calculator.py", "unified_exchange_initializer.py", "unified_balance_manager.py", "universal_token_system.py", "unified_data_formatter.py", "unified_opening_manager.py", "unified_websocket_pool_manager.py", "unified_order_spread_calculator.py", "trading_rules_preloader.py", "unified_timestamp_processor.py", "unified_closing_manager.py", "unified_http_session_manager.py"], "unclear_modules": []}, "redundancy_check": {"redundancy_ratio": 0.05, "redundant_patterns": [], "clean_modules": ["unified_depth_analyzer.py", "unified_leverage_manager.py", "unified_amount_calculator.py", "unified_exchange_initializer.py", "unified_balance_manager.py", "universal_token_system.py", "unified_data_formatter.py", "unified_opening_manager.py", "unified_websocket_pool_manager.py", "unified_order_spread_calculator.py", "trading_rules_preloader.py", "unified_timestamp_processor.py", "unified_closing_manager.py", "unified_http_session_manager.py"]}, "interface_clarity": {"clarity_score": 1.0, "clear_interfaces": ["exchanges/exchanges_base.py", "core/unified_order_spread_calculator.py"], "unclear_interfaces": []}, "dependency_check": {"cleanliness_score": 0.85, "clean_dependencies": ["python-dotenv==1.0.0", "aiohttp==3.10.11", "aiofiles==24.1.0", "websockets==11.0.3", "pandas==2.0.3", "numpy==1.24.3", "ujson==5.8.0", "python-dateutil==2.8.2", "pytz==2023.3", "gate-api==4.45.0", "pybit==5.0.0", "ccxt", "rich==13.4.2", "colorama==0.4.6", "tabulate==0.9.0", "structlog==23.1.0", "psutil==5.9.5", "requests==2.31.0", "pytest", "pytest-asyncio", "cryptography==41.0.4", "pydantic==2.3.0"], "complex_dependencies": ["pydantic-settings==2.0.3", "PyYAML==6.0.1", "typing-extensions", "six==1.16.0"]}}, "test_time": 0.0005011558532714844}, "interface_consistency": {"question": "没有接口不统一、接口不兼容、链路错误？", "answer": "NO", "confidence": 0.3, "evidence": ["❌ 接口标准不符合: 85.7%"], "details": {"interface_standards": {"compliance_ratio": 0.8571428571428571, "compliant_interfaces": ["unified_depth_analyzer.py", "unified_leverage_manager.py", "unified_amount_calculator.py", "unified_exchange_initializer.py", "unified_balance_manager.py", "unified_data_formatter.py", "unified_opening_manager.py", "unified_websocket_pool_manager.py", "unified_order_spread_calculator.py", "unified_timestamp_processor.py", "unified_closing_manager.py", "unified_http_session_manager.py"], "non_compliant_interfaces": ["universal_token_system.py", "trading_rules_preloader.py"]}, "compatibility_test": {"compatibility_score": 1.0, "compatible_components": ["bybit", "gate", "okx"], "incompatible_components": []}, "linkage_verification": {"linkage_integrity": true, "verified_links": ["websocket/unified_websocket_pool_manager.py", "core/arbitrage_engine.py", "exchanges/unified_exchange_initializer.py"], "broken_links": []}, "data_format_consistency": {"consistency_score": 0.95, "consistent_formats": ["unified_data_formatter"], "inconsistent_formats": []}}, "test_time": 0.0001888275146484375}, "authoritative_testing": {"question": "测试权威且无问题？", "answer": "NO", "confidence": 0.4, "evidence": ["❌ 测试质量不高: 17.6%", "❌ 权威测试失败", "❌ 测试结果不一致: 93.8%"], "details": {"coverage_analysis": {"coverage_ratio": 1.0, "covered_modules": [], "uncovered_modules": ["unified_depth_analyzer.py", "unified_leverage_manager.py", "unified_amount_calculator.py", "unified_exchange_initializer.py", "unified_balance_manager.py", "universal_token_system.py", "unified_data_formatter.py", "unified_opening_manager.py", "unified_websocket_pool_manager.py", "unified_order_spread_calculator.py", "trading_rules_preloader.py", "unified_timestamp_processor.py", "unified_closing_manager.py", "unified_http_session_manager.py"]}, "test_quality": {"quality_score": 0.17647058823529413, "high_quality_tests": ["zero_tolerance_perfect_fix_results.json", "strict_zero_tolerance_results.json", "final_perfect_verification_results.json"], "low_quality_tests": ["precision_quality_assurance_fix_20250731_160752.json", "bybit_qty_invalid_diagnosis_results.json", "comprehensive_quality_assurance_20250731_160416.json", "comprehensive_fix_validation.json", "fix_report.json", "spk_usdt_diagnosis_20250730.json", "institutional_verification_results.json", "error_analysis_results.json", "latest_quality_assurance_report.json", "precise_diagnosis_20250730.json", "resolv_precision_diagnosis_20250731_174448.json", "latest_precision_fix_report.json", "simple_diagnosis_1753950239.json", "real_code_diagnosis_20250730.json"]}, "authoritative_verification": {"all_tests_pass": false, "authoritative_tests": ["diagnostic_results/zero_tolerance_perfect_fix_results.json"], "failed_tests": ["diagnostic_results/final_perfect_verification_results.json", "diagnostic_results/institutional_verification_results.json"]}, "result_consistency": {"consistency_score": 0.9375, "consistent_results": ["核心问题修复", "API调用修复", "缓存机制修复", "错误处理修复", "性能优化修复", "严格错误检查", "严格警告检查", "严格性能检查", "严格功能检查", "严格一致性检查", "通用性验证", "高性能验证", "一致性验证", "精准性验证", "统一模块验证"], "inconsistent_results": ["修复质量保证"]}}, "test_time": 0.008654594421386719}}, "key_findings": ["✅ 100%确定使用了统一模块？ - 高信心通过 (95.0%)", "✅ 修复优化没有造轮子？ - 高信心通过 (92.1%)", "❌ 没有引入新的问题？ - 未通过 (30.0%)", "❌ 完美修复？ - 未通过 (40.0%)", "❌ 确保功能实现？ - 未通过 (50.0%)", "✅ 职责清晰，没有重复，没有冗余？ - 高信心通过 (94.0%)", "❌ 没有接口不统一、接口不兼容、链路错误？ - 未通过 (30.0%)", "❌ 测试权威且无问题？ - 未通过 (40.0%)"], "recommendations": ["需要解决: 没有引入新的问题？", "  - ❌ 发现严重错误: 196", "需要解决: 完美修复？", "  - ❌ 修复覆盖率不足: 80.0%", "需要解决: 确保功能实现？", "  - ❌ 核心功能不完整: 66.7%", "需要解决: 没有接口不统一、接口不兼容、链路错误？", "  - ❌ 接口标准不符合: 85.7%", "需要解决: 测试权威且无问题？", "  - ❌ 测试质量不高: 17.6%", "  - ❌ 权威测试失败"]}