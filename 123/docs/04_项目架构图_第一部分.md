# 🏗️ 通用代币期货溢价套利系统 - 项目架构图（第一部分：结构概览）

## 📂 项目结构概览 - 通用代币期货溢价套利系统
```
arbitrage-system/                       # 🏠 支持所有代币的通用套利系统
├── 📄 main.py                          # 主程序入口 (1074行) - 系统启动控制
├── 📄 .env                             # 环境配置文件 (575行) - 动态交易对配置
├── 📄 requirements.txt                 # 依赖包列表 (245行)
├── 📄 README.md                        # 项目说明文档
├── 📄 方案.md                          # 系统方案说明
├── 📄 项目架构图_实际版本.md           # 本架构图文档
│
├── 📁 config/                          # 🔧 配置管理模块 - 通用配置系统
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 debug_config.py              # 调试配置 (111行)
│   ├── 📄 settings.py                  # 系统配置 (395行) - 支持所有代币
│   └── 📄 exchange_config.py           # 交易所配置 (20行) - 三交易所统一
│
├── 📁 core/                            # 🎯 核心业务逻辑 - 通用套利引擎 (15个模块)
│   ├── 📄 __init__.py                  # 模块初始化 (21行)
│   ├── 📄 arbitrage_engine.py          # 套利引擎核心 (874行) - 余额缓存+API获取系统
│   ├── 📄 opportunity_scanner.py       # 套利机会扫描器 (1010行) - 动态扫描
│   ├── 📄 execution_engine.py          # 执行引擎 (1956行) - 通用执行逻辑
│   ├── 📄 execution_params_preparer.py # 🆕 执行参数准备器 (357行) - 动态参数，支持预获取深度
│   ├── 📄 order_pairing_manager.py     # 🆕 订单配对管理器 (299行) - 智能配对，5%容差
│   ├── 📄 convergence_monitor.py       # 🆕 价差趋同监控器 (206行) - 实时监控，依赖注入
│   ├── 📄 trading_rules_preloader.py   # 5大缓存+API获取系统核心 - 统一精度处理
│   ├── 📄 trading_system_initializer.py # 🆕 交易系统初始化器 (651行) - 统一启动，缓存预热
│   ├── 📄 unified_balance_manager.py   # 统一余额管理器 - 30秒TTL缓存
│   ├── 📄 unified_closing_manager.py   # 统一平仓管理器 - 通用平仓逻辑
│   ├── 📄 unified_opening_manager.py   # 统一开仓管理器 - 通用开仓逻辑
│   ├── 📄 unified_depth_analyzer.py    # 统一深度分析器 - 订单簿分析
│   ├── 📄 unified_http_session_manager.py # 🆕 统一HTTP会话管理器 (292行) - 防止会话泄漏
│   ├── 📄 unified_leverage_manager.py  # 🆕 统一杠杆管理器 (313行) - 三交易所杠杆设置
│   └── 📄 universal_token_system.py    # 通用代币系统 - 零硬编码支持
│
├── 📁 exchanges/                       # 🏪 交易所适配器 - 三交易所统一接口
│   ├── 📄 __init__.py                  # 模块初始化 (34行)
│   ├── 📄 README.md                    # 交易所说明文档
│   ├── 📄 exchanges_base.py            # 交易所基类 - 统一接口定义
│   ├── 📄 currency_adapter.py          # 🎯 统一代币转换模块 (123行) - 核心
│   ├── 📄 exchange_adapters.py         # 交易所适配器 - 统一封装
│   ├── 📄 gate_exchange.py             # Gate.io交易所实现 - 完整API封装
│   ├── 📄 bybit_exchange.py            # Bybit交易所实现 - 完整API封装
│   └── 📄 okx_exchange.py              # OKX交易所实现 - 完整API封装
│
├── 📁 trading/                         # 🛒 交易执行模块 - 现货期货交易
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 spot_trader.py               # 现货交易执行器 - 支持所有代币
│   ├── 📄 futures_trader.py            # 期货交易执行器 - 支持所有代币
│   └── 📄 order_manager.py             # 订单管理器 - 订单生命周期管理
│
├── 📁 fund_management/                 # 💰 资金管理模块 - 多交易所资金管理
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 fund_manager.py              # 资金管理器 (790行) - 资金平衡控制
│   └── 📄 fund_transfer_service.py     # 资金划转服务 - 跨交易所划转
│
├── 📁 websocket/                       # 🌐 WebSocket模块 - 实时数据流
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 ws_client.py                 # WebSocket客户端基类 (561行)
│   ├── 📄 ws_manager.py                # WebSocket管理器 (729行) - 连接管理
│   ├── 📄 gate_ws.py                   # Gate.io WebSocket (576行)
│   ├── 📄 bybit_ws.py                  # Bybit WebSocket (777行)
│   └── 📄 okx_ws.py                    # OKX WebSocket - 实时行情
│
├── 📁 utils/                           # 🛠️ 工具模块 - 通用工具和配置 (8个模块)
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 cache_monitor.py             # 🆕 统一缓存监控系统 - 5大缓存性能监控
│   ├── 📄 min_order_detector.py        # 🆕 动态最小金额检测器 - 三交易所最小金额查询
│   ├── 📄 hedge_calculator.py          # 🆕 对冲计算器 - 精确对冲比例计算，98%阈值
│   ├── 📄 margin_calculator.py         # 🆕 保证金计算器 - API驱动，5分钟TTL缓存
│   ├── 📄 helpers.py                   # 辅助工具函数 - 通用工具
│   ├── 📄 logger.py                    # 日志系统 - 统一日志管理
│   └── 📄 notification.py              # 通知模块 - 消息推送
│
├── 📁 monitoring/                      # 📊 监控模块 - 系统监控
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 performance_monitor.py       # 性能监控器 (946行) - 性能分析
│   ├── 📄 position_monitor.py          # 仓位监控器 (741行) - 仓位跟踪
│   └── 📄 risk_monitor.py              # 风险监控器 - 风险控制
│
├── 📁 tests/                           # 🧪 测试模块 - 完整测试套件
│   ├── 📄 __init__.py                  # 模块初始化
│   ├── 📄 test_exchanges_comprehensive.py # 交易所综合测试 - 真实API测试
│   ├── 📄 test_trading_comprehensive.py   # 交易模块综合测试 - 完整集成
│   ├── 📄 test_complete_system.py         # 完整系统测试 - 端到端测试
│   ├── 📄 test_final_precision_system.py  # 精度系统最终测试
│   ├── 📄 performance_comprehensive_test.py # 性能综合测试
│   ├── 📄 regression_test_quick.py        # 快速回归测试
│   └── 📄 run_all_tests.py               # 测试运行器 - 批量执行
│
├── 📁 docs/                            # 📚 文档模块 - 项目文档
│   ├── 📄 README.md                    # 项目说明
│   ├── 📄 COMPREHENSIVE_TEST_CHECKLIST.md # 综合测试清单
│   ├── 📄 FIXED_ISSUES_SUMMARY.md     # 修复问题总结
│   ├── 📄 01_核心统一模块清单.md       # 核心模块清单（第一部分）
│   ├── 📄 02_交易所模块修复对比.md     # 修复对比（第二部分）
│   ├── 📄 03_执行引擎和最终确认.md     # 执行引擎（第三部分）
│   ├── 📄 04_项目架构图_第一部分.md    # 架构图（第一部分）
│   ├── 📄 05_数据流链路文档.md         # 数据流链路文档
│   ├── 📄 DOCUMENTATION_CONSISTENCY_ANALYSIS_REPORT.md # 一致性分析报告
│   └── 📄 precision_config通用动态精度.md # 精度配置文档
│
├── 📁 logs/                            # 📝 日志目录 - 系统日志
│   ├── 📄 ExecutionEngine.log          # 执行引擎日志
│   ├── 📄 detailed_20250604.log        # 详细日志
│   ├── 📄 error_20250604.log           # 错误日志
│   └── 📄 websocket_20250604.log       # WebSocket日志
│
├── 📁 analysis/                        # 🔍 分析工具 - 系统分析
│   ├── 📄 check_all_exchanges_precision.py # 精度检查工具
│   ├── 📄 test_bybit_precision_fix.py      # Bybit精度修复测试
│   └── 📄 opening_precision_analysis.md    # 开仓精度分析
│
└── 📁 api sdk/                         # 📦 官方SDK - 参考实现
    ├── 📁 gateapi-python-master/       # Gate.io官方SDK
    ├── 📁 pybit-master/                # Bybit官方SDK
    └── 📁 okx-sdk-master/              # OKX官方SDK
```

## 🎯 核心特性 - 通用代币期货溢价套利系统

### 1. 🌍 通用代币支持
- **零硬编码设计**: 支持所有代币，无需修改代码
- **动态交易对配置**: 通过.env文件随时添加新交易对
- **智能符号转换**: 统一的currency_adapter模块处理所有格式转换
- **API精度自动获取**: 实时从交易所API获取精度和步长信息

### 2. 🏪 三交易所统一接口
- **Gate.io**: 完整API封装，支持现货期货交易
- **Bybit**: 完整API封装，支持现货期货交易
- **OKX**: 完整API封装，支持现货期货交易
- **统一接口**: 所有交易所使用相同的接口标准

### 3. 🎯 统一代币转换模块 (currency_adapter.py)
```python
# 核心功能：
- extract_base_currency()    # 提取基础币种 (BTC, ETH, ADA...)
- get_exchange_symbol()      # 转换交易所格式
- normalize_symbol()         # 标准化为统一格式

# 支持格式：
- 标准格式: BTC-USDT
- Gate格式: BTC_USDT
- Bybit格式: BTCUSDT
- OKX现货: BTC-USDT
- OKX期货: BTC-USDT-SWAP
```

### 4. 🔥 6大缓存+API获取系统架构

#### **💰 余额缓存+API获取系统 (ArbitrageEngine)**
```python
# 文件: core/arbitrage_engine.py
- 实时余额管理和API获取
- 缓存TTL: 30秒（可配置）
- 支持三大交易所统一账户
```

#### **📊 保证金缓存+API获取系统 (MarginCalculator)**
```python
# 文件: utils/margin_calculator.py
- 合约信息缓存和API获取
- 缓存TTL: 5分钟
- 支持期货保证金计算
```

#### **📋 交易规则缓存+API获取系统 (TradingRulesPreloader)**
```python
# 文件: core/trading_rules_preloader.py
- 精度数据缓存和API批量获取
- 缓存TTL: 24小时（可配置）
- 统一精度处理
```

#### **⚖️ 对冲质量缓存+计算系统 (TradingRulesPreloader)**
```python
# 文件: core/trading_rules_preloader.py
- 对冲质量缓存和智能计算
- 缓存TTL: 10秒（可配置）
- 98%对冲质量保证
```

#### **🎯 精度缓存+处理系统 (TradingRulesPreloader)**
```python
# 文件: core/trading_rules_preloader.py
- 统一精度缓存和处理
- 缓存TTL: 1小时（可配置）
- 零API调用精度格式化
```

#### **🔥 【NEW】价格数据统一系统 (OpportunityScanner)**
```python
# 文件: core/opportunity_scanner.py
- 🔥 唯一价格数据源：OpportunityScanner.market_data
- 数据来源：WebSocket实时数据流
- 数据更新：实时更新，无缓存TTL
- 🔥 替代：已删除WebSocket价格缓存，已删除RestAPI价格获取
- 支持：ConvergenceMonitor依赖注入，统一价格获取接口
```

#### **🔥 【DELETED】订单簿缓存系统（已删除）**
```python
# 原文件: core/trading_rules_preloader.py
# 状态: 已删除，统一使用OpportunityScanner.market_data
# 原因: 避免重复缓存，统一数据源
```

### 5. 🔧 ~~底层API调用模块 (precision_config.py)~~ 
⚠️ **已废弃**: 精度处理已统一在 `TradingRulesPreloader` 中，无需单独的precision_config模块

**原设计**: 为 `TradingRulesPreloader` 提供底层API调用服务
**现状**: 直接在 `TradingRulesPreloader` 内部实现所有精度相关功能

#### 🔥 **现在的统一精度架构**:
- **TradingRulesPreloader**: 统一精度处理中心
  - `format_amount_unified()`: 统一金额格式化
  - `truncate_to_step_size()`: 统一步长处理
  - `format_amount_with_contract_conversion()`: 合约转换
  - `get_hedge_quality_cached()`: 对冲质量检查

- **各交易所**: 直接调用TradingRulesPreloader的统一方法
  - Gate.io: 调用 + 整数转换
  - Bybit: 直接调用  
  - OKX: 调用 + 合约转换

---

**📝 注意**: 本文档为第一部分，描述项目结构概览和核心特性。完整架构图请参考其他部分文档。
