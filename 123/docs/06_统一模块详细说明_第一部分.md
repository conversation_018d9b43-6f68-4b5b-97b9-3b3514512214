# 🧩 通用期货溢价套利系统 - 统一模块详细说明（第一部分：核心引擎）

## 🔥 **系统核心定位**

### **⚠️ 重要说明：期货溢价专业套利系统**

**本系统是通用的专注期货溢价执行套利，现货溢价平仓的套利系统，支持任何代币！**

#### **🎯 模块设计围绕核心套利逻辑**
```
期货溢价阈值达到 → 开仓锁定差价 → 等待趋同 → 现货溢价阈值达到 → 平仓获利
```

#### **📊 模块职责分工**
- **OpportunityScanner** → 发现期货溢价差价（+符号）和现货溢价差价（-符号）
- **ExecutionEngine** → 执行开仓（期货溢价差价）和平仓（现货溢价差价）
- **ConvergenceMonitor** → 监控价差趋同过程
- **UnifiedOrderSpreadCalculator** → 精确计算期货溢价差价和现货溢价差价
- **核心理念** → +/-符号表示差价类型，总利润 = |期货溢价差价| + |现货溢价差价|

## 📋 文档概述

本文档详细说明了通用期货溢价套利系统中19个核心统一模块的职责、核心方法和使用示例，确保开发者能够正确理解和使用每个模块。其中新增了UnifiedOrderSpreadCalculator作为核心Order差价计算引擎。

## 🎯 核心设计理念

- **统一接口**: 消除重复代码，提供一致的接口
- **模块化设计**: 每个模块职责单一，高内聚低耦合
- **缓存优先**: 运行时零API延迟，提升性能
- **异常安全**: 完善的错误处理和重试机制
- **套利专业化**: 所有模块围绕期货溢价套利逻辑设计

## 🏗️ 1. 核心引擎模块 (core/)

### 1.1 ArbitrageEngine - 套利引擎

```python
# 文件位置: core/arbitrage_engine.py
# 职责: 套利系统主控制器，余额缓存管理

class ArbitrageEngine:
    """套利引擎 - 系统核心控制器"""

    # 🔥 核心方法
    async def start(self) -> None:
        """启动套利引擎 - 实际方法名"""
        # 1. 初始化各组件
        # 2. 启动时一次性资金优化
        # 3. 初始化余额缓存
        # 4. 启动主循环作为后台任务

    async def stop(self) -> None:
        """停止套利引擎"""
        # 1. 停止主循环任务
        # 2. 关闭时资金恢复
        # 3. 优雅关闭当前套利会话
        # 4. 停止各组件

    async def update_balance_cache(self) -> None:
        """更新余额缓存 - 实际方法名"""
        # 从UnifiedBalanceManager获取最新余额

    # 注意：get_balance_summary方法已移除
    # 余额信息通过UnifiedBalanceManager.get_balance()获取

    # 🔥 余额缓存系统（通过UnifiedBalanceManager管理）
    # balance_cache由UnifiedBalanceManager统一管理，格式：
    # {
    #     "gate_spot_usdt": 1250.0,
    #     "bybit_unified_usdt": 2500.0,
    #     "okx_unified_usdt": 2500.0
    # }
```

### 1.2 ExecutionEngine - 执行引擎

```python
# 文件位置: core/execution_engine.py
# 职责: 极速执行逻辑，<30ms并行交易

class ExecutionEngine:
    """执行引擎 - 极速执行核心"""

    async def execute_arbitrage(self, opportunity: ArbitrageOpportunity) -> ExecutionResult:
        """执行套利交易 - 核心方法（实际方法名）"""
        # 1. 🔥 Order差价验证 (支持execution_context)
        # 2. 预检查对冲质量
        # 3. 极速并行执行交易
        # 4. 等待收敛并平仓
        # 5. 返回执行结果

    async def _revalidate_opportunity_before_execution(self, opportunity: ArbitrageOpportunity, execution_context: str) -> Tuple[bool, float]:
        """🔥 执行前Order差价验证 - 支持开仓/平仓不同逻辑"""
        # 开仓上下文: 只接受期货溢价 (spread > 0)
        # 平仓上下文: 只接受现货溢价 (spread < 0)
        # 使用UnifiedOrderSpreadCalculator进行验证

    async def _execute_parallel_trading(self, opportunity: ArbitrageOpportunity) -> bool:
        """极速并行执行交易 - 内部方法（实际方法名）"""
        # 1. 🔥 Order差价验证 (execution_context="opening")
        # 2. 零延迟参数准备（使用6大缓存+API系统）
        # 3. 智能数量协调
        # 4. 真正并行执行 (asyncio.gather)
        # 5. 调用_execute_spot_order()和_execute_futures_order()

    async def _pre_check_hedge_quality(self, opportunity: ArbitrageOpportunity) -> bool:
        """预检查对冲质量 - 98%阈值"""
        # 确保对冲质量达到98%以上

    async def close_positions(self, opportunity: ArbitrageOpportunity) -> bool:
        """关闭仓位 - 平仓方法"""
        # 🔥 Order差价验证 (execution_context="closing")
        # 使用UnifiedClosingManager进行平仓
```

### 1.3 OpportunityScanner - 机会扫描器

```python
# 文件位置: core/opportunity_scanner.py
# 职责: 套利机会扫描，唯一价格数据源

class OpportunityScanner:
    """机会扫描器 - 唯一价格数据源"""
    
    # 🔥 唯一价格数据源
    market_data = {
        "gate": {"spot": {}, "futures": {}},
        "bybit": {"spot": {}, "futures": {}},
        "okx": {"spot": {}, "futures": {}}
    }
    
    async def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """扫描套利机会"""
        # 1. 🔥 使用Order数据计算6种套利组合差价
        # 2. 筛选价差>0.2%的机会
        # 3. 验证机会有效性

    def _calculate_order_based_spread(self, spot_orderbook: dict, futures_orderbook: dict,
                                    target_amount_usd: float = 100.0, execution_context: str = "opening") -> Optional[float]:
        """🔥 基于Order数据的精确差价计算 - 统一计算方法"""
        # 使用UnifiedOrderSpreadCalculator进行30档深度分析

    # 🔥 数据源管理 (已更新为Order数据源)
    # market_data: Dict[str, MarketData] - 唯一Order数据源
    # 格式: {"gate_spot_BTC-USDT": MarketData(orderbook={asks:[], bids:[]}, ...)}
```

### 1.4 🔥 UnifiedOrderSpreadCalculator - 统一Order差价计算器

```python
# 文件位置: core/unified_order_spread_calculator.py
# 职责: 统一Order差价计算，30档深度分析，滑点控制

class UnifiedOrderSpreadCalculator:
    """统一Order差价计算器 - 核心差价计算引擎"""

    # 🔥 核心方法
    def calculate_order_based_spread(self, spot_orderbook: Dict[str, Any], futures_orderbook: Dict[str, Any],
                                   target_amount_usd: float, execution_context: str = "opening") -> Optional[OrderSpreadResult]:
        """基于Order数据的统一差价计算"""
        # 1. 30档累积表构建
        # 2. 二分查找最优执行档位
        # 3. 计算加权平均执行价格
        # 4. 滑点控制 (<0.1%)
        # 5. 返回完整计算结果

    def build_cumulative_table_30_levels(self, orderbook_side: List[List[float]], side: str) -> np.ndarray:
        """构建30档累积表 - 核心算法"""
        # 累积表格式: [[price, cumulative_value, cumulative_qty], ...]

    def find_optimal_execution_level(self, cumulative_table: np.ndarray, target_amount: float) -> Optional[ExecutionLevel]:
        """二分查找最优执行档位 - O(log n)复杂度"""
        # 返回最优执行档位信息

    # 🔥 配置管理
    # config: DepthAnalysisConfig
    # - max_depth_levels: 30 (最大深度档位)
    # - safety_margin: 0.90 (90%安全边际)
    # - slippage_threshold: 0.001 (0.1%滑点阈值)
    # - min_execution_amount: 10.0 (最小执行金额)

# 🔥 全局单例获取
def get_order_spread_calculator() -> UnifiedOrderSpreadCalculator:
    """获取统一Order差价计算器实例"""
    # 单例模式，确保全系统使用同一个计算器实例
```

## 🔧 2. 统一管理器模块

### 2.1 UnifiedOpeningManager - 统一开仓管理器

```python
# 文件位置: core/unified_opening_manager.py
# 职责: 统一开仓逻辑，支持所有交易所

class UnifiedOpeningManager:
    """统一开仓管理器"""
    
    async def unified_market_buy(
        self, symbol: str, quantity: float, exchange,
        market_type: str = "spot", orderbook: Optional[Dict] = None
    ) -> OpeningResult:
        """统一市价买入接口"""
        # 1. 参数准备和验证
        # 2. 精度格式化 (缓存)
        # 3. 深度检查 (orderbook)
        # 4. 执行开仓订单

    async def unified_market_sell(
        self, symbol: str, quantity: float, exchange,
        market_type: str = "spot", orderbook: Optional[Dict] = None
    ) -> OpeningResult:
        """统一市价卖出接口"""
        # 支持现货和期货卖出

    async def unified_market_sell_open(
        self, symbol: str, quantity: float, exchange,
        market_type: str = "spot", orderbook: Optional[Dict] = None
    ) -> OpeningResult:
        """统一市价卖出开仓接口"""
        # 支持期货做空开仓
```

### 2.2 UnifiedClosingManager - 统一平仓管理器

```python
# 文件位置: core/unified_closing_manager.py
# 职责: 统一平仓逻辑，支持现货和期货

class UnifiedClosingManager:
    """统一平仓管理器"""
    
    async def close_position_unified(
        self, symbol: str, exchange, market_type: str,
        side: str = None, orderbook: Dict = None
    ) -> ClosingResult:
        """统一平仓接口"""
        # 1. 平仓类型判断 (现货/期货)
        # 2. 获取持仓/余额信息
        # 3. 执行平仓订单
        # 4. 重试机制保证

    async def unified_market_sell_close(
        self, symbol: str, quantity: float, exchange,
        market_type: str = "spot"
    ) -> ClosingResult:
        """统一市价卖出平仓接口"""
        # 现货平仓卖出

    async def unified_market_buy_close(
        self, symbol: str, quantity: float, exchange,
        market_type: str = "spot"
    ) -> ClosingResult:
        """统一市价买入平仓接口"""
        # 期货平仓买入

    async def emergency_close_all(
        self, exchange, symbols: List[str]
    ) -> List[ClosingResult]:
        """紧急平仓所有仓位 - 实际存在的方法"""
        # 1. 遍历所有交易对
        # 2. 逐个调用close_position_unified
        # 3. 处理现货和期货平仓
        # 4. 返回所有平仓结果
```

### 2.3 UnifiedExchangeInitializer - 统一初始化器

```python
# 文件位置: exchanges/unified_exchange_initializer.py
# 职责: 消除三交易所重复初始化，单例模式

class UnifiedExchangeInitializer:
    """统一交易所初始化器"""
    
    @classmethod
    def get_initializer(cls, exchange_name: str) -> 'UnifiedExchangeInitializer':
        """单例模式获取初始化器"""
        # 避免重复创建实例
    
    def get_all_modules(self) -> Dict[str, Any]:
        """获取所有统一模块"""
        return {
            "currency_adapter": self.currency_adapter,
            "rules_preloader": self.rules_preloader,
            "token_system": self.token_system,
            "opening_manager": self.opening_manager,
            "closing_manager": self.closing_manager,
            "config": self.config
        }

# 🔥 全局快速访问函数
def init_exchange_modules(exchange_name: str) -> Dict[str, Any]:
    """一键初始化交易所所有模块"""
    initializer = UnifiedExchangeInitializer.get_initializer(exchange_name)
    return initializer.get_all_modules()
```

## 📊 3. 缓存系统模块

### 3.1 TradingRulesPreloader - 交易规则预加载器

```python
# 文件位置: core/trading_rules_preloader.py
# 职责: 5大缓存系统核心，统一精度处理

class TradingRulesPreloader:
    """交易规则预加载器 - 参与5大缓存系统中的3个"""

    # 🔥 TradingRulesPreloader管理的3个缓存系统
    trading_rules = {}          # 24小时TTL (可配置)
    precision_cache = {}        # 1小时TTL (可配置)
    hedge_quality_cache = {}    # 10秒TTL (可配置)

    # 🔥 其他模块管理的缓存系统
    # balance_cache - 由ArbitrageEngine管理 (30秒TTL)
    # margin_cache - 由MarginCalculator管理 (5分钟TTL)

    # ❌ 已删除的缓存
    # orderbook_cache - 已删除，统一使用OpportunityScanner.market_data
    
    def format_amount_unified(
        self, amount: float, exchange: str, symbol: str, market_type: str = "spot"
    ) -> str:
        """统一金额格式化 - 零API调用"""
        # 从precision_cache获取精度信息，返回格式化字符串
        # 🔥 统一截断处理：所有交易所使用相同的截断逻辑，确保对冲质量一致性
        # 🔥 Bybit特殊处理：只有小步长(<1.0)才去除尾随零，大步长保持完整格式
        # 🔥 Gate.io期货：API层面转换为整数，但统一模块保持截断逻辑一致性

    def get_step_size(self, exchange: str, symbol: str, market_type: str = "spot") -> Optional[float]:
        """🔥 新增：获取步长信息 - 与文档保持一致"""
        # 从交易规则缓存获取步长信息，返回浮点数步长值
        # 如果规则不存在或步长为空，返回None
    
    def get_hedge_quality_cached(
        self, spot_exchange: str, futures_exchange: str, symbol: str,
        spot_amount: float, futures_amount: float, spot_price: float, futures_price: float
    ) -> Dict[str, Any]:
        """获取缓存的对冲质量"""
        # 智能缓存，相同参数复用结果，返回包含hedge_ratio的字典
    
    async def preload_all_rules(self) -> None:
        """预加载所有交易规则 - 启动时执行"""
        # 批量获取90个交易规则
```

### 3.2 实际TTL配置（可通过.env配置）

```python
# 实际TTL配置方式
self.trading_rules_ttl = int(os.getenv("TRADING_RULES_TTL", "86400"))  # 24小时
self.hedge_quality_cache_ttl = int(os.getenv("HEDGE_QUALITY_TTL", "10"))  # 10秒
self.precision_cache_ttl = int(os.getenv("PRECISION_CACHE_TTL", "3600"))  # 1小时
self.balance_cache_ttl = int(os.getenv("BALANCE_CACHE_TTL", "30"))  # 30秒
self.margin_cache_ttl = int(os.getenv("MARGIN_CACHE_TTL", "300"))  # 5分钟
```

---

**📝 注意**: 本文档为第一部分，描述核心引擎和统一管理器模块。完整说明请参考其他部分文档。
