# 📡 通用期货溢价套利系统 - 数据流链路文档

## 📋 文档概述

本文档详细描述了通用期货溢价套利系统中WebSocket数据流、缓存系统和数据传递机制，确保数据的实时性、一致性和高效传递。

## 🎯 核心设计原则

- **实时数据优先**: WebSocket作为主要数据源，禁用REST API价格获取
- **统一数据源**: OpportunityScanner.market_data作为唯一价格数据源
- **多级缓存**: 5大缓存系统，不同TTL策略
- **零延迟获取**: 运行时缓存命中，避免API调用

## 🌐 1. WebSocket数据流架构

### 1.1 WebSocket连接架构

```
三交易所WebSocket连接 (🔥 统一Order数据源，30档深度分析)
├── Gate.io WebSocket (gate_ws.py)
│   ├── 现货Order流: spot.order_book (30档全量快照)
│   └── 期货Order流: futures.order_book (30档全量快照)
├── Bybit WebSocket (bybit_ws.py)
│   ├── 现货Order流: orderbook.50.BTCUSDT (30档深度提取)
│   └── 期货Order流: orderbook.50.BTCUSDT (30档深度提取)
└── OKX WebSocket (okx_ws.py)
    ├── 现货Order流: books (30档深度提取)
    └── 期货Order流: books (30档深度提取)

🔥 核心变化：
- 完全删除ticker价格流，只使用Order数据源
- 统一30档深度分析，支持累积表+二分查找算法
- Order数据提供真实执行价格，消除ticker中间价偏差
```

### 1.2 WebSocket管理器数据流 (🔥 2025年修复)

```
WsManager (ws_manager.py)
├── 1. 连接管理
│   ├── start_all_connections() → 启动所有WebSocket
│   ├── 心跳检测机制 → 保持连接活跃
│   ├── 断线重连逻辑 → 自动恢复连接
│   └── 连接状态监控 → 实时状态报告
├── 2. 🔥 Order数据分发 (统一Order数据源)
│   ├── 原始Order数据接收 → 各交易所WebSocket
│   ├── 🔥 Order数据验证 → 30档深度完整性检查
│   ├── Order数据格式标准化 → 统一数据结构
│   ├── Order数据质量检查 → 过滤异常深度数据
│   ├── 🔥 Order数据处理日志 → 记录30档深度处理状态
│   └── Order数据路由分发 → 发送到OpportunityScanner
└── 3. 异常处理
    ├── 连接异常恢复
    ├── 数据异常过滤
    ├── 🔥 数据类型混淆检测
    └── 性能监控统计
```

### 1.3 数据流向图 (🔥 2025年修复后)

```
WebSocket原始数据
    ↓
🔥 WsManager数据类型识别
    ↓
┌─────────────────────────────────────┐
│          Order数据 (统一数据源)        │
│     (30档深度 + 执行价格信息)          │
└─────────────────────────────────────┘
    ↓
🔥 统一Order数据处理 (30档累积表+二分查找)
    ↓
OpportunityScanner.market_data (唯一Order数据源)
    ↓
UnifiedOrderSpreadCalculator (统一差价计算)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   ArbitrageEngine   │  ExecutionEngine   │ ConvergenceMonitor │
│   (套利决策)        │   (执行参数)       │   (趋同监控)       │
└─────────────────┴─────────────────┴─────────────────┘
    ↓                    ↓                    ↓
套利机会识别        执行参数准备        价差趋同检测
```

## 🗄️ 2. 缓存系统架构

### 2.1 五大缓存系统概览

```
统一缓存系统 (5大缓存系统)
├── 1. 余额缓存系统 (ArbitrageEngine)
│   ├── TTL: 30秒 (可配置 BALANCE_CACHE_TTL)
│   ├── 数据源: 统一余额管理器API获取
│   ├── 缓存内容: 各交易所余额信息
│   ├── 缓存键格式: "exchange_account_currency"
│   ├── 更新策略: 交易后实时更新
│   └── 使用场景: 余额充足性检查
├── 2. 保证金缓存系统 (MarginCalculator)
│   ├── TTL: 5分钟 (300秒)
│   ├── 数据源: 交易所API获取
│   ├── 缓存内容: 合约信息、保证金计算
│   ├── 缓存键格式: "exchange_symbol"
│   ├── 更新策略: 定时刷新
│   └── 使用场景: 期货保证金计算
├── 3. 交易规则缓存系统 (TradingRulesPreloader)
│   ├── TTL: 24小时 (可配置 TRADING_RULES_TTL)
│   ├── 数据源: 交易所API批量获取
│   ├── 缓存内容: 90个交易规则
│   ├── 缓存键格式: "exchange_symbol_market"
│   ├── 更新策略: 启动时预加载 + 定时刷新
│   └── 使用场景: 精度处理、最小订单量检查
├── 4. 对冲质量缓存系统 (TradingRulesPreloader)
│   ├── TTL: 10秒 (可配置 HEDGE_QUALITY_TTL)
│   ├── 数据源: 实时计算结果
│   ├── 缓存内容: 对冲质量计算结果
│   ├── 更新策略: 智能缓存，相同参数复用
│   └── 使用场景: 98%对冲质量检查
└── 5. 精度缓存系统 (TradingRulesPreloader)
    ├── TTL: 1小时 (可配置 PRECISION_CACHE_TTL)
    ├── 数据源: 交易规则缓存衍生
    ├── 缓存内容: 精度信息 (qty_precision, price_precision)
    ├── 更新策略: 与trading_rules同步更新
    └── 使用场景: format_amount_unified()统一处理

注意：合约信息缓存已整合到保证金缓存系统中，不再单独作为第6个缓存系统
```

### 2.2 缓存数据流

```
缓存数据获取流程
├── 1. 缓存命中检查
│   ├── 检查缓存键是否存在
│   ├── 检查TTL是否过期
│   └── 返回缓存数据 (0ms延迟)
├── 2. 缓存未命中处理
│   ├── 发起API请求获取数据
│   ├── 数据验证和格式化
│   ├── 存储到缓存 (设置TTL)
│   └── 返回新数据
└── 3. 缓存更新策略
    ├── 主动更新: 定时刷新长期缓存
    ├── 被动更新: 访问时检查过期
    └── 事件更新: 交易后更新余额
```

## 📊 3. 统一数据源管理

### 3.1 OpportunityScanner.market_data架构

```python
# 文件位置: core/opportunity_scanner.py

class OpportunityScanner:
    """套利机会扫描器 - 唯一价格数据源"""
    
    def __init__(self):
        # 🔥 唯一价格数据源
        self.market_data = {
            "gate": {
                "spot": {},      # {"BTC-USDT": MarketData}
                "futures": {}    # {"BTC-USDT": MarketData}
            },
            "bybit": {
                "spot": {},
                "futures": {}
            },
            "okx": {
                "spot": {},
                "futures": {}
            }
        }
        
        # 🔥 订单簿数据
        self.orderbook_data = {
            "gate": {"spot": {}, "futures": {}},
            "bybit": {"spot": {}, "futures": {}},
            "okx": {"spot": {}, "futures": {}}
        }
```

### 3.2 数据更新机制

```
🔥 Order数据更新流程 (统一Order数据源)
├── 1. Order数据接收
│   ├── WebSocket推送30档Order数据
│   ├── Order数据格式验证
│   └── Order数据时间戳检查
├── 2. Order数据标准化
│   ├── 交易对格式统一 (BTC-USDT)
│   ├── 30档asks/bids数据提取
│   ├── Order深度数据验证
│   └── MarketData对象创建 (包含orderbook字段)
├── 3. Order数据存储
│   ├── 更新market_data.orderbook字典
│   ├── 保存30档深度数据
│   └── 触发Order数据变更事件
└── 4. Order数据消费
    ├── UnifiedOrderSpreadCalculator获取Order数据
    ├── ExecutionEngine获取深度
    └── ConvergenceMonitor获取实时价格
```

## 🔄 4. 数据传递机制

### 4.1 依赖注入模式

```python
# ConvergenceMonitor依赖注入修复
class ConvergenceMonitor:
    def __init__(self, opportunity_scanner: OpportunityScanner):
        """依赖注入OpportunityScanner实例"""
        self.opportunity_scanner = opportunity_scanner
    
    def get_current_prices(self, symbol: str) -> Dict[str, float]:
        """从统一数据源获取价格"""
        return self.opportunity_scanner.get_current_prices(symbol)
```

### 4.2 数据访问接口

```python
# 统一数据访问接口
class DataAccessInterface:
    """统一数据访问接口"""
    
    @staticmethod
    def get_market_price(exchange: str, symbol: str, market_type: str) -> float:
        """获取市场价格 - 从OpportunityScanner.market_data"""
        return OpportunityScanner.instance.market_data[exchange][market_type][symbol].price
    
    @staticmethod
    def get_orderbook_depth(exchange: str, symbol: str, market_type: str) -> Dict:
        """获取订单簿深度 - 从OpportunityScanner.orderbook_data"""
        return OpportunityScanner.instance.orderbook_data[exchange][market_type][symbol]
    
    @staticmethod
    def get_cached_balance(exchange: str, currency: str) -> float:
        """获取缓存余额 - 从ArbitrageEngine.balance_cache"""
        return ArbitrageEngine.instance.balance_cache.get(f"{exchange}_{currency}", 0.0)
```

## ⚡ 5. 性能优化机制

### 5.1 零延迟数据获取

```
性能优化对比
├── 传统方式 (已废弃)
│   ├── 余额查询: 50-100ms API调用
│   ├── 保证金计算: 30-80ms API调用
│   ├── 深度数据: 20-50ms API调用
│   ├── 精度数据: 10-30ms API调用
│   └── 交易规则: 100-200ms API调用
└── 缓存方式 (当前)
    ├── 余额查询: 0.00ms 缓存命中
    ├── 保证金计算: 0.00ms 缓存命中
    ├── 深度数据: 0.00ms WebSocket实时
    ├── 精度数据: 0.00ms 缓存命中
    └── 交易规则: 0.00ms 缓存命中
```

### 5.2 缓存命中率优化

```
缓存优化策略
├── 1. 预加载策略
│   ├── 启动时预加载90个交易规则
│   ├── 预计算常用精度信息
│   └── 预连接WebSocket数据流
├── 2. 智能缓存策略
│   ├── 对冲质量缓存: 相同参数复用
│   ├── 🔥 WebSocket数据流: 实时更新，无独立缓存
│   └── 余额缓存: 交易后主动更新
└── 3. 缓存清理策略
    ├── 定时清理过期缓存
    ├── 内存使用监控
    └── 缓存大小限制
```

## 🔍 6. 数据质量保证

### 6.1 数据验证机制

```python
class DataValidator:
    """数据质量验证器"""
    
    @staticmethod
    def validate_market_data(data: MarketData) -> bool:
        """验证市场数据质量"""
        return (
            data.price > 0 and
            data.volume >= 0 and
            not math.isnan(data.price) and
            not math.isinf(data.price) and
            time.time() - data.timestamp < 60  # 数据不超过1分钟
        )
    
    @staticmethod
    def validate_orderbook_data(data: OrderBookData) -> bool:
        """验证订单簿数据质量 - 🔥 修复：前10档深度验证"""
        return (
            len(data.bids) >= 10 and  # 🔥 修复：至少10档买单深度
            len(data.asks) >= 10 and  # 🔥 修复：至少10档卖单深度
            data.get_best_bid() > 0 and
            data.get_best_ask() > 0 and
            data.get_best_ask() > data.get_best_bid()  # 价差合理
        )
```

### 6.2 异常数据处理

```
异常数据处理流程
├── 1. 数据异常检测
│   ├── 价格异常检测 (价格为0、负数、NaN)
│   ├── 时间戳异常检测 (数据过期)
│   ├── 数量异常检测 (负数、异常大值)
│   └── 格式异常检测 (字段缺失)
├── 2. 异常数据处理
│   ├── 丢弃异常数据
│   ├── 使用上一次有效数据
│   ├── 记录异常日志
│   └── 触发告警通知
└── 3. 数据恢复机制
    ├── 重新订阅WebSocket
    ├── 重新获取API数据
    └── 缓存数据回退
```

## 📈 7. 监控和统计

### 7.1 数据流监控

```python
class DataFlowMonitor:
    """数据流监控器"""
    
    def __init__(self):
        self.stats = {
            "websocket_messages": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "api_calls": 0,
            "data_errors": 0
        }
    
    def record_websocket_message(self, exchange: str, message_type: str):
        """记录WebSocket消息"""
        self.stats["websocket_messages"] += 1
    
    def record_cache_hit(self, cache_type: str):
        """记录缓存命中"""
        self.stats["cache_hits"] += 1
    
    def record_cache_miss(self, cache_type: str):
        """记录缓存未命中"""
        self.stats["cache_misses"] += 1
    
    def get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.stats["cache_hits"] + self.stats["cache_misses"]
        return self.stats["cache_hits"] / max(total, 1)
```

### 7.2 性能指标

```
关键性能指标 (KPI)
├── 数据实时性
│   ├── WebSocket延迟: < 30ms
│   ├── 数据更新频率: > 1Hz
│   └── 数据新鲜度: < 1秒
├── 缓存性能
│   ├── 缓存命中率: > 95%
│   ├── 缓存响应时间: < 1ms
│   └── 内存使用率: < 80%
├── 数据质量
│   ├── 数据完整性: > 99.9%
│   ├── 数据准确性: > 99.99%
│   └── 异常数据率: < 0.1%
└── 系统稳定性
    ├── WebSocket连接稳定性: > 99.9%
    ├── 数据流中断率: < 0.01%
    └── 自动恢复成功率: > 99%
```

## ✅ 8. 数据流完整性验证

### 8.1 端到端数据流测试

```python
async def test_data_flow_integrity():
    """数据流完整性测试"""
    
    # 1. WebSocket数据接收测试
    assert ws_manager.get_connection_status()["gate"] == True
    
    # 2. 数据标准化测试
    market_data = opportunity_scanner.market_data["gate"]["spot"]["BTC-USDT"]
    assert market_data.price > 0
    
    # 3. 缓存系统测试
    cached_rules = rules_preloader.get_trading_rule("gate", "BTC-USDT", "spot")
    assert cached_rules is not None
    
    # 4. 数据消费测试
    prices = convergence_monitor.get_current_prices("BTC-USDT")
    assert "gate_spot" in prices
```

### 8.2 数据一致性检查

```python
def verify_data_consistency():
    """数据一致性检查"""
    
    # 检查所有数据源的时间戳一致性
    # 检查价格数据的合理性
    # 检查缓存数据的同步性
    # 检查订单簿数据的完整性
```

## 📊 9. Order价格日志格式

### 9.1 websocket_prices.log格式说明

```
🔥 Order实时计算日志格式 (已修复为Order加权平均价格)

08:34:15.669 📈 [F] RESOLV-USDT | GATE现货$ 0.1408 ↔ OKX期货$ 0.1407 | 差价-0.046% | 现货溢价 | 买okx期货+卖gate现货 | ms.669 | lat:35ms/31ms

格式说明：
- 时间戳: 08:34:15.669 (精确到毫秒)
- 组合标识: [F] (Gate现货-OKX期货组合)
- 交易对: RESOLV-USDT
- 🔥 Order执行价格: GATE现货$ 0.1408 (30档Order加权平均价格)
- 🔥 Order执行价格: OKX期货$ 0.1407 (30档Order加权平均价格)
- Order差价: -0.046% (基于Order数据的精确差价)
- 差价类型: 现货溢价/期货溢价
- 交易策略: 具体执行方向
- 延迟信息: lat:35ms/31ms (WebSocket延迟)
```

### 9.2 Order价格计算流程

```
Order价格日志生成流程:
1. WebSocket接收30档Order数据
2. UnifiedOrderSpreadCalculator计算加权平均执行价格
3. 计算基于Order数据的精确差价
4. 记录到websocket_prices.log
5. 确保日志价格与实际交易执行价格100%一致
```

### 9.3 日志数据源验证

```python
# 验证日志使用Order数据源
def verify_log_data_source():
    """验证websocket_prices.log使用Order数据源"""

    # 1. 确认价格来源为Order加权平均价格
    assert log_spot_price == order_result.spot_execution_price
    assert log_futures_price == order_result.futures_execution_price

    # 2. 确认差价计算基于Order数据
    assert spread_percent == order_result.executable_spread

    # 3. 确认30档深度分析生效
    assert order_result.spot_levels_used <= 30
    assert order_result.futures_levels_used <= 30
```

---

**📝 注意**: 本文档描述的数据流链路为系统核心数据架构，所有模块必须按照这些数据流进行数据获取和传递，确保数据的实时性、一致性和高效性。系统已完全删除ticker数据依赖，只使用Order数据源进行差价计算和日志记录。
