# 🧩 通用期货溢价套利系统 - 统一模块详细说明（第二部分：交易所和执行模块）

## 🏪 4. 交易所模块

### 4.1 BaseExchange - 交易所基类

```python
# 文件位置: exchanges/exchanges_base.py
# 职责: 定义统一接口标准

class BaseExchange(ABC):
    """交易所基类 - 统一接口定义"""
    
    @abstractmethod
    async def place_order(self, symbol: str, side: OrderSide, 
                         order_type: OrderType, amount: float, 
                         price: Optional[float] = None,
                         market_type: str = "spot") -> Dict[str, Any]:
        """统一下单接口"""
    
    @abstractmethod
    async def get_balance(self, account_type: AccountType) -> Dict[str, float]:
        """统一余额查询接口"""
    
    @abstractmethod
    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """统一持仓查询接口"""
```

### 4.2 CurrencyAdapter - 货币适配器

```python
# 文件位置: exchanges/currency_adapter.py
# 职责: 统一符号转换，支持所有交易所格式

class CurrencyAdapter:
    """货币适配器 - 统一符号转换"""
    
    def get_exchange_symbol(self, symbol: str, exchange_name: str, 
                           market_type: str = "spot") -> str:
        """获取交易所特定格式的交易对"""
        # BTC-USDT → Gate: BTC_USDT, Bybit: BTCUSDT, OKX: BTC-USDT
    
    def normalize_symbol(self, symbol: str) -> str:
        """标准化为统一格式 BTC-USDT"""
    
    def extract_base_currency(self, symbol: str) -> str:
        """提取基础币种 BTC-USDT → BTC"""
```

## 🛒 5. 交易执行模块

### 5.1 SpotTrader - 现货交易器

```python
# 文件位置: trading/spot_trader.py
# 职责: 现货交易执行，支持所有代币

class SpotTrader:
    """现货交易器"""
    
    async def market_buy(self, symbol: str, quantity: float, 
                        exchange, orderbook: Optional[Dict] = None) -> OpeningResult:
        """现货市价买入"""
        # 1. 深度检查 (orderbook)
        # 2. 精度格式化
        # 3. 执行买入订单
    
    async def market_sell(self, symbol: str, quantity: float,
                         exchange, orderbook: Optional[Dict] = None) -> ClosingResult:
        """现货市价卖出"""
        # 现货平仓使用
```

### 5.2 FuturesTrader - 期货交易器

```python
# 文件位置: trading/futures_trader.py
# 职责: 期货交易执行，支持所有代币

class FuturesTrader:
    """期货交易器"""
    
    async def market_sell_open(self, symbol: str, quantity: float,
                              exchange, orderbook: Optional[Dict] = None) -> OpeningResult:
        """期货市价做空开仓"""
        # 1. 杠杆设置
        # 2. 深度检查
        # 3. 执行做空订单
    
    async def close_position(self, symbol: str, exchange,
                           orderbook: Optional[Dict] = None) -> ClosingResult:
        """期货平仓"""
        # 自动检测持仓方向并平仓
```

## 📡 6. WebSocket模块

### 6.1 WsManager - WebSocket管理器

```python
# 文件位置: websocket/ws_manager.py
# 职责: 统一WebSocket连接管理

class WsManager:
    """WebSocket管理器"""
    
    async def start_all_connections(self) -> None:
        """启动所有WebSocket连接"""
        # 1. 启动Gate.io WebSocket
        # 2. 启动Bybit WebSocket  
        # 3. 启动OKX WebSocket
    
    def get_connection_status(self) -> Dict[str, bool]:
        """获取连接状态"""
        return {
            "gate": self.gate_ws.is_connected,
            "bybit": self.bybit_ws.is_connected,
            "okx": self.okx_ws.is_connected
        }
```

## 💰 7. 资金管理模块

### 7.1 FundManager - 资金管理器

```python
# 文件位置: fund_management/fund_manager.py
# 职责: 多交易所资金管理

class FundManager:
    """资金管理器"""
    
    async def check_all_balances(self) -> Dict[str, Dict[str, float]]:
        """检查所有交易所余额"""
        # 从ArbitrageEngine.balance_cache获取
    
    async def auto_balance_funds(self) -> None:
        """自动平衡资金分配"""
        # 调整各交易所资金至50%-50%平衡
```

## 📊 8. 监控模块

### 8.1 PositionMonitor - 仓位监控器

```python
# 文件位置: monitoring/position_monitor.py
# 职责: 实时仓位监控

class PositionMonitor:
    """仓位监控器"""
    
    async def monitor_all_positions(self) -> Dict[str, List[Dict]]:
        """监控所有持仓"""
        # 实时监控三个交易所的持仓状态
    
    def calculate_total_exposure(self) -> float:
        """计算总敞口"""
        # 计算所有持仓的总风险敞口
```

## 🔧 9. 工具模块

### 9.1 HedgeCalculator - 对冲计算器

```python
# 文件位置: utils/hedge_calculator.py
# 职责: 精确对冲质量计算

class HedgeCalculator:
    """对冲计算器"""
    
    @staticmethod
    def calculate_hedge_quality(spot_value: float, futures_value: float) -> float:
        """计算对冲质量"""
        # 返回0.0-1.0的对冲质量分数
    
    @staticmethod
    def is_hedge_acceptable(hedge_quality: float, threshold: float = 0.98) -> bool:
        """检查对冲质量是否可接受"""
        return hedge_quality >= threshold
```

### 9.2 MarginCalculator - 保证金计算器

```python
# 文件位置: utils/margin_calculator.py
# 职责: 保证金缓存+API获取系统

class MarginCalculator:
    """保证金计算器 - 5大缓存系统之一"""
    
    # 🔥 保证金缓存系统
    margin_cache = {}  # 5分钟TTL (可配置)
    
    async def get_margin_info(self, exchange: str, symbol: str) -> Dict[str, Any]:
        """获取保证金信息 - 缓存优先"""
        # 从margin_cache获取或API获取
    
    def calculate_required_margin(self, symbol: str, quantity: float, 
                                 leverage: int, price: float) -> float:
        """计算所需保证金"""
        # 基于合约信息计算保证金需求
```

## ✅ 10. 模块使用示例

### 10.1 完整套利流程示例

```python
async def complete_arbitrage_example():
    """完整套利流程示例"""
    
    # 1. 统一初始化
    modules = init_exchange_modules("Gate.io")
    opening_manager = modules["opening_manager"]
    closing_manager = modules["closing_manager"]
    
    # 2. 执行开仓
    spot_result = await opening_manager.unified_market_buy(
        symbol="BTC-USDT",
        quantity=0.001,
        exchange=gate_exchange,
        market_type="spot",
        orderbook=orderbook_data
    )
    
    # 3. 执行平仓
    close_result = await closing_manager.close_position_unified(
        symbol="BTC-USDT",
        exchange=gate_exchange,
        market_type="spot",
        side="sell",
        orderbook=orderbook_data
    )
```

### 10.2 缓存系统使用示例

```python
async def cache_system_example():
    """缓存系统使用示例"""
    
    # 1. 获取格式化金额 (零延迟)
    formatted_amount = rules_preloader.format_amount_unified(
        amount=0.001234,
        exchange="gate",
        symbol="BTC-USDT", 
        market_type="spot"
    )
    
    # 2. 获取对冲质量 (缓存)
    hedge_quality = rules_preloader.get_hedge_quality_cached(
        spot_exchange="gate",
        futures_exchange="bybit",
        symbol="BTC-USDT",
        spot_amount=0.001,
        futures_amount=0.001,
        spot_price=50000.0,
        futures_price=50100.0
    )
```

### 10.3 实际方法签名验证

```python
# ✅ 实际正确的方法签名（已验证）

# TradingRulesPreloader
def format_amount_unified(self, amount: float, exchange: str, symbol: str, market_type: str = "spot") -> str:

def truncate_to_step_size(self, amount: float, exchange: str, symbol: str, market_type: str = "spot") -> float:

def get_hedge_quality_cached(self, spot_exchange: str, futures_exchange: str, symbol: str,
                           spot_amount: float, futures_amount: float, spot_price: float, futures_price: float) -> Dict[str, Any]:

# UnifiedOpeningManager
async def unified_market_buy(self, symbol: str, quantity: float, exchange,
                           market_type: str = "spot", orderbook: Optional[Dict] = None) -> OpeningResult:

# UnifiedClosingManager  
async def close_position_unified(self, symbol: str, exchange, market_type: str,
                               side: str = None, orderbook: Dict = None) -> ClosingResult:
```

## 🔥 11. 最新修复和优化

### 11.1 WebSocket时间戳处理统一化

```python
# 🔥 修复：删除重复的_get_synced_timestamp方法
# 文件位置: websocket/gate_ws.py, bybit_ws.py, okx_ws.py

# ❌ 修复前：三个文件中都有重复方法
def _get_synced_timestamp(self, data):
    from websocket.unified_timestamp_processor import get_timestamp_processor
    processor = get_timestamp_processor("gate")  # 重复代码
    return processor.get_synced_timestamp(data)

# ✅ 修复后：直接使用统一接口
from websocket.unified_timestamp_processor import get_synced_timestamp
timestamp = get_synced_timestamp("gate", data)  # 统一调用
```

### 11.2 ArbitrageEngine统一管理器使用

```python
# 🔥 修复：ArbitrageEngine._verify_positions_cleared方法
# 文件位置: core/arbitrage_engine.py

# ❌ 修复前：直接调用交易所API
balance = await spot_exchange.get_balance()
positions = await futures_exchange.get_positions()

# ✅ 修复后：使用UnifiedBalanceManager
from core.unified_balance_manager import get_unified_balance_manager
balance_manager = get_unified_balance_manager(self.exchanges)
balance = await balance_manager.get_balance_unified(spot_exchange, AccountType.SPOT)
positions = await balance_manager.get_position_unified(futures_exchange, symbol)
```

### 11.3 PositionMonitor价格获取实现

```python
# 🔥 修复：PositionMonitor价格获取功能
# 文件位置: monitoring/position_monitor.py

# ✅ 新增：依赖注入OpportunityScanner
def __init__(self, exchanges: Dict[str, Any], opportunity_scanner=None):
    self.opportunity_scanner = opportunity_scanner

# ✅ 实现：真正的价格获取逻辑
async def _get_current_price(self, exchange_name: str, symbol: str, position_type: str) -> float:
    if not self.opportunity_scanner:
        return 0.0

    # 从OpportunityScanner.market_data获取价格
    market_data = self.opportunity_scanner.market_data
    possible_keys = [
        f"{exchange_name}_{position_type}_{symbol}",
        f"{exchange_name}_{symbol}_{position_type}",
        f"{exchange_name}_{symbol}",
    ]

    for key in possible_keys:
        if key in market_data:
            price_data = market_data[key]
            if hasattr(price_data, 'price') and price_data.price > 0:
                return float(price_data.price)

    return 0.0
```

### 11.4 修复效果总结

- **消除重复代码**: 删除21行重复代码
- **提升接口一致性**: 所有模块使用统一接口
- **增强功能完整性**: 实现缺失的价格获取功能
- **优化缓存利用**: 提高统一管理器使用率

---

**📝 注意**: 本文档为第二部分，描述交易所、执行、监控和工具模块。本文档描述的18个统一模块为系统核心架构，所有功能必须通过这些统一模块实现，严禁绕过统一模块直接调用底层API，确保系统的一致性和可维护性。最新的修复进一步强化了统一模块的使用，消除了重复代码和绕过统一接口的调用。
