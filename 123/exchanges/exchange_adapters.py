"""
交易所参数适配器
用于处理不同交易所API的参数差异
🚀 完全通用的多币种系统 - 无硬编码，支持任意交易对
"""

import os
import decimal
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional
from utils.logger import get_logger
import time

logger = logging.getLogger("exchange_adapters")

class ExchangeParamAdapter:
    """各交易所参数适配器 - 🚀 完全通用无硬编码版本"""
    
    # 🔥 移除所有硬编码配置，改为动态计算
    # 🚀 使用通用的价格区间算法，支持任意币种
    
    @classmethod
    def get_market_type_param(cls, exchange: str, market_type: str) -> str:
        """获取市场类型参数"""
        # 某些交易所参数名称不同
        if exchange.lower() in ["bybit"]:
            if market_type == "spot":
                return "spot"
            else:
                return "linear"
        elif exchange.lower() in ["okx"]:
            if market_type == "spot":
                return "SPOT"
            else:
                return "SWAP"
        else:
            return market_type
            
    @classmethod
    def adapt_order_params(cls, exchange: str, market_type: str, order_type: str, 
                          side: str, symbol: str, amount: float, price: Optional[float] = None,
                          last_price: Optional[float] = None) -> Dict[str, Any]:
        """
        调整订单参数以适应各个交易所
        🚀 完全通用版本 - 无硬编码，基于动态计算
        
        Args:
            exchange: 交易所名称 (gate, bybit, okx)
            market_type: 市场类型 (spot/futures)
            order_type: 订单类型 (limit/market)
            side: 买卖方向 (buy/sell)
            symbol: 交易对
            amount: 交易数量
            price: 价格 (限价单)
            last_price: 最新价格 (用于市价单价值估算)
            
        Returns:
            Dict[str, Any]: 适配后的订单参数
        """
        exchange_name = exchange.lower().replace(".", "")
        
        # 🔥 删除硬编码精度配置：直接使用默认价格精度，依赖重试机制
        price_precision = 2  # 默认价格精度
        
        # 🔥 删除硬编码价格格式化：直接使用原始价格，依赖重试机制
        formatted_price = None
        if price:
            try:
                formatted_price = cls._format_price(price, price_precision)
            except:
                formatted_price = cls._format_price(price, price_precision)
        
        # 根据不同交易所适配参数
        if exchange_name in ["gate", "gateio"]:
            return cls._adapt_gate_params(
                market_type, order_type, side, symbol, amount, 
                formatted_price, last_price
            )
        elif exchange_name == "bybit":
            return cls._adapt_bybit_params(
                market_type, order_type, side, symbol, amount, 
                formatted_price, last_price
            )
        elif exchange_name == "okx":
            return cls._adapt_okx_params(
                market_type, order_type, side, symbol, amount, 
                formatted_price, last_price
            )
        else:
            logger.warning(f"未知交易所: {exchange}，返回通用参数")
            return {
                "symbol": symbol,
                "side": side,
                "type": order_type,
                "amount": str(amount),
                "price": str(formatted_price) if formatted_price else None
            }
    
    @classmethod
    def _format_price(cls, price: float, precision: int) -> str:
        """格式化价格为指定精度的字符串"""
        context = decimal.getcontext()
        # 保存当前精度设置
        original_precision = context.prec
        try:
            # 设置足够高的精度避免四舍五入问题
            context.prec = 28
            
            # 使用Decimal保证精确计算
            formatted = str(Decimal(str(price)).quantize(
                Decimal('0.' + '0' * precision),
                rounding=ROUND_DOWN
            ))
            
            # 确保返回的价格字符串符合精度要求
            # 避免科学计数法格式和多余的小数位
            parts = formatted.split('.')
            if len(parts) == 1:  # 整数
                return parts[0]
            
            # 处理小数部分
            integer_part, decimal_part = parts
            decimal_part = decimal_part[:precision]  # 截断到指定精度
            
            # 去掉尾部多余的零
            decimal_part = decimal_part.rstrip('0')
            
            # 如果小数部分为空，返回整数部分
            if not decimal_part:
                return integer_part
            
            # 否则返回整数+小数点+小数部分
            return f"{integer_part}.{decimal_part}"
        finally:
            # 恢复原始精度设置
            context.prec = original_precision
    
    @classmethod
    def _adapt_gate_params(cls, market_type: str, order_type: str, side: str, 
                          symbol: str, amount: float, price: Optional[str] = None, 
                          last_price: Optional[float] = None) -> Dict[str, Any]:
        """适配Gate.io参数 - 🚀 通用版本无硬编码"""
        if market_type == "spot":
            # 现货参数
            params = {
                "currency_pair": symbol,
                "side": side,
                "type": "limit" if order_type.lower() == "limit" else "market",
                "amount": str(amount)
            }
            
            # 市价单特殊处理
            if order_type.lower() == "market":
                params["time_in_force"] = "ioc"  # 市价单必须使用IOC
                
                # 🔥 删除重复配置读取：使用配置管理器
                try:
                    from config.settings import get_config
                    config = get_config()
                    min_order_amount_usd = config.MIN_ORDER_AMOUNT_USD
                    if last_price and amount * last_price < min_order_amount_usd:
                        adjusted_amount = min_order_amount_usd / last_price
                        params["amount"] = str(adjusted_amount)
                        logger.info(f"Gate现货动态调整: {amount} -> {adjusted_amount}")
                except:
                    pass  # 使用原值
                
                # 🔥 删除重复的market buy转换逻辑
                # Gate.io市价买单的特殊处理已经在gate_exchange.py的create_spot_order方法中实现
                # 这里不再重复处理，避免双重转换错误
                
            elif price:
                params["price"] = price
                
            return params
        else:
            # 期货参数 - 🚀 通用版本，无硬编码合约大小
            # 使用标准的1:1比例，让交易所处理合约转换
            params = {
                "contract": symbol,
                # 🔥 修复：使用四舍五入而不是截断，保证对冲质量
                "size": round(amount) if side.lower() == "buy" else -round(amount),
            }
            
            # 添加交易类型特定参数
            if order_type.lower() == "limit":
                params["tif"] = "gtc"
                params["price"] = price if price else "0"
            else:
                params["tif"] = "ioc"
                params["price"] = "0"
                params["text"] = f"t-market-{int(time.time() * 1000)}"
            
            return params
    
    @classmethod
    def _adapt_bybit_params(cls, market_type: str, order_type: str, side: str, 
                           symbol: str, amount: float, price: Optional[str] = None, 
                           last_price: Optional[float] = None) -> Dict[str, Any]:
        """适配Bybit参数 - 🚀 通用版本无硬编码"""
        params = {
            "category": "spot" if market_type == "spot" else "linear",
            "symbol": symbol,
            "side": side.upper(),  # Bybit要求大写：BUY/SELL
            "orderType": order_type.upper(),  # Bybit要求大写：LIMIT/MARKET
            "qty": str(amount)
        }
        
        # 添加价格（限价单）
        if price and order_type.lower() == "limit":
            params["price"] = price
            
        # 设置时间有效期
        if order_type.lower() == "limit":
            params["timeInForce"] = "GTC"  # Good Till Canceled
        else:
            params["timeInForce"] = "IOC"  # Immediate Or Cancel
            
        # 期货需要设置仓位模式
        if market_type == "futures":
            params["positionIdx"] = 0  # 单向持仓
            params["reduceOnly"] = False  # 不是只减仓订单
            
        # 🔥 删除重复配置读取：使用配置管理器
        if order_type.lower() == "market" and market_type == "spot" and side.lower() == "buy":
            if last_price:
                try:
                    from config.settings import get_config
                    config = get_config()
                    min_order_amount_usd = config.MIN_ORDER_AMOUNT_USD
                    estimated_value = amount * last_price
                    if estimated_value < min_order_amount_usd:
                        adjusted_qty = min_order_amount_usd / last_price
                        # 🔥 修复：使用精准截取，不允许四舍五入
                        qty_str = f"{adjusted_qty:.12f}".rstrip('0').rstrip('.')
                        if '.' in qty_str:
                            integer_part, decimal_part = qty_str.split('.')
                            if len(decimal_part) > 6:
                                truncated_decimal = decimal_part[:6]  # 精准截取到6位
                            else:
                                truncated_decimal = decimal_part.ljust(6, '0')
                            params["qty"] = f"{integer_part}.{truncated_decimal}"
                        else:
                            params["qty"] = f"{qty_str}.000000"
                        logger.info(f"Bybit市价买单动态调整: {amount} -> {adjusted_qty}")
                except:
                    pass  # 使用原值
            
        return params
    
    @classmethod
    def _adapt_okx_params(cls, market_type: str, order_type: str, side: str, 
                         symbol: str, amount: float, price: Optional[str] = None, 
                         last_price: Optional[float] = None) -> Dict[str, Any]:
        """适配OKX参数 - 🚀 通用版本无硬编码"""
        params = {
            "instId": symbol,
            "tdMode": "cash" if market_type == "spot" else "cross",
            "side": side,
            "ordType": "limit" if order_type.lower() == "limit" else "market",
            "sz": str(amount)
        }
        
        # 限价单设置价格
        if order_type.lower() == "limit" and price:
            params["px"] = price
            
        # 现货市价买单使用交易额
        if order_type.lower() == "market" and side.lower() == "buy" and market_type == "spot":
            # 使用计价货币(USDT)金额
            params["tgtCcy"] = "quote_ccy"
            
            # 🔥 修复：使用统一计算器估算交易额
            if last_price:
                try:
                    from core.unified_amount_calculator import get_unified_amount_calculator
                    amount_calculator = get_unified_amount_calculator()

                    estimated_value = amount * last_price
                    if estimated_value < amount_calculator.min_order_amount_usd:
                        estimated_value = amount_calculator.min_order_amount_usd

                    # 使用精准截取
                    import math
                    truncated_value = math.floor(estimated_value * 100) / 100  # 2位精度截取
                    params["sz"] = str(truncated_value)
                except:
                    pass  # 使用原值
                
        # 期货不使用tgtCcy参数
        if market_type == "futures" and "tgtCcy" in params:
            del params["tgtCcy"]
            
        return params
        
    @classmethod
    def get_min_trade_amount(cls, exchange: str, symbol: str, market_type: str, current_price: float = None) -> float:
        """
        获取最小交易量 - 🔥 使用统一金额计算器，确保一致性
        """
        # 🔥 修复：使用统一金额计算器
        from core.unified_amount_calculator import get_unified_amount_calculator
        amount_calculator = get_unified_amount_calculator()

        if current_price and current_price > 0:
            # 使用统一计算器计算基础金额
            result = amount_calculator.calculate_base_amount(current_price)
            return result.final_amount
        else:
            # 没有价格时返回默认最小值
            return 0.001


    
    @classmethod
    def calculate_safe_amount(cls, exchange: str, symbol: str, market_type: str,
                            amount: float, price: float) -> float:
        """
        计算安全的交易量，确保满足最小交易额要求
        🔥 使用统一金额计算器，确保一致性
        """
        # 🔥 修复：使用统一金额计算器
        from core.unified_amount_calculator import get_unified_amount_calculator
        amount_calculator = get_unified_amount_calculator()

        # 使用统一计算器获取安全金额
        safe_amount = amount_calculator.get_safe_amount(exchange, symbol, market_type, amount, price)

        return safe_amount


# 🔥 生产环境：移除测试代码，保持适配器模块纯净