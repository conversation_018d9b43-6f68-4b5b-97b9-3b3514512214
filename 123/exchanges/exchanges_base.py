"""
交易所基类
提供统一的交易所接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime
import time
import asyncio
import os
from enum import Enum
import decimal

# 导入参数适配器
from exchanges.exchange_adapters import ExchangeParamAdapter

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """订单类型 - 🔥 纯市价单系统"""
    MARKET = "market"
    # LIMIT和LIMIT_MAKER已彻底删除


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    OPEN = "open"
    PARTIAL = "partial"
    FILLED = "filled"
    CANCELLED = "cancelled"
    FAILED = "failed"


class AccountType(Enum):
    """账户类型"""
    SPOT = "spot"
    FUTURES = "futures"
    UNIFIED = "unified"  # 统一账户


class BaseExchange(ABC):
    """交易所基类"""
    
    def __init__(self, name: str, api_key: str, api_secret: str, **kwargs):
        self.name = name
        self.exchange_name = name  # 🔥 添加exchange_name属性以保持兼容性
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = kwargs.get("passphrase", "")  # OKX需要
        
        # 请求限制 - 从环境变量读取，默认每秒10次
        default_rate_limit = 10
        env_rate_limit = os.environ.get(f"{name.upper().replace('.', '')}_RATE_LIMIT")
        self.rate_limit = int(env_rate_limit) if env_rate_limit else kwargs.get("rate_limit", default_rate_limit)
        self.last_request_time = 0
        
        # 统计信息
        self.request_count = 0
        self.error_count = 0
        
        logger.info(f"初始化{name}交易所接口，API请求限制: {self.rate_limit}/秒")
    
    @abstractmethod
    async def get_balance(self, account_type: AccountType = AccountType.SPOT) -> Dict[str, Any]:
        """
        获取账户余额 - 统一接口

        Args:
            account_type: 账户类型，默认为SPOT现货账户

        Returns:
            Dict[str, Any]: 格式为 {"USDT": {"available": 100.0, "locked": 0.0}, "BTC": {...}}
        """
        raise NotImplementedError("Subclasses must implement get_balance method")
    
    # 🔥 删除RestAPI价格获取抽象方法 - 统一使用WebSocket+OpportunityScanner.market_data
    # @abstractmethod async def get_ticker() 已删除，改为使用 OpportunityScanner.market_data
    # 所有价格数据通过WebSocket实时获取，避免RestAPI延迟
    
    @abstractmethod
    async def get_orderbook(self, symbol: str, market_type: str, limit: int = 10) -> Dict[str, Any]:
        """
        🚨 获取订单簿 - 严禁REST API调用，必须重定向到WebSocket数据源
        
        根据项目文档要求：
        - ✅ 订单簿100%来自WebSocket，完全无缓存，延迟<30ms
        - 🔧 从混用改为纯WebSocket+短缓存，删除REST API
        - ✅ 订单簿时间戳对齐：深度数据同步性重要
        
        实现要求：
        1. 必须调用ExecutionEngine._get_websocket_orderbook()
        2. 严禁直接调用REST API
        3. 失败时返回空订单簿，不得使用REST API备用
        
        :param symbol: 交易对
        :param market_type: 市场类型
        :param limit: 深度档位
        :return: WebSocket订单簿数据
        """
        pass
    
    @abstractmethod
    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        market_type: str = "spot",
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        下单
        :param symbol: 交易对
        :param side: 买卖方向
        :param order_type: 订单类型
        :param amount: 数量
        :param price: 价格（市价单不需要）
        :param market_type: 市场类型
        :param params: 额外参数，用于传递交易所特定参数
        :return: 订单信息
        
        注意：子类实现时应当使用ExchangeParamAdapter进行参数适配，
             并确保交易量满足最小要求
        """
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str, market_type: str = "spot") -> bool:
        """
        取消订单
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 是否成功
        """
        pass
    
    @abstractmethod
    async def get_order(self, order_id: str, symbol: str, market_type: str = "spot") -> Dict[str, Any]:
        """
        查询订单
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 订单信息
        """
        pass

    @abstractmethod
    async def get_order_status(self, order_id: str, symbol: str, market_type: str = "spot") -> str:
        """
        获取订单状态
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 订单状态
        """
        pass

    @abstractmethod
    async def get_trading_rules(self, symbol: str = None) -> Dict[str, Any]:
        """
        获取交易规则
        :param symbol: 交易对，None表示所有
        :return: 交易规则
        """
        pass

    @abstractmethod
    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """
        获取持仓（期货）
        :param symbol: 交易对，None表示所有
        :return: 持仓列表
        """
        pass
    
    @abstractmethod
    async def close_position(self, symbol: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """
        平仓（期货）
        :param symbol: 交易对
        :param amount: 数量，None表示全部
        :return: 平仓结果
        """
        pass
    
    @abstractmethod
    async def transfer_funds(
        self,
        currency: str,
        amount: float,
        from_account: AccountType,
        to_account: AccountType
    ) -> bool:
        """
        内部转账
        :param currency: 币种
        :param amount: 数量
        :param from_account: 源账户
        :param to_account: 目标账户
        :return: 是否成功
        """
        pass
    
    @abstractmethod
    def is_unified_account(self) -> bool:
        """
        是否为统一账户
        :return: bool
        """
        pass
    
    @abstractmethod
    async def get_server_time(self) -> int:
        """
        获取服务器时间戳
        :return: 时间戳（毫秒）
        """
        pass
    
    async def _rate_limit(self):
        """速率限制"""
        current_time = time.time()
        time_diff = current_time - self.last_request_time
        min_interval = 1.0 / self.rate_limit
        
        if time_diff < min_interval:
            await asyncio.sleep(min_interval - time_diff)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def _normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对格式
        :param symbol: 原始交易对
        :return: 标准化后的交易对
        """
        # 基础实现，子类可以覆盖
        return symbol.upper().replace("-", "")
    
    def _parse_order_status(self, status: str) -> OrderStatus:
        """
        解析订单状态
        :param status: 原始状态
        :return: 标准化状态
        """
        status_map = {
            "new": OrderStatus.OPEN,
            "open": OrderStatus.OPEN,
            "partial": OrderStatus.PARTIAL,
            "filled": OrderStatus.FILLED,
            "done": OrderStatus.FILLED,
            "cancelled": OrderStatus.CANCELLED,
            "canceled": OrderStatus.CANCELLED,
            "failed": OrderStatus.FAILED
        }
        return status_map.get(status.lower(), OrderStatus.PENDING)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "exchange": self.name,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "success_rate": (self.request_count - self.error_count) / max(self.request_count, 1)
        }



    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            balance = await self.get_balance(AccountType.SPOT)
            return True
        except Exception as e:
            logger.error(f"{self.name}连接测试失败: {e}")
            return False

    def get_min_trade_amount(self, symbol: str, market_type: str = "spot") -> float:
        """
        获取指定交易对的最小交易量
        :param symbol: 交易对
        :param market_type: 市场类型 spot/futures
        :return: 最小交易量
        """
        # 从环境变量或配置中读取
        return ExchangeParamAdapter.get_min_trade_amount(self.name, symbol, market_type)
    
    def calculate_safe_amount(self, symbol: str, market_type: str, amount: float, price: float) -> float:
        """
        计算符合交易所要求的安全交易量
        
        Args:
            symbol: 交易对
            market_type: 市场类型 (spot/futures)
            amount: 原始交易量
            price: 当前价格
            
        Returns:
            安全交易量
        """
        # 使用适配器计算安全交易量
        exchange_name = self.name.lower().replace(".", "")
        return ExchangeParamAdapter.calculate_safe_amount(
            exchange_name, symbol, market_type, amount, price
        )
    
    async def get_last_price(self, symbol: str, market_type: str) -> float:
        """
        获取最新成交价 - 🔥 修复：使用WebSocket缓存替代RestAPI
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 最新价格
        """
        try:
            # 🔥 修复：废弃方法，建议使用OpportunityScanner.market_data
            logger.warning(f"get_last_price已废弃，建议直接使用OpportunityScanner.market_data")
            logger.warning(f"推荐用法: opportunity_scanner.market_data.get('{self.name.lower()}_{market_type}_{symbol}')")
            return 0.0

        except Exception as e:
            logger.error(f"获取{symbol}最新价格失败: {e}")
            return 0.0

    async def get_server_time(self) -> int:
        """
        获取服务器时间 - 🔥 统一实现，子类可重写
        :return: 服务器时间戳（毫秒）
        """
        try:
            # 默认返回本地时间，子类可重写获取真实服务器时间
            return int(time.time() * 1000)
        except Exception as e:
            logger.warning(f"获取服务器时间失败: {e}，使用本地时间")
            return int(time.time() * 1000)

    async def get_current_price(self, symbol: str, market_type: str) -> float:
        """
        获取当前价格 - 🔥 统一实现，删除子类重复
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 当前价格
        """
        return await self.get_last_price(symbol, market_type)

    def _handle_common_errors(self, error: Exception, operation: str) -> bool:
        """
        统一错误处理 - 🔥 删除子类重复错误处理逻辑
        :param error: 异常对象
        :param operation: 操作类型
        :return: True表示错误已处理，False表示需要重新抛出
        """
        error_str = str(error)

        # 订单不存在错误 - 通用处理
        order_not_found_keywords = [
            'ORDER_NOT_FOUND', 'Order not found', 'order not found', 'not found',
            '51400', 'Order cancellation failed', 'has been filled, canceled or does not exist',
            'sCode\': \'51400\'', 'OKX_ORDER_ALREADY_PROCESSED_51400'
        ]

        if operation == "cancel_order" and any(keyword in error_str for keyword in order_not_found_keywords):
            logger.info(f"{self.name}订单不存在或已处理，视为取消成功")
            return True

        # 交易对不存在错误 - 通用处理
        symbol_invalid_keywords = [
            '10001', 'symbol invalid', 'Invalid symbol', 'trading pair not found'
        ]

        if any(keyword in error_str for keyword in symbol_invalid_keywords):
            logger.debug(f"{self.name}交易对不存在 - 这是正常情况")
            return False  # 让上层处理

        # 杠杆相关错误 - 通用处理
        leverage_keywords = [
            'POSITION_NOT_FOUND', '110043', 'leverage not modified', 'INVALID_LEVERAGE'
        ]

        if operation == "set_leverage" and any(keyword in error_str for keyword in leverage_keywords):
            if 'POSITION_NOT_FOUND' in error_str or '110043' in error_str:
                logger.info(f"{self.name}杠杆状态正常或仓位不存在")
                return True
            elif 'INVALID_LEVERAGE' in error_str:
                logger.error(f"{self.name}杠杆值无效")
                return False

        # 账户模式错误 - 通用处理
        account_mode_keywords = ['51070', '51008', '51003']

        if any(keyword in error_str for keyword in account_mode_keywords):
            logger.warning(f"{self.name}账户模式限制，但不影响交易")
            return True

        return False  # 未处理的错误，需要重新抛出

    def _format_orderbook_data(self, asks: list, bids: list, symbol: str, timestamp: int = None) -> dict:
        """
        🔥 统一订单簿数据格式化 - 使用统一格式化器，消除重复代码
        :param asks: 卖单数据
        :param bids: 买单数据
        :param symbol: 交易对
        :param timestamp: 时间戳
        :return: 标准格式的订单簿数据
        """
        try:
            # 🔥 使用统一格式化器，避免重复实现
            from websocket.unified_data_formatter import format_orderbook_data

            return format_orderbook_data(
                asks=asks,
                bids=bids,
                symbol=symbol,
                exchange=self.name.lower(),
                market_type="spot",  # 默认现货，子类可重写
                timestamp=timestamp
            )

        except Exception as e:
            logger.error(f"{self.name}订单簿格式化失败: {e}")
            # 返回空的标准格式数据
            return {
                "symbol": symbol,
                "price": 0,
                "asks": [],
                "bids": [],
                "timestamp": timestamp or int(time.time() * 1000),
                "exchange": self.name.lower(),
                "market_type": "spot",
                "incomplete_orderbook": True,
                "incomplete_reason": f"格式化失败: {str(e)}"
            }


# 辅助函数
def calculate_order_value(price: float, amount: float) -> float:
    """计算订单价值"""
    return price * amount


def calculate_slippage(expected_price: float, actual_price: float) -> float:
    """计算滑点"""
    return abs(actual_price - expected_price) / expected_price


def check_order_match(order1: Dict, order2: Dict, threshold: float = 0.001) -> bool:
    """
    检查两个订单是否匹配
    :param order1: 订单1
    :param order2: 订单2
    :param threshold: 阈值
    :return: 是否匹配
    """
    amount_diff = abs(order1["amount"] - order2["amount"]) / max(order1["amount"], order2["amount"])
    value_diff = abs(order1["value"] - order2["value"]) / max(order1["value"], order2["value"])
    
    return amount_diff < threshold and value_diff < threshold


if __name__ == "__main__":
    # 测试代码
    class TestExchange(BaseExchange):
        async def get_balance(self, account_type: AccountType = AccountType.SPOT) -> Dict[str, Any]:
            return {"USDT": 1000.0, "BTC": 0.1}
        
        # 🔥 删除RestAPI价格获取方法 - 统一使用WebSocket+OpportunityScanner.market_data
        # async def get_ticker() 方法已删除，改为使用 OpportunityScanner.market_data
        
        async def get_orderbook(self, symbol: str, market_type: str, limit: int = 10) -> Dict[str, Any]:
            return {
                "symbol": symbol,
                "bids": [[49990.0, 1.0], [49980.0, 2.0]],
                "asks": [[50010.0, 1.0], [50020.0, 2.0]]
            }
        
        async def place_order(self, symbol: str, side: OrderSide, order_type: OrderType, 
                            amount: float, price: Optional[float] = None, 
                            market_type: str = "spot", **kwargs) -> Dict[str, Any]:
            return {
                "order_id": "12345",
                "symbol": symbol,
                "side": side.value,
                "type": order_type.value,
                "amount": amount,
                "price": price,
                "status": "open"
            }
        
        async def cancel_order(self, order_id: str, symbol: str, market_type: str = "spot") -> bool:
            return True
        
        async def get_order(self, order_id: str, symbol: str, market_type: str = "spot") -> Dict[str, Any]:
            return {
                "order_id": order_id,
                "symbol": symbol,
                "status": "filled",
                "filled_amount": 1.0,
                "filled_value": 50000.0
            }
        
        async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
            return []
        
        async def close_position(self, symbol: str, amount: Optional[float] = None) -> Dict[str, Any]:
            return {"success": True}
        
        async def transfer_funds(self, currency: str, amount: float, 
                               from_account: AccountType, to_account: AccountType) -> bool:
            return True
        
        def is_unified_account(self) -> bool:
            return True
        
        async def get_server_time(self) -> int:
            return int(time.time() * 1000)
    
    # 🔥 生产环境：移除测试代码，保持基类纯净


def get_exchange_name(exchange) -> str:
    """
    🔥 统一的交易所名称获取函数 - 消除重复实现

    Args:
        exchange: 交易所实例或字符串

    Returns:
        str: 标准化的交易所名称 (小写)
    """
    try:
        # 处理None输入
        if exchange is None:
            return "unknown"

        # 方法1: 如果是字符串，直接返回
        if isinstance(exchange, str):
            return exchange.lower()

        # 方法2: 尝试获取类名
        class_name = exchange.__class__.__name__.lower()
        if 'exchange' in class_name:
            # 移除'exchange'后缀：BybitExchange -> bybit
            return class_name.replace('exchange', '')

        # 方法3: 尝试获取name属性
        if hasattr(exchange, 'name'):
            return exchange.name.lower()

        # 方法4: 尝试获取exchange_name属性
        if hasattr(exchange, 'exchange_name'):
            return exchange.exchange_name.lower()

        # 方法5: 默认返回类名
        return class_name

    except Exception as e:
        logger.warning(f"⚠️ 获取交易所名称失败: {e}")
        return "unknown"