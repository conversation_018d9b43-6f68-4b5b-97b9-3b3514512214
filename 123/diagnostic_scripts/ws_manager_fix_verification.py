#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket状态监控修复验证脚本
验证reconnect_tasks未定义错误是否已修复
"""

import ast
import sys
import os
from typing import Dict, List, Any

class WSManagerFixVerification:
    def __init__(self):
        self.ws_manager_path = "C:\\Users\\<USER>\\Desktop\\66 修复websocket\\123\\websocket\\ws_manager.py"
        self.results = {
            "syntax_check": False,
            "initialization_check": False,
            "usage_check": False,
            "method_analysis": {},
            "overall_status": "UNKNOWN"
        }
    
    def verify_fix(self) -> Dict[str, Any]:
        """验证修复是否有效"""
        print("Starting WebSocket status monitoring fix verification...")
        
        try:
            # 1. 语法检查
            self._check_syntax()
            
            # 2. 检查初始化
            self._check_initialization()
            
            # 3. 检查变量使用
            self._check_variable_usage()
            
            # 4. 分析修复质量
            self._analyze_fix_quality()
            
            # 5. 生成最终结果
            self._generate_final_result()
            
            return self.results
            
        except Exception as e:
            print(f"验证过程出错: {e}")
            self.results["overall_status"] = "ERROR"
            self.results["error"] = str(e)
            return self.results
    
    def _check_syntax(self):
        """检查语法是否正确"""
        print("Checking syntax correctness...")
        
        try:
            with open(self.ws_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试解析AST
            ast.parse(content)
            self.results["syntax_check"] = True
            print("   Syntax check passed")
            
        except SyntaxError as e:
            print(f"   Syntax error: {e}")
            self.results["syntax_check"] = False
            self.results["syntax_error"] = str(e)
        except Exception as e:
            print(f"   Syntax check exception: {e}")
            self.results["syntax_check"] = False
    
    def _check_initialization(self):
        """检查reconnect_tasks是否正确初始化"""
        print("Checking reconnect_tasks initialization...")
        
        try:
            with open(self.ws_manager_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找_monitor_status方法
            in_monitor_method = False
            method_start_line = 0
            initialization_found = False
            
            for i, line in enumerate(lines):
                if 'async def _monitor_status(self):' in line:
                    in_monitor_method = True
                    method_start_line = i + 1
                    continue
                    
                if in_monitor_method:
                    # 检查是否到达下一个方法
                    if line.strip().startswith('async def ') or line.strip().startswith('def '):
                        if 'async def _monitor_status(self):' not in line:
                            break
                    
                    # 检查是否有reconnect_tasks初始化
                    if 'reconnect_tasks = []' in line:
                        initialization_found = True
                        try:
                            print(f"   Found initialization at line {i+1}: {line.strip()}")
                        except UnicodeEncodeError:
                            print(f"   Found initialization at line {i+1}: [Line contains non-ASCII characters]")
                        break
            
            self.results["initialization_check"] = initialization_found
            self.results["method_analysis"]["initialization_line"] = method_start_line
            
            if initialization_found:
                print("   Initialization check passed")
            else:
                print("   Initialization check failed: reconnect_tasks initialization not found")
                
        except Exception as e:
            print(f"   Initialization check exception: {e}")
            self.results["initialization_check"] = False
    
    def _check_variable_usage(self):
        """检查变量使用是否正确"""
        print("Checking variable usage...")
        
        try:
            with open(self.ws_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有reconnect_tasks使用
            usage_lines = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                if 'reconnect_tasks' in line:
                    try:
                        content = line.strip()
                    except UnicodeEncodeError:
                        content = "[Line contains non-ASCII characters]"
                    
                    usage_lines.append({
                        "line_number": i,
                        "content": content,
                        "type": self._classify_usage_type(line)
                    })
            
            self.results["method_analysis"]["usage_lines"] = usage_lines
            
            # 检查是否所有使用都在初始化之后
            init_lines = [usage for usage in usage_lines if usage["type"] == "initialization"]
            other_uses = [usage for usage in usage_lines if usage["type"] != "initialization"]
            
            if init_lines and other_uses:
                # 确保初始化在使用之前
                min_init_line = min(usage["line_number"] for usage in init_lines)
                max_usage_line = max(usage["line_number"] for usage in other_uses)
                
                if min_init_line < max_usage_line:
                    self.results["usage_check"] = True
                    print(f"   Variable usage check passed (init at line {min_init_line}, used until line {max_usage_line})")
                else:
                    self.results["usage_check"] = False
                    print("   Variable usage check failed: initialization after usage")
            else:
                self.results["usage_check"] = len(init_lines) > 0
                print(f"   Found {len(init_lines)} initializations, {len(other_uses)} usages")
                
        except Exception as e:
            print(f"   Variable usage check exception: {e}")
            self.results["usage_check"] = False
    
    def _classify_usage_type(self, line: str) -> str:
        """分类使用类型"""
        line = line.strip()
        if 'reconnect_tasks = []' in line:
            return "initialization"
        elif 'reconnect_tasks.extend(' in line:
            return "extend"
        elif 'reconnect_tasks.append(' in line:
            return "append"
        elif 'if reconnect_tasks:' in line:
            return "condition"
        else:
            return "other"
    
    def _analyze_fix_quality(self):
        """分析修复质量"""
        print("Analyzing fix quality...")
        
        quality_score = 0
        max_score = 100
        
        # 语法正确性 (40分)
        if self.results["syntax_check"]:
            quality_score += 40
            print("   Syntax correctness: 40/40")
        else:
            print("   Syntax correctness: 0/40")
        
        # 初始化正确性 (30分)
        if self.results["initialization_check"]:
            quality_score += 30
            print("   Initialization correctness: 30/30")
        else:
            print("   Initialization correctness: 0/30")
        
        # 变量使用正确性 (30分)
        if self.results["usage_check"]:
            quality_score += 30
            print("   Variable usage correctness: 30/30")
        else:
            print("   Variable usage correctness: 0/30")
        
        self.results["quality_score"] = quality_score
        self.results["max_score"] = max_score
        self.results["quality_percentage"] = (quality_score / max_score) * 100
        
        print(f"   Fix quality score: {quality_score}/{max_score} ({self.results['quality_percentage']:.1f}%)")
    
    def _generate_final_result(self):
        """生成最终结果"""
        if self.results["quality_percentage"] >= 90:
            self.results["overall_status"] = "EXCELLENT"
        elif self.results["quality_percentage"] >= 70:
            self.results["overall_status"] = "GOOD"
        elif self.results["quality_percentage"] >= 50:
            self.results["overall_status"] = "ACCEPTABLE"
        else:
            self.results["overall_status"] = "NEEDS_IMPROVEMENT"
    
    def print_verification_report(self):
        """打印验证报告"""
        print("\n" + "="*60)
        print("WEBSOCKET STATUS MONITORING FIX VERIFICATION REPORT")
        print("="*60)
        
        print(f"\nOverall Status: {self.results['overall_status']}")
        print(f"Fix Quality: {self.results.get('quality_percentage', 0):.1f}%")
        
        print(f"\nCheck Results:")
        print(f"   Syntax check: {'Passed' if self.results['syntax_check'] else 'Failed'}")
        print(f"   Initialization check: {'Passed' if self.results['initialization_check'] else 'Failed'}")
        print(f"   Variable usage check: {'Passed' if self.results['usage_check'] else 'Failed'}")
        
        if "usage_lines" in self.results["method_analysis"]:
            usage_lines = self.results["method_analysis"]["usage_lines"]
            print(f"\nVariable usage details ({len(usage_lines)} instances):")
            for usage in usage_lines:
                try:
                    print(f"   Line {usage['line_number']} [{usage['type']}]: {usage['content']}")
                except UnicodeEncodeError:
                    print(f"   Line {usage['line_number']} [{usage['type']}]: [Line contains non-ASCII characters]")
        
        # 修复建议
        if self.results["overall_status"] not in ["EXCELLENT", "GOOD"]:
            print(f"\nFix recommendations:")
            if not self.results["syntax_check"]:
                print("   - Fix syntax errors")
            if not self.results["initialization_check"]:
                print("   - Add reconnect_tasks = [] at the beginning of _monitor_status method")
            if not self.results["usage_check"]:
                print("   - Ensure variable is properly initialized before use")

def main():
    """主函数"""
    print("Starting WebSocket status monitoring fix verification...")
    
    verifier = WSManagerFixVerification()
    results = verifier.verify_fix()
    
    verifier.print_verification_report()
    
    # Return verification results for further processing
    return results

if __name__ == "__main__":
    main()