#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易规则预加载错误诊断脚本
分析2025-07-30错误日志中的"无法获取交易规则"问题
"""

import asyncio
import logging
import sys
import os
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger

class TradingRulesErrorDiagnosis:
    """交易规则错误诊断器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.error_patterns = []
        self.diagnosis_results = {}
        
    def analyze_error_log(self, log_file_path: str):
        """分析错误日志文件"""
        self.logger.info("🔍 开始分析错误日志...")
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 分析错误模式
            trading_rule_errors = []
            margin_calculator_errors = []
            
            for line in lines:
                if "无法获取交易规则" in line:
                    trading_rule_errors.append(line.strip())
                elif "获取合约信息失败" in line:
                    margin_calculator_errors.append(line.strip())
                    
            self.logger.info(f"📊 错误统计:")
            self.logger.info(f"   交易规则错误: {len(trading_rule_errors)}条")
            self.logger.info(f"   保证金计算错误: {len(margin_calculator_errors)}条")
            
            # 分析错误模式
            self._analyze_trading_rule_errors(trading_rule_errors)
            self._analyze_margin_calculator_errors(margin_calculator_errors)
            
        except Exception as e:
            self.logger.error(f"❌ 分析错误日志失败: {e}")
            
    def _analyze_trading_rule_errors(self, errors: List[str]):
        """分析交易规则错误"""
        self.logger.info("🔍 分析交易规则错误模式...")
        
        # 提取错误的交易对信息
        error_symbols = set()
        error_exchanges = set()
        error_markets = set()
        
        for error in errors:
            # 解析错误信息：SPK-USDT_gate_spot
            if "_" in error:
                parts = error.split("_")
                if len(parts) >= 3:
                    symbol_part = parts[0].split(": ")[-1] if ": " in parts[0] else parts[0]
                    exchange = parts[1]
                    market = parts[2]
                    
                    error_symbols.add(symbol_part)
                    error_exchanges.add(exchange)
                    error_markets.add(market)
                    
        self.logger.info(f"📊 交易规则错误分析:")
        self.logger.info(f"   涉及交易对: {len(error_symbols)}个 - {sorted(error_symbols)}")
        self.logger.info(f"   涉及交易所: {len(error_exchanges)}个 - {sorted(error_exchanges)}")
        self.logger.info(f"   涉及市场: {len(error_markets)}个 - {sorted(error_markets)}")
        
        # 分析可能的原因
        self._diagnose_trading_rule_causes(error_symbols, error_exchanges, error_markets)
        
    def _analyze_margin_calculator_errors(self, errors: List[str]):
        """分析保证金计算错误"""
        self.logger.info("🔍 分析保证金计算错误模式...")
        
        error_pairs = set()
        for error in errors:
            if "okx_" in error:
                # 提取交易对信息
                parts = error.split("okx_")
                if len(parts) > 1:
                    pair_info = parts[1].split(" ")[0]
                    error_pairs.add(pair_info)
                    
        self.logger.info(f"📊 保证金计算错误分析:")
        self.logger.info(f"   涉及交易对: {len(error_pairs)}个 - {sorted(error_pairs)}")
        
    def _diagnose_trading_rule_causes(self, symbols: set, exchanges: set, markets: set):
        """诊断交易规则错误的可能原因"""
        self.logger.info("🔍 诊断交易规则错误原因...")
        
        possible_causes = []
        
        # 1. 检查是否是新代币或小众代币
        small_cap_tokens = {"SPK", "RESOLV", "ICNT", "AI16Z"}
        if symbols.intersection(small_cap_tokens):
            possible_causes.append("新代币或小众代币，部分交易所可能不支持")
            
        # 2. 检查是否是特定交易所问题
        if len(exchanges) < 3:
            possible_causes.append(f"特定交易所问题: {exchanges}")
            
        # 3. 检查是否是特定市场类型问题
        if len(markets) < 2:
            possible_causes.append(f"特定市场类型问题: {markets}")
            
        # 4. 通用原因
        possible_causes.extend([
            "API频率限制导致请求失败",
            "网络连接问题",
            "交易所API临时不可用",
            "交易对在某些交易所已下线",
            "配置文件中的交易对列表需要更新"
        ])
        
        self.logger.info("🎯 可能的原因:")
        for i, cause in enumerate(possible_causes, 1):
            self.logger.info(f"   {i}. {cause}")
            
    async def test_trading_rules_loading(self):
        """测试交易规则加载功能"""
        self.logger.info("🧪 测试交易规则加载功能...")
        
        try:
            # 导入必要模块
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import get_global_exchanges
            
            # 获取交易规则预加载器
            preloader = get_trading_rules_preloader()
            
            # 获取全局交易所
            exchanges = get_global_exchanges()
            
            if not exchanges:
                self.logger.warning("⚠️ 未找到全局交易所实例，尝试初始化...")
                # 这里可以添加交易所初始化逻辑
                return False
                
            # 测试几个代币的交易规则加载
            test_symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
            test_exchanges = ["gate", "bybit", "okx"]
            test_markets = ["spot", "futures"]
            
            success_count = 0
            total_tests = 0
            
            for symbol in test_symbols:
                for exchange_name in test_exchanges:
                    if exchange_name in exchanges:
                        for market_type in test_markets:
                            total_tests += 1
                            
                            # 测试获取交易规则
                            rule = preloader.get_trading_rule(exchange_name, symbol, market_type)
                            
                            if rule:
                                success_count += 1
                                self.logger.info(f"✅ {exchange_name}_{symbol}_{market_type}: 成功")
                            else:
                                self.logger.warning(f"❌ {exchange_name}_{symbol}_{market_type}: 失败")
                                
            success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
            self.logger.info(f"📊 测试结果: {success_count}/{total_tests} ({success_rate:.1f}%)")
            
            return success_rate > 50  # 50%以上成功率认为正常
            
        except Exception as e:
            self.logger.error(f"❌ 测试交易规则加载失败: {e}")
            return False
            
    async def check_system_configuration(self):
        """检查系统配置"""
        self.logger.info("🔍 检查系统配置...")
        
        try:
            # 检查环境变量
            required_env_vars = [
                "TARGET_SYMBOLS",
                "TRADING_RULES_TTL",
                "HEDGE_QUALITY_TTL",
                "PRECISION_CACHE_TTL"
            ]
            
            missing_vars = []
            for var in required_env_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
                    
            if missing_vars:
                self.logger.warning(f"⚠️ 缺少环境变量: {missing_vars}")
            else:
                self.logger.info("✅ 环境变量配置完整")
                
            # 检查TARGET_SYMBOLS配置
            target_symbols = os.getenv("TARGET_SYMBOLS", "")
            if target_symbols:
                symbols = [s.strip() for s in target_symbols.split(",")]
                self.logger.info(f"📊 配置的交易对: {len(symbols)}个")
                self.logger.info(f"   交易对列表: {symbols}")
            else:
                self.logger.warning("⚠️ TARGET_SYMBOLS未配置")
                
            return len(missing_vars) == 0
            
        except Exception as e:
            self.logger.error(f"❌ 检查系统配置失败: {e}")
            return False
            
    def generate_fix_recommendations(self):
        """生成修复建议"""
        self.logger.info("💡 生成修复建议...")
        
        recommendations = [
            "1. 检查网络连接和API访问权限",
            "2. 验证交易所API密钥配置是否正确",
            "3. 检查TARGET_SYMBOLS中的交易对是否在所有交易所都支持",
            "4. 考虑移除不支持的小众代币",
            "5. 增加API调用重试机制和错误处理",
            "6. 实施交易对支持性预检查",
            "7. 添加交易规则缓存预热机制",
            "8. 优化API调用频率，避免触发限制"
        ]
        
        self.logger.info("🔧 修复建议:")
        for rec in recommendations:
            self.logger.info(f"   {rec}")
            
    async def run_comprehensive_diagnosis(self, log_file_path: str):
        """运行综合诊断"""
        self.logger.info("🚀 开始综合诊断...")
        
        # 1. 分析错误日志
        self.analyze_error_log(log_file_path)
        
        # 2. 检查系统配置
        config_ok = await self.check_system_configuration()
        
        # 3. 测试交易规则加载
        loading_ok = await self.test_trading_rules_loading()
        
        # 4. 生成修复建议
        self.generate_fix_recommendations()
        
        # 5. 综合评估
        self.logger.info("📊 综合诊断结果:")
        self.logger.info(f"   系统配置: {'✅ 正常' if config_ok else '❌ 异常'}")
        self.logger.info(f"   交易规则加载: {'✅ 正常' if loading_ok else '❌ 异常'}")
        
        overall_status = "正常" if (config_ok and loading_ok) else "需要修复"
        self.logger.info(f"   整体状态: {overall_status}")
        
        return config_ok and loading_ok


async def main():
    """主函数"""
    diagnosis = TradingRulesErrorDiagnosis()
    
    # 错误日志文件路径
    log_file_path = "123/logs/error_20250730.log"
    
    if not os.path.exists(log_file_path):
        diagnosis.logger.error(f"❌ 错误日志文件不存在: {log_file_path}")
        return
        
    # 运行综合诊断
    success = await diagnosis.run_comprehensive_diagnosis(log_file_path)
    
    if success:
        diagnosis.logger.info("🎉 诊断完成，系统状态正常")
    else:
        diagnosis.logger.warning("⚠️ 诊断完成，发现问题需要修复")


if __name__ == "__main__":
    asyncio.run(main())