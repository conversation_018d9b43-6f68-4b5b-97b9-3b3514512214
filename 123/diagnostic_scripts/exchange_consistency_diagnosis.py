#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 三交易所一致性诊断脚本
专门检查 Bybit、Gate.io、OKX 三个交易所的实现一致性问题

基于代码审查发现的潜在问题：
1. 各交易所精度处理不一致
2. 异步API调用模式差异  
3. 数据格式标准化问题
4. 错误处理机制不统一
"""

import asyncio
import sys
import os
import json
import time
import traceback
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class ExchangeConsistencyDiagnostic:
    """交易所一致性诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "exchange_tests": {
                "bybit": {"tests": [], "status": "unknown"},
                "gate": {"tests": [], "status": "unknown"}, 
                "okx": {"tests": [], "status": "unknown"}
            },
            "consistency_issues": [],
            "critical_inconsistencies": [],
            "precision_inconsistencies": [],
            "async_pattern_inconsistencies": [],
            "summary": {
                "total_tests": 0,
                "consistent": 0,
                "inconsistent": 0,
                "critical": 0
            }
        }
        
    def log_inconsistency(self, category: str, severity: str, description: str, 
                         exchanges_affected: List[str], details: Dict[str, Any] = None):
        """记录不一致性问题"""
        issue = {
            "category": category,
            "severity": severity,
            "description": description,
            "exchanges_affected": exchanges_affected,
            "details": details or {},
            "timestamp": time.time()
        }
        
        if category == "precision":
            self.results["precision_inconsistencies"].append(issue)
        elif category == "async":
            self.results["async_pattern_inconsistencies"].append(issue)
        else:
            self.results["consistency_issues"].append(issue)
            
        if severity == "CRITICAL":
            self.results["critical_inconsistencies"].append(issue)
            self.results["summary"]["critical"] += 1
            
        self.results["summary"]["total_tests"] += 1
        if severity in ["CRITICAL", "ERROR"]:
            self.results["summary"]["inconsistent"] += 1
        else:
            self.results["summary"]["consistent"] += 1

    async def test_exchange_initialization_consistency(self):
        """测试交易所初始化一致性"""
        print("🔍 测试交易所初始化一致性...")
        
        exchanges = {}
        init_methods = {}
        
        try:
            # 导入三个交易所
            from exchanges.bybit_exchange import BybitExchange
            from exchanges.gate_exchange import GateExchange  
            from exchanges.okx_exchange import OKXExchange
            
            # 检查构造函数签名一致性
            import inspect
            
            bybit_sig = inspect.signature(BybitExchange.__init__)
            gate_sig = inspect.signature(GateExchange.__init__)
            okx_sig = inspect.signature(OKXExchange.__init__)
            
            # 检查参数一致性
            bybit_params = list(bybit_sig.parameters.keys())[1:]  # 排除self
            gate_params = list(gate_sig.parameters.keys())[1:]
            okx_params = list(okx_sig.parameters.keys())[1:]
            
            if not (bybit_params == gate_params == okx_params):
                self.log_inconsistency(
                    "initialization", "WARNING",
                    "交易所构造函数参数不一致",
                    ["bybit", "gate", "okx"],
                    {
                        "bybit_params": bybit_params,
                        "gate_params": gate_params,
                        "okx_params": okx_params
                    }
                )
            else:
                print("    ✅ 交易所构造函数参数一致")
                
            # 检查初始化方法存在性
            for name, cls in [("bybit", BybitExchange), ("gate", GateExchange), ("okx", OKXExchange)]:
                if hasattr(cls, 'initialize'):
                    init_methods[name] = inspect.signature(cls.initialize)
                else:
                    self.log_inconsistency(
                        "initialization", "ERROR",
                        f"{name}交易所缺少initialize方法",
                        [name]
                    )
                    
        except Exception as e:
            self.log_inconsistency(
                "initialization", "CRITICAL",
                "交易所类导入失败",
                ["bybit", "gate", "okx"],
                {"error": str(e), "traceback": traceback.format_exc()}
            )

    async def test_precision_handling_consistency(self):
        """测试精度处理一致性"""
        print("🔍 测试精度处理一致性...")
        
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            preloader = TradingRulesPreloader()
            
            # 测试数据
            test_symbol = "BTC-USDT"
            test_amounts = [0.12345678, 1.23456789, 12.3456789]
            
            precision_results = {}
            
            for exchange in ["bybit", "gate", "okx"]:
                precision_results[exchange] = {}
                
                for market_type in ["spot", "futures"]:
                    try:
                        # 获取交易规则
                        rule = preloader.get_trading_rule(exchange, test_symbol, market_type)
                        if rule:
                            precision_results[exchange][market_type] = {
                                "qty_step": float(rule.qty_step),
                                "price_step": float(rule.price_step),
                                "min_qty": float(rule.min_qty),
                                "formatted_amounts": []
                            }
                            
                            # 测试格式化一致性
                            for amount in test_amounts:
                                try:
                                    formatted = preloader.format_amount_unified(
                                        amount, exchange, test_symbol, market_type
                                    )
                                    precision_results[exchange][market_type]["formatted_amounts"].append({
                                        "original": amount,
                                        "formatted": formatted
                                    })
                                except Exception as e:
                                    precision_results[exchange][market_type]["formatted_amounts"].append({
                                        "original": amount,
                                        "error": str(e)
                                    })
                    except Exception as e:
                        precision_results[exchange][market_type] = {"error": str(e)}
            
            # 分析精度一致性
            self._analyze_precision_consistency(precision_results)
            
        except Exception as e:
            self.log_inconsistency(
                "precision", "CRITICAL",
                "精度处理测试失败",
                ["bybit", "gate", "okx"],
                {"error": str(e)}
            )

    def _analyze_precision_consistency(self, precision_results: Dict):
        """分析精度一致性"""
        exchanges = ["bybit", "gate", "okx"]
        
        for market_type in ["spot", "futures"]:
            # 检查步长一致性
            qty_steps = []
            price_steps = []
            
            for exchange in exchanges:
                if market_type in precision_results[exchange]:
                    data = precision_results[exchange][market_type]
                    if "qty_step" in data:
                        qty_steps.append((exchange, data["qty_step"]))
                    if "price_step" in data:
                        price_steps.append((exchange, data["price_step"]))
            
            # 检查数量步长差异
            if len(qty_steps) > 1:
                max_step = max(qty_steps, key=lambda x: x[1])
                min_step = min(qty_steps, key=lambda x: x[1])
                
                if max_step[1] / min_step[1] > 10:  # 超过10倍差异
                    self.log_inconsistency(
                        "precision", "WARNING",
                        f"{market_type}市场数量步长差异过大",
                        [item[0] for item in qty_steps],
                        {
                            "max_step": max_step,
                            "min_step": min_step,
                            "ratio": max_step[1] / min_step[1],
                            "all_steps": qty_steps
                        }
                    )

    async def test_async_pattern_consistency(self):
        """测试异步模式一致性"""
        print("🔍 测试异步调用模式一致性...")
        
        try:
            import inspect
            from exchanges.bybit_exchange import BybitExchange
            from exchanges.gate_exchange import GateExchange
            from exchanges.okx_exchange import OKXExchange
            
            exchanges = {
                "bybit": BybitExchange,
                "gate": GateExchange,
                "okx": OKXExchange
            }
            
            # 检查关键方法的异步一致性
            key_methods = [
                "get_balance", "place_order", "cancel_order", 
                "get_order", "get_orderbook", "get_ticker"
            ]
            
            async_patterns = {}
            
            for exchange_name, exchange_class in exchanges.items():
                async_patterns[exchange_name] = {}
                
                for method_name in key_methods:
                    if hasattr(exchange_class, method_name):
                        method = getattr(exchange_class, method_name)
                        async_patterns[exchange_name][method_name] = {
                            "is_async": inspect.iscoroutinefunction(method),
                            "signature": str(inspect.signature(method))
                        }
                    else:
                        async_patterns[exchange_name][method_name] = {"missing": True}
            
            # 分析异步模式一致性
            for method_name in key_methods:
                is_async_flags = []
                signatures = []
                
                for exchange_name in exchanges.keys():
                    if method_name in async_patterns[exchange_name]:
                        method_info = async_patterns[exchange_name][method_name]
                        if "is_async" in method_info:
                            is_async_flags.append((exchange_name, method_info["is_async"]))
                            signatures.append((exchange_name, method_info["signature"]))
                
                # 检查异步一致性
                if len(set(flag[1] for flag in is_async_flags)) > 1:
                    self.log_inconsistency(
                        "async", "ERROR",
                        f"{method_name} 方法异步模式不一致",
                        [flag[0] for flag in is_async_flags],
                        {
                            "async_flags": is_async_flags,
                            "signatures": signatures
                        }
                    )
                
        except Exception as e:
            self.log_inconsistency(
                "async", "CRITICAL", 
                "异步模式测试失败",
                ["bybit", "gate", "okx"],
                {"error": str(e)}
            )

    async def test_data_format_consistency(self):
        """测试数据格式一致性"""
        print("🔍 测试数据格式标准化一致性...")
        
        try:
            from websocket.unified_data_formatter import get_orderbook_formatter
            formatter = get_orderbook_formatter()
            
            # 模拟各交易所原始数据格式
            mock_data = {
                "bybit": {
                    "asks": [["50234.12", "0.1"], ["50234.15", "0.2"]],
                    "bids": [["50233.98", "0.1"], ["50233.95", "0.2"]],
                    "timestamp": 1703123456789
                },
                "gate": {
                    "asks": [["50234.12", "0.1"], ["50234.15", "0.2"]],
                    "bids": [["50233.98", "0.1"], ["50233.95", "0.2"]],
                    "timestamp": 1703123456789
                },
                "okx": {
                    "asks": [["50234.12", "0.1"], ["50234.15", "0.2"]],
                    "bids": [["50233.98", "0.1"], ["50233.95", "0.2"]],
                    "timestamp": 1703123456789
                }
            }
            
            formatted_results = {}
            
            for exchange, data in mock_data.items():
                try:
                    result = formatter.format_orderbook_data(
                        asks=data["asks"],
                        bids=data["bids"],
                        symbol="BTC-USDT",
                        exchange=exchange,
                        market_type="spot",
                        timestamp=data["timestamp"]
                    )
                    formatted_results[exchange] = result
                except Exception as e:
                    formatted_results[exchange] = {"error": str(e)}
            
            # 检查格式化结果一致性
            if len(formatted_results) > 1:
                first_exchange = list(formatted_results.keys())[0]
                reference_keys = set(formatted_results[first_exchange].keys())
                
                for exchange, result in formatted_results.items():
                    if "error" not in result:
                        current_keys = set(result.keys())
                        if current_keys != reference_keys:
                            self.log_inconsistency(
                                "data_format", "WARNING",
                                f"{exchange} 数据格式字段不一致",
                                [first_exchange, exchange],
                                {
                                    "reference_keys": list(reference_keys),
                                    "current_keys": list(current_keys),
                                    "missing": list(reference_keys - current_keys),
                                    "extra": list(current_keys - reference_keys)
                                }
                            )
                            
        except Exception as e:
            self.log_inconsistency(
                "data_format", "CRITICAL",
                "数据格式一致性测试失败",
                ["bybit", "gate", "okx"],
                {"error": str(e)}
            )

    async def test_error_handling_consistency(self):
        """测试错误处理一致性"""  
        print("🔍 测试错误处理机制一致性...")
        
        try:
            # 检查异常定义一致性
            exception_patterns = {}
            
            for exchange in ["bybit", "gate", "okx"]:
                try:
                    module_name = f"exchanges.{exchange}_exchange"
                    module = __import__(module_name, fromlist=[''])
                    
                    # 查找异常类
                    exceptions = []
                    for name in dir(module):
                        obj = getattr(module, name)
                        if isinstance(obj, type) and issubclass(obj, Exception):
                            exceptions.append(name)
                    
                    exception_patterns[exchange] = exceptions
                    
                except Exception as e:
                    exception_patterns[exchange] = {"error": str(e)}
            
            # 分析异常处理一致性
            all_exceptions = set()
            for exchange, exceptions in exception_patterns.items():
                if isinstance(exceptions, list):
                    all_exceptions.update(exceptions)
            
            for exception_name in all_exceptions:
                exchanges_with_exception = []
                for exchange, exceptions in exception_patterns.items():
                    if isinstance(exceptions, list) and exception_name in exceptions:
                        exchanges_with_exception.append(exchange)
                
                if len(exchanges_with_exception) != len(exception_patterns):
                    missing_exchanges = set(exception_patterns.keys()) - set(exchanges_with_exception)
                    self.log_inconsistency(
                        "error_handling", "WARNING",
                        f"异常类 {exception_name} 在部分交易所中缺失",
                        list(missing_exchanges),
                        {
                            "exception_name": exception_name,
                            "present_in": exchanges_with_exception,
                            "missing_in": list(missing_exchanges)
                        }
                    )
                    
        except Exception as e:
            self.log_inconsistency(
                "error_handling", "CRITICAL",
                "错误处理一致性测试失败", 
                ["bybit", "gate", "okx"],
                {"error": str(e)}
            )

    def generate_consistency_report(self) -> str:
        """生成一致性报告"""
        report = {
            "consistency_analysis": {
                "timestamp": self.results["timestamp"],
                "total_inconsistencies": (
                    len(self.results["consistency_issues"]) +
                    len(self.results["precision_inconsistencies"]) +
                    len(self.results["async_pattern_inconsistencies"])
                ),
                "critical_inconsistencies": len(self.results["critical_inconsistencies"]),
                "by_category": {
                    "precision": len(self.results["precision_inconsistencies"]),
                    "async_patterns": len(self.results["async_pattern_inconsistencies"]),
                    "general": len(self.results["consistency_issues"])
                },
                "test_summary": self.results["summary"]
            },
            "detailed_results": self.results
        }
        
        return json.dumps(report, indent=2, ensure_ascii=False)

    async def run_full_consistency_check(self):
        """运行完整一致性检查"""
        print("🚀 开始三交易所一致性诊断...")
        print("=" * 80)
        
        try:
            await self.test_exchange_initialization_consistency()
            await self.test_precision_handling_consistency()
            await self.test_async_pattern_consistency()
            await self.test_data_format_consistency()
            await self.test_error_handling_consistency()
            
            print("=" * 80)
            print("✅ 一致性检查完成")
            
            # 生成报告
            report = self.generate_consistency_report()
            
            # 保存报告
            report_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "diagnostic_results", 
                f"exchange_consistency_report_{int(time.time())}.json"
            )
            
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"📊 一致性报告已保存: {report_path}")
            
            # 打印摘要
            summary = self.results["summary"]
            print(f"\n📈 一致性检查摘要:")
            print(f"  总测试数: {summary['total_tests']}")
            print(f"  一致: {summary['consistent']}")
            print(f"  不一致: {summary['inconsistent']}")
            print(f"  严重不一致: {summary['critical']}")
            print(f"  精度不一致: {len(self.results['precision_inconsistencies'])}")
            print(f"  异步模式不一致: {len(self.results['async_pattern_inconsistencies'])}")
            
            return report_path
            
        except Exception as e:
            print(f"❌ 一致性检查过程异常: {e}")
            traceback.print_exc()
            return None

async def main():
    """主函数"""
    diagnostic = ExchangeConsistencyDiagnostic()
    report_path = await diagnostic.run_full_consistency_check()
    
    if report_path:
        print(f"\n🎯 下一步: 基于一致性分析进行统一修复")
        print(f"📄 详细报告: {report_path}")
    else:
        print("\n❌ 一致性检查失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())