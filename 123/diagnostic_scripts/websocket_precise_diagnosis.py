#!/usr/bin/env python3
"""
WebSocket精确诊断脚本
专门诊断reconnect_tasks未定义问题和扫描阈值不一致问题
"""

import os
import sys
import re
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def diagnose_reconnect_tasks_issue():
    """诊断reconnect_tasks变量定义问题"""
    print("🔍 诊断1: WebSocket管理器reconnect_tasks变量定义问题")
    print("=" * 60)
    
    ws_manager_path = project_root / "websocket" / "ws_manager.py"
    
    if not ws_manager_path.exists():
        print(f"❌ 文件不存在: {ws_manager_path}")
        return False
    
    issues = []
    
    with open(ws_manager_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 检查_monitor_status方法中的reconnect_tasks使用
    in_monitor_method = False
    reconnect_tasks_defined_in_method = False
    reconnect_tasks_defined_in_loop = False
    reconnect_tasks_used = []
    method_start_line = 0
    
    for i, line in enumerate(lines, 1):
        if "def _monitor_status(self):" in line:
            in_monitor_method = True
            method_start_line = i
            print(f"✅ 找到_monitor_status方法: 第{i}行")
            continue
            
        if in_monitor_method:
            if line.strip().startswith("def ") and "_monitor_status" not in line:
                # 进入下一个方法，结束检查
                break
                
            # 检查方法开始时的定义
            if i - method_start_line < 10 and "reconnect_tasks = []" in line:
                reconnect_tasks_defined_in_method = True
                print(f"✅ 找到reconnect_tasks方法级定义: 第{i}行")
                
            # 检查循环内的定义
            if "while self.running:" in line:
                # 进入监控循环
                pass
            elif in_monitor_method and "reconnect_tasks = []" in line:
                reconnect_tasks_defined_in_loop = True
                print(f"✅ 找到reconnect_tasks循环内定义: 第{i}行")
                
            if "if reconnect_tasks:" in line:
                reconnect_tasks_used.append(i)
                print(f"📍 reconnect_tasks条件检查: 第{i}行 - {line.strip()}")
                
            if "reconnect_tasks.extend" in line or "reconnect_tasks.append" in line:
                reconnect_tasks_used.append(i)
                print(f"📍 reconnect_tasks操作: 第{i}行 - {line.strip()}")
    
    # 分析问题
    if not (reconnect_tasks_defined_in_method or reconnect_tasks_defined_in_loop) and reconnect_tasks_used:
        issues.append(f"❌ CRITICAL: reconnect_tasks变量未定义但被使用")
        print(f"❌ CRITICAL: reconnect_tasks变量未定义但被使用在第{reconnect_tasks_used}行")
        
        # 查找具体的错误行
        for line_num in reconnect_tasks_used:
            if line_num <= len(lines):
                print(f"   第{line_num}行: {lines[line_num-1].strip()}")
                
        # 检查是否是第921行的问题
        if 921 in reconnect_tasks_used:
            print(f"🎯 确认第921行问题: 这正是日志中报告的NameError位置")
    
    elif reconnect_tasks_defined_in_method or reconnect_tasks_defined_in_loop:
        print("✅ reconnect_tasks变量已正确定义")
    
    return len(issues) == 0

def diagnose_scan_interval_inconsistency():
    """诊断扫描间隔不一致问题"""
    print("\n🔍 诊断2: 扫描间隔配置不一致问题")
    print("=" * 60)
    
    issues = []
    
    # 检查.env配置
    env_path = project_root / ".env"
    env_scan_interval = None
    
    if env_path.exists():
        with open(env_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if "SCAN_INTERVAL=" in line and not line.strip().startswith("#"):
                    # 提取等号后的值，去除注释
                    value_part = line.strip().split("=")[1]
                    # 去除注释部分
                    env_scan_interval = value_part.split("#")[0].strip()
                    print(f"✅ .env中SCAN_INTERVAL配置: {env_scan_interval}秒 (第{line_num}行)")
                    break
    
    # 检查OpportunityScanner中的实际使用
    scanner_path = project_root / "core" / "opportunity_scanner.py"
    code_scan_intervals = []
    
    if scanner_path.exists():
        with open(scanner_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines, 1):
            # 查找_market_data_loop中的asyncio.sleep调用
            if "await asyncio.sleep(" in line:
                # 检查是否在_market_data_loop方法中
                method_context = "".join(lines[max(0, i-20):i])
                if "_market_data_loop" in method_context:
                    sleep_match = re.search(r'await asyncio\.sleep\(([\d.]+)\)', line)
                    if sleep_match:
                        sleep_time = sleep_match.group(1)
                        code_scan_intervals.append((i, sleep_time))
                        print(f"📍 _market_data_loop中扫描间隔: {sleep_time}秒 (第{i}行)")
                        
                        # 检查注释说明
                        comment_match = re.search(r'#.*?(\d+\.?\d*).*?ms', line)
                        if comment_match:
                            comment_ms = comment_match.group(1)
                            print(f"   注释说明: {comment_ms}ms")
    
    # 分析不一致性
    if env_scan_interval:
        env_interval_float = float(env_scan_interval)
        for line_num, code_interval in code_scan_intervals:
            code_interval_float = float(code_interval)
            if abs(env_interval_float - code_interval_float) > 0.001:  # 允许1ms误差
                issues.append(f"❌ 扫描间隔不一致: .env={env_scan_interval}s vs 代码第{line_num}行={code_interval}s")
                print(f"❌ 扫描间隔不一致: .env={env_scan_interval}s vs 代码第{line_num}行={code_interval}s")
    
    # 检查用户要求的0.3秒
    target_interval = 0.3
    print(f"\n🎯 用户要求扫描间隔: {target_interval}秒")
    
    for line_num, code_interval in code_scan_intervals:
        code_interval_float = float(code_interval)
        if abs(target_interval - code_interval_float) > 0.001:
            issues.append(f"❌ 与用户要求不符: 代码第{line_num}行={code_interval}s, 应为{target_interval}s")
            print(f"❌ 与用户要求不符: 代码第{line_num}行={code_interval}s, 应为{target_interval}s")
    
    return len(issues) == 0

def diagnose_okx_api_rate_limit():
    """诊断OKX API限速问题"""
    print("\n🔍 诊断3: OKX API限速问题")
    print("=" * 60)
    
    # 检查错误日志中的OKX限速错误
    error_log_path = project_root / "logs" / "error_20250731.log"
    
    if not error_log_path.exists():
        print(f"⚠️ 错误日志文件不存在: {error_log_path}")
        return True
    
    okx_rate_limit_errors = 0
    
    with open(error_log_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if "50011" in line and "Too Many Requests" in line:
                okx_rate_limit_errors += 1
    
    print(f"📊 OKX API限速错误统计: {okx_rate_limit_errors}次")
    
    if okx_rate_limit_errors > 10:
        print(f"❌ CRITICAL: OKX API限速错误过多 ({okx_rate_limit_errors}次)")
        return False
    elif okx_rate_limit_errors > 0:
        print(f"⚠️ WARNING: 发现OKX API限速错误 ({okx_rate_limit_errors}次)")
        return False
    else:
        print("✅ 未发现OKX API限速错误")
        return True

def main():
    """主诊断函数"""
    print("🚀 WebSocket精确诊断脚本启动")
    print("=" * 60)
    print(f"📁 项目根目录: {project_root}")
    print(f"⏰ 诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行所有诊断
    results = []
    
    results.append(("reconnect_tasks变量定义", diagnose_reconnect_tasks_issue()))
    results.append(("扫描间隔一致性", diagnose_scan_interval_inconsistency()))
    results.append(("OKX API限速", diagnose_okx_api_rate_limit()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 诊断结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有诊断通过！WebSocket系统健康")
    else:
        print("🚨 发现问题需要修复！")
        print("\n🔧 建议修复步骤:")
        print("1. 修复ws_manager.py中reconnect_tasks变量定义")
        print("2. 统一扫描间隔配置为0.3秒")
        print("3. 优化OKX API调用频率")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
