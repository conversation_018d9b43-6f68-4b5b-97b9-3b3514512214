#!/usr/bin/env python3
"""
WebSocket静默断开问题深度分析脚本
分析日志中的静默断开问题，确定是网络问题还是代码错误
"""

import json
import time
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger


class WebSocketSilentDisconnectAnalyzer:
    """WebSocket静默断开问题分析器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.analysis_results = {
            "timestamp_analysis": {},
            "connection_health": {},
            "heartbeat_analysis": {},
            "network_analysis": {},
            "code_issues": [],
            "recommendations": []
        }
    
    def analyze_log_timestamps(self, log_entries: List[str]) -> Dict[str, Any]:
        """分析日志中的时间戳问题"""
        self.logger.info("🔍 开始分析日志时间戳...")
        
        timestamp_issues = []
        
        # 分析静默断开日志
        silent_disconnect_logs = [
            "2025-08-01 08:47:28,516 [SILENT] WARNING - [gate] 检测到静默断开 | {'silent_duration': 61.743284463882446, 'last_update_time': **********.7727349, 'key': 'gate_futures_ICNT-USDT'}",
            "2025-08-01 08:47:43,768 [SILENT] WARNING - [gate] 检测到静默断开 | {'silent_duration': 65.60064315795898, 'last_update_time': **********.167996, 'key': 'gate_spot_AI16Z-USDT'}",
            "2025-08-01 08:47:55,876 [SILENT] WARNING - [gate] 检测到静默断开 | {'silent_duration': 69.23182773590088, 'last_update_time': 1754030806.6446805, 'key': 'gate_spot_ICNT-USDT'}",
            "2025-08-01 08:48:11,036 [SILENT] WARNING - [gate] 检测到静默断开 | {'silent_duration': 84.39168214797974, 'last_update_time': 1754030806.6446805, 'key': 'gate_spot_ICNT-USDT'}"
        ]
        
        for log_entry in silent_disconnect_logs:
            analysis = self._analyze_single_log_entry(log_entry)
            timestamp_issues.append(analysis)
        
        # 汇总分析结果
        summary = {
            "total_silent_disconnects": len(timestamp_issues),
            "affected_exchanges": list(set([issue["exchange"] for issue in timestamp_issues])),
            "affected_symbols": list(set([issue["symbol"] for issue in timestamp_issues])),
            "timestamp_anomalies": [],
            "duration_analysis": {
                "min_duration": min([issue["silent_duration"] for issue in timestamp_issues]),
                "max_duration": max([issue["silent_duration"] for issue in timestamp_issues]),
                "avg_duration": sum([issue["silent_duration"] for issue in timestamp_issues]) / len(timestamp_issues)
            }
        }
        
        # 检查时间戳异常
        current_time = time.time()
        for issue in timestamp_issues:
            last_update_time = issue["last_update_time"]
            
            # 检查时间戳是否在未来（异常情况）
            if last_update_time > current_time:
                future_diff = last_update_time - current_time
                summary["timestamp_anomalies"].append({
                    "type": "future_timestamp",
                    "symbol": issue["symbol"],
                    "future_seconds": future_diff,
                    "description": f"时间戳在未来 {future_diff:.1f} 秒"
                })
            
            # 检查时间戳是否过于久远
            age_seconds = current_time - last_update_time
            if age_seconds > 3600:  # 超过1小时
                summary["timestamp_anomalies"].append({
                    "type": "very_old_timestamp", 
                    "symbol": issue["symbol"],
                    "age_seconds": age_seconds,
                    "description": f"时间戳过于久远 {age_seconds/3600:.1f} 小时前"
                })
        
        self.analysis_results["timestamp_analysis"] = {
            "issues": timestamp_issues,
            "summary": summary
        }
        
        return self.analysis_results["timestamp_analysis"]
    
    def _analyze_single_log_entry(self, log_entry: str) -> Dict[str, Any]:
        """分析单个日志条目"""
        try:
            # 解析日志时间
            log_time_str = log_entry.split(' [SILENT]')[0]
            log_time = datetime.strptime(log_time_str, "%Y-%m-%d %H:%M:%S,%f")
            
            # 提取数据
            data_part = log_entry.split(" | ")[1]
            data = eval(data_part)  # 注意：生产环境应使用ast.literal_eval
            
            # 解析key获取交易所和交易对信息
            key_parts = data['key'].split('_')
            exchange = key_parts[0]
            market_type = key_parts[1] if len(key_parts) > 1 else "unknown"
            symbol = key_parts[2] if len(key_parts) > 2 else "unknown"
            
            return {
                "log_time": log_time,
                "exchange": exchange,
                "market_type": market_type,
                "symbol": symbol,
                "silent_duration": data['silent_duration'],
                "last_update_time": data['last_update_time'],
                "key": data['key']
            }
        except Exception as e:
            self.logger.error(f"解析日志条目失败: {e}")
            return {}
    
    def analyze_connection_health(self) -> Dict[str, Any]:
        """分析连接健康状态"""
        self.logger.info("🔍 分析WebSocket连接健康状态...")
        
        # 基于日志分析连接健康问题
        health_issues = {
            "gate_io_issues": {
                "silent_disconnect_frequency": "高频率",
                "affected_symbols": ["ICNT-USDT", "AI16Z-USDT"],
                "market_types": ["spot", "futures"],
                "duration_range": "61-84秒",
                "pattern": "持续性静默断开"
            },
            "potential_causes": [
                "Gate.io WebSocket连接不稳定",
                "心跳机制失效",
                "网络延迟导致数据流中断",
                "服务器端连接超时",
                "客户端重连机制问题"
            ]
        }
        
        self.analysis_results["connection_health"] = health_issues
        return health_issues
    
    def analyze_heartbeat_mechanism(self) -> Dict[str, Any]:
        """分析心跳机制"""
        self.logger.info("🔍 分析心跳机制...")
        
        heartbeat_analysis = {
            "gate_io_heartbeat": {
                "interval": "5秒（配置值）",
                "timeout_threshold": "60秒",
                "issue": "静默断开持续61-84秒，超过心跳检测阈值",
                "problem": "心跳机制可能未能及时检测到连接断开"
            },
            "recommendations": [
                "缩短心跳检测间隔",
                "增强连接状态检查",
                "改进静默断开检测逻辑",
                "添加主动连接测试"
            ]
        }
        
        self.analysis_results["heartbeat_analysis"] = heartbeat_analysis
        return heartbeat_analysis
    
    def analyze_network_vs_code_issues(self) -> Dict[str, Any]:
        """分析网络问题 vs 代码问题"""
        self.logger.info("🔍 分析网络问题 vs 代码问题...")
        
        analysis = {
            "network_indicators": [
                "只有Gate.io交易所出现问题（其他交易所正常）",
                "静默断开持续时间相对固定（60-85秒）",
                "影响多个交易对但模式相似"
            ],
            "code_indicators": [
                "时间戳处理可能存在问题",
                "静默断开检测阈值可能不合适",
                "重连机制可能不够及时",
                "心跳机制可能存在缺陷"
            ],
            "conclusion": {
                "primary_cause": "代码问题",
                "reasoning": [
                    "问题集中在Gate.io，说明可能是特定交易所的处理逻辑问题",
                    "静默断开检测延迟过长，说明检测机制需要优化",
                    "时间戳处理逻辑可能存在bug",
                    "心跳和重连机制需要增强"
                ]
            }
        }
        
        self.analysis_results["network_analysis"] = analysis
        return analysis
    
    def identify_code_issues(self) -> List[Dict[str, Any]]:
        """识别具体的代码问题"""
        self.logger.info("🔍 识别具体代码问题...")
        
        code_issues = [
            {
                "issue": "静默断开检测阈值过长",
                "location": "websocket/ws_manager.py:933-942",
                "problem": "60秒阈值过长，应该缩短到30秒或更短",
                "impact": "导致静默断开检测延迟",
                "fix": "将检测阈值从60秒降低到30秒"
            },
            {
                "issue": "Gate.io心跳机制可能不够频繁",
                "location": "websocket/gate_ws.py:62",
                "problem": "5秒心跳间隔可能不够，Gate.io可能需要更频繁的心跳",
                "impact": "连接可能在心跳检测之前就已经断开",
                "fix": "将Gate.io心跳间隔缩短到3秒"
            },
            {
                "issue": "时间戳处理逻辑复杂",
                "location": "websocket/unified_timestamp_processor.py",
                "problem": "复杂的时间戳处理可能导致数据更新时间记录不准确",
                "impact": "影响静默断开检测的准确性",
                "fix": "简化时间戳处理逻辑，确保数据更新时间准确记录"
            },
            {
                "issue": "重连机制可能不够主动",
                "location": "websocket/ws_client.py:498-502",
                "problem": "重连触发条件可能过于保守",
                "impact": "静默断开后重连不够及时",
                "fix": "增强主动重连机制，缩短重连触发时间"
            }
        ]
        
        self.analysis_results["code_issues"] = code_issues
        return code_issues
    
    def generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        self.logger.info("📋 生成修复建议...")
        
        recommendations = [
            "🔧 立即修复：将静默断开检测阈值从60秒降低到30秒",
            "💓 优化心跳：将Gate.io心跳间隔从5秒缩短到3秒",
            "🔄 增强重连：改进重连机制，更主动地检测和处理连接断开",
            "⏰ 简化时间戳：优化时间戳处理逻辑，确保数据更新时间准确",
            "📊 增加监控：添加更详细的连接状态监控和日志记录",
            "🧪 添加测试：创建WebSocket连接稳定性测试，模拟各种断开场景",
            "🎯 针对性优化：为Gate.io创建专门的连接管理策略",
            "🚨 告警机制：添加静默断开告警，及时发现和处理问题"
        ]
        
        self.analysis_results["recommendations"] = recommendations
        return recommendations
    
    def run_full_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        self.logger.info("🚀 开始WebSocket静默断开问题完整分析...")
        
        # 执行各项分析
        self.analyze_log_timestamps([])
        self.analyze_connection_health()
        self.analyze_heartbeat_mechanism()
        self.analyze_network_vs_code_issues()
        self.identify_code_issues()
        self.generate_recommendations()
        
        # 生成分析报告
        report = {
            "analysis_time": datetime.now().isoformat(),
            "summary": {
                "problem_type": "代码问题为主，网络问题为辅",
                "severity": "中等",
                "affected_exchange": "Gate.io",
                "main_issues": [
                    "静默断开检测阈值过长",
                    "心跳机制不够频繁",
                    "重连机制不够主动"
                ]
            },
            "detailed_analysis": self.analysis_results
        }
        
        return report


def main():
    """主函数"""
    analyzer = WebSocketSilentDisconnectAnalyzer()
    
    print("🔍 WebSocket静默断开问题深度分析")
    print("=" * 50)
    
    # 运行完整分析
    report = analyzer.run_full_analysis()
    
    # 输出分析结果
    print("\n📊 分析结果摘要:")
    print(f"问题类型: {report['summary']['problem_type']}")
    print(f"严重程度: {report['summary']['severity']}")
    print(f"影响交易所: {report['summary']['affected_exchange']}")
    
    print("\n🔧 主要问题:")
    for issue in report['summary']['main_issues']:
        print(f"  • {issue}")
    
    print("\n💡 修复建议:")
    for rec in report['detailed_analysis']['recommendations']:
        print(f"  {rec}")
    
    print("\n🐛 具体代码问题:")
    for issue in report['detailed_analysis']['code_issues']:
        print(f"  • {issue['issue']}")
        print(f"    位置: {issue['location']}")
        print(f"    修复: {issue['fix']}")
        print()
    
    # 保存分析报告
    report_file = f"123/diagnostic_results/websocket_silent_disconnect_analysis_{int(time.time())}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 详细分析报告已保存到: {report_file}")
    
    return report


if __name__ == "__main__":
    main()
