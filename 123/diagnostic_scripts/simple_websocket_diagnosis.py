#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket卡住问题简化诊断脚本
"""

import sys
import os
import asyncio
import time
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_simple_diagnosis():
    """运行简化诊断"""
    print("="*60)
    print("WebSocket卡住问题诊断报告")
    print("="*60)
    
    results = {
        "issues_found": [],
        "recommendations": []
    }
    
    # 1. 分析API限速问题
    print("\n1. 分析API限速问题...")
    error_log_path = "logs/error_20250731.log"
    if os.path.exists(error_log_path):
        with open(error_log_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        okx_rate_limit_count = content.count("Too Many Requests")
        okx_50011_count = content.count("50011")
        
        print(f"   OKX限速错误次数: {okx_rate_limit_count}")
        print(f"   错误代码50011次数: {okx_50011_count}")
        
        if okx_rate_limit_count > 5:
            results["issues_found"].append({
                "type": "API限速",
                "severity": "HIGH",
                "count": okx_rate_limit_count,
                "description": "OKX API频繁触发限速机制"
            })
            results["recommendations"].append("降低OKX API调用频率")
    
    # 2. 分析数据流停止问题
    print("\n2. 分析数据流停止问题...")
    prices_log = "logs/websocket_prices.log"
    if os.path.exists(prices_log):
        with open(prices_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        total_lines = len(lines)
        print(f"   总日志条数: {total_lines}")
        
        # 检查最后几行
        if lines:
            last_lines = lines[-5:]
            last_timestamps = []
            for line in last_lines:
                if "18:24:" in line:
                    parts = line.split(' ')
                    if len(parts) > 0:
                        last_timestamps.append(parts[0])
            
            if last_timestamps:
                print(f"   最后时间戳: {last_timestamps[-1]}")
                
                # 检查是否在18:24:41后停止
                if any("18:24:41" in ts for ts in last_timestamps):
                    results["issues_found"].append({
                        "type": "数据流停止",
                        "severity": "CRITICAL", 
                        "stoppage_time": "18:24:41",
                        "description": "数据流在18:24:41后完全停止"
                    })
                    results["recommendations"].append("重启WebSocket管理器")
    
    # 3. 分析连接日志
    print("\n3. 分析连接日志...")
    conn_log = "logs/websocket_connection_20250731.log"
    if os.path.exists(conn_log):
        with open(conn_log, 'r', encoding='utf-8') as f:
            conn_lines = f.readlines()
            
        print(f"   连接事件数: {len(conn_lines)}")
        
        # 检查是否有在关键时间的连接事件
        critical_events = [line for line in conn_lines if "18:24:" in line]
        print(f"   关键时间连接事件: {len(critical_events)}")
        
        if len(critical_events) == 0 and len(conn_lines) > 0:
            results["issues_found"].append({
                "type": "连接事件缺失",
                "severity": "HIGH",
                "description": "在关键时间段缺少WebSocket连接事件"
            })
            results["recommendations"].append("检查WebSocket重连机制")
    
    # 4. 生成诊断摘要
    print("\n" + "="*60)
    print("诊断摘要")
    print("="*60)
    
    critical_issues = [issue for issue in results["issues_found"] if issue["severity"] == "CRITICAL"]
    high_issues = [issue for issue in results["issues_found"] if issue["severity"] == "HIGH"]
    
    print(f"关键问题数: {len(critical_issues)}")
    print(f"高级问题数: {len(high_issues)}")
    
    if critical_issues:
        print("\n[CRITICAL] 关键问题:")
        for issue in critical_issues:
            print(f"  - {issue['type']}: {issue['description']}")
    
    if high_issues:
        print("\n[HIGH] 高级问题:")
        for issue in high_issues:
            print(f"  - {issue['type']}: {issue['description']}")
    
    print("\n建议解决方案:")
    for i, rec in enumerate(results["recommendations"], 1):
        print(f"  {i}. {rec}")
    
    # 根本原因分析
    print("\n根本原因分析:")
    if critical_issues:
        if any(issue["type"] == "数据流停止" for issue in critical_issues):
            print("  主要原因: WebSocket数据流管理器在18:24:41后进入阻塞状态")
            print("  可能触发因素: OKX API限速导致异步任务阻塞")
            print("  解决方案: 重启WebSocket管理器，降低API调用频率")
    
    # 紧急行动建议
    print("\n紧急行动建议:")
    print("  1. 立即重启WebSocket数据流管理器")
    print("  2. 临时降低OKX API调用频率50%") 
    print("  3. 启用WebSocket连接健康监控")
    print("  4. 检查异步锁的正确释放")
    
    print("\n" + "="*60)
    
    # 保存结果
    result_file = f"diagnostic_results/simple_diagnosis_{int(time.time())}.json"
    os.makedirs("diagnostic_results", exist_ok=True)
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"诊断结果已保存到: {result_file}")
    
    return results

if __name__ == "__main__":
    try:
        run_simple_diagnosis()
    except Exception as e:
        print(f"诊断失败: {e}")
        import traceback
        traceback.print_exc()