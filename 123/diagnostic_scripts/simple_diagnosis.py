#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步调用缺陷和精度错误简化诊断脚本
"""

import asyncio
import sys
import os
import json
import traceback
import time
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Any, Tuple, Optional

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def main():
    """简化版诊断主函数"""
    print("开始异步调用缺陷和精度错误诊断...")
    print("=" * 60)
    
    results = {
        "async_issues": [],
        "precision_issues": [],
        "timestamp": time.time()
    }
    
    # 诊断1: 检查ExecutionEngine
    print("1. 检查ExecutionEngine导入...")
    try:
        from core.execution_engine import ExecutionEngine
        engine = ExecutionEngine()
        print("   ExecutionEngine导入成功")
        
        # 检查关键方法
        if hasattr(engine, '_execute_parallel_trading'):
            print("   _execute_parallel_trading 方法存在")
        else:
            results["async_issues"].append("_execute_parallel_trading 方法不存在")
            
        if hasattr(engine, 'execution_lock'):
            print("   execution_lock 属性存在")
        else:
            results["async_issues"].append("execution_lock 属性不存在")
            
    except Exception as e:
        print(f"   ExecutionEngine导入失败: {e}")
        results["async_issues"].append(f"ExecutionEngine导入失败: {e}")
    
    # 诊断2: 检查精度计算器
    print("2. 检查UnifiedOrderSpreadCalculator...")
    try:
        from core.unified_order_spread_calculator import UnifiedOrderSpreadCalculator
        calculator = UnifiedOrderSpreadCalculator()
        print("   UnifiedOrderSpreadCalculator导入成功")
        
        # 简单精度测试
        test_price1 = 50234.12345678
        test_price2 = 50234.87654321
        
        # 使用 float 计算
        float_spread = (test_price2 - test_price1) / test_price1
        
        # 使用 Decimal 计算
        decimal_price1 = Decimal(str(test_price1))
        decimal_price2 = Decimal(str(test_price2))
        decimal_spread = float((decimal_price2 - decimal_price1) / decimal_price1)
        
        precision_diff = abs(float_spread - decimal_spread)
        print(f"   精度差异: {precision_diff}")
        
        if precision_diff > 1e-12:
            results["precision_issues"].append(f"float/Decimal精度差异: {precision_diff}")
        
    except Exception as e:
        print(f"   UnifiedOrderSpreadCalculator导入失败: {e}")
        results["precision_issues"].append(f"UnifiedOrderSpreadCalculator导入失败: {e}")
    
    # 诊断3: 检查交易所一致性
    print("3. 检查交易所模块...")
    exchanges = ["bybit", "gate", "okx"]
    
    for exchange in exchanges:
        try:
            module_name = f"exchanges.{exchange}_exchange"
            module = __import__(module_name, fromlist=[''])
            class_name = f"{exchange.capitalize()}Exchange"
            exchange_class = getattr(module, class_name)
            print(f"   {exchange} 交易所模块导入成功: {class_name}")
            
        except Exception as e:
            print(f"   {exchange} 交易所模块导入失败: {e}")
            results["async_issues"].append(f"{exchange} 交易所导入失败: {e}")
    
    # 保存结果
    print("=" * 60)
    
    total_issues = len(results["async_issues"]) + len(results["precision_issues"])
    print(f"诊断完成 - 发现 {total_issues} 个问题")
    print(f"异步问题: {len(results['async_issues'])}")
    print(f"精度问题: {len(results['precision_issues'])}")
    
    if results["async_issues"]:
        print("\n异步问题:")
        for issue in results["async_issues"]:
            print(f"  - {issue}")
    
    if results["precision_issues"]:
        print("\n精度问题:")
        for issue in results["precision_issues"]:
            print(f"  - {issue}")
    
    # 保存报告
    report_path = os.path.join(
        os.path.dirname(__file__), 
        "..", "diagnostic_results", 
        f"simple_diagnosis_{int(time.time())}.json"
    )
    
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n报告已保存: {report_path}")
    return report_path

if __name__ == "__main__":
    main()