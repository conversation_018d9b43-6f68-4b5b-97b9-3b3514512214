#!/usr/bin/env python3
"""
WebSocket连接健康状态测试脚本
模拟各种失败场景，测试静默断开检测机制
"""

import asyncio
import json
import time
import websockets
from datetime import datetime
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger


class WebSocketConnectionHealthTester:
    """WebSocket连接健康状态测试器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.test_results = {
            "connection_tests": [],
            "heartbeat_tests": [],
            "silent_disconnect_tests": [],
            "recovery_tests": []
        }
        
        # Gate.io WebSocket URLs
        self.gate_urls = {
            "spot": "wss://api.gateio.ws/ws/v4/",
            "futures": "wss://fx-ws.gateio.ws/v4/ws/usdt"
        }
    
    async def test_basic_connection(self, url: str, market_type: str) -> Dict[str, Any]:
        """测试基本连接功能"""
        self.logger.info(f"🔗 测试 {market_type} 基本连接: {url}")
        
        test_result = {
            "test_type": "basic_connection",
            "market_type": market_type,
            "url": url,
            "start_time": time.time(),
            "success": False,
            "connection_time": 0,
            "error": None
        }
        
        try:
            start_time = time.time()
            
            # 尝试连接
            async with websockets.connect(
                url,
                ping_interval=None,
                ping_timeout=None,
                close_timeout=5,
                max_size=10 * 1024 * 1024
            ) as websocket:
                connection_time = time.time() - start_time
                test_result["connection_time"] = connection_time
                test_result["success"] = True
                
                self.logger.info(f"✅ {market_type} 连接成功，耗时: {connection_time:.3f}秒")
                
                # 测试基本订阅
                if market_type == "spot":
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "spot.order_book",
                        "event": "subscribe",
                        "payload": ["BTC_USDT", "10", "100ms"]
                    }
                else:
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "futures.order_book",
                        "event": "subscribe",
                        "payload": ["BTC_USDT"]
                    }
                
                await websocket.send(json.dumps(subscribe_msg))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    test_result["subscription_response"] = response_data
                    self.logger.info(f"📊 {market_type} 订阅响应: {response_data.get('event', 'unknown')}")
                except asyncio.TimeoutError:
                    test_result["error"] = "订阅响应超时"
                    self.logger.warning(f"⚠️ {market_type} 订阅响应超时")
                
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"❌ {market_type} 连接失败: {e}")
        
        test_result["end_time"] = time.time()
        test_result["duration"] = test_result["end_time"] - test_result["start_time"]
        
        return test_result
    
    async def test_heartbeat_mechanism(self, url: str, market_type: str) -> Dict[str, Any]:
        """测试心跳机制"""
        self.logger.info(f"💓 测试 {market_type} 心跳机制")
        
        test_result = {
            "test_type": "heartbeat_mechanism",
            "market_type": market_type,
            "url": url,
            "start_time": time.time(),
            "success": False,
            "heartbeat_responses": [],
            "error": None
        }
        
        try:
            async with websockets.connect(url) as websocket:
                # 发送多个心跳测试
                for i in range(3):
                    ping_msg = {
                        "time": int(time.time()),
                        "channel": f"{market_type}.ping"
                    }
                    
                    ping_start = time.time()
                    await websocket.send(json.dumps(ping_msg))
                    
                    try:
                        # 等待pong响应
                        response = await asyncio.wait_for(websocket.recv(), timeout=5)
                        ping_end = time.time()
                        
                        response_data = json.loads(response)
                        heartbeat_result = {
                            "ping_time": ping_start,
                            "pong_time": ping_end,
                            "rtt": ping_end - ping_start,
                            "response": response_data
                        }
                        
                        test_result["heartbeat_responses"].append(heartbeat_result)
                        self.logger.info(f"💓 心跳 {i+1}: RTT {heartbeat_result['rtt']:.3f}秒")
                        
                        await asyncio.sleep(2)  # 等待2秒再发送下一个心跳
                        
                    except asyncio.TimeoutError:
                        test_result["error"] = f"心跳 {i+1} 响应超时"
                        self.logger.warning(f"⚠️ 心跳 {i+1} 响应超时")
                        break
                
                if len(test_result["heartbeat_responses"]) > 0:
                    test_result["success"] = True
                    avg_rtt = sum([h["rtt"] for h in test_result["heartbeat_responses"]]) / len(test_result["heartbeat_responses"])
                    test_result["average_rtt"] = avg_rtt
                    self.logger.info(f"✅ {market_type} 心跳测试成功，平均RTT: {avg_rtt:.3f}秒")
                
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"❌ {market_type} 心跳测试失败: {e}")
        
        test_result["end_time"] = time.time()
        test_result["duration"] = test_result["end_time"] - test_result["start_time"]
        
        return test_result
    
    async def test_silent_disconnect_detection(self, url: str, market_type: str) -> Dict[str, Any]:
        """测试静默断开检测"""
        self.logger.info(f"🔇 测试 {market_type} 静默断开检测")
        
        test_result = {
            "test_type": "silent_disconnect_detection",
            "market_type": market_type,
            "url": url,
            "start_time": time.time(),
            "success": False,
            "messages_received": 0,
            "last_message_time": 0,
            "silent_periods": [],
            "error": None
        }
        
        try:
            async with websockets.connect(url) as websocket:
                # 订阅数据
                if market_type == "spot":
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "spot.order_book",
                        "event": "subscribe",
                        "payload": ["BTC_USDT", "10", "100ms"]
                    }
                else:
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "futures.order_book",
                        "event": "subscribe",
                        "payload": ["BTC_USDT"]
                    }
                
                await websocket.send(json.dumps(subscribe_msg))
                
                # 监听消息，检测静默期
                test_duration = 120  # 测试2分钟
                end_time = time.time() + test_duration
                last_msg_time = time.time()
                
                while time.time() < end_time:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=10)
                        current_time = time.time()
                        
                        # 检查是否有静默期
                        silent_duration = current_time - last_msg_time
                        if silent_duration > 30:  # 超过30秒没有消息
                            test_result["silent_periods"].append({
                                "start_time": last_msg_time,
                                "end_time": current_time,
                                "duration": silent_duration
                            })
                            self.logger.warning(f"🔇 检测到静默期: {silent_duration:.1f}秒")
                        
                        test_result["messages_received"] += 1
                        test_result["last_message_time"] = current_time
                        last_msg_time = current_time
                        
                        if test_result["messages_received"] % 100 == 0:
                            self.logger.info(f"📊 已接收 {test_result['messages_received']} 条消息")
                        
                    except asyncio.TimeoutError:
                        current_time = time.time()
                        silent_duration = current_time - last_msg_time
                        
                        test_result["silent_periods"].append({
                            "start_time": last_msg_time,
                            "end_time": current_time,
                            "duration": silent_duration,
                            "type": "timeout"
                        })
                        
                        self.logger.warning(f"🔇 检测到超时静默期: {silent_duration:.1f}秒")
                        break
                
                test_result["success"] = True
                self.logger.info(f"✅ {market_type} 静默断开检测完成，接收 {test_result['messages_received']} 条消息")
                
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"❌ {market_type} 静默断开检测失败: {e}")
        
        test_result["end_time"] = time.time()
        test_result["duration"] = test_result["end_time"] - test_result["start_time"]
        
        return test_result
    
    async def test_connection_recovery(self, url: str, market_type: str) -> Dict[str, Any]:
        """测试连接恢复机制"""
        self.logger.info(f"🔄 测试 {market_type} 连接恢复机制")
        
        test_result = {
            "test_type": "connection_recovery",
            "market_type": market_type,
            "url": url,
            "start_time": time.time(),
            "success": False,
            "reconnection_attempts": [],
            "error": None
        }
        
        try:
            # 模拟连接断开和重连
            for attempt in range(3):
                self.logger.info(f"🔄 连接尝试 {attempt + 1}/3")
                
                reconnect_start = time.time()
                try:
                    async with websockets.connect(url) as websocket:
                        reconnect_time = time.time() - reconnect_start
                        
                        # 测试连接是否正常工作
                        ping_msg = {
                            "time": int(time.time()),
                            "channel": f"{market_type}.ping"
                        }
                        
                        await websocket.send(json.dumps(ping_msg))
                        response = await asyncio.wait_for(websocket.recv(), timeout=5)
                        
                        reconnect_result = {
                            "attempt": attempt + 1,
                            "reconnect_time": reconnect_time,
                            "success": True,
                            "response": json.loads(response)
                        }
                        
                        test_result["reconnection_attempts"].append(reconnect_result)
                        self.logger.info(f"✅ 重连成功 {attempt + 1}: {reconnect_time:.3f}秒")
                        
                        # 模拟短暂使用后断开
                        await asyncio.sleep(2)
                        
                except Exception as e:
                    reconnect_result = {
                        "attempt": attempt + 1,
                        "reconnect_time": time.time() - reconnect_start,
                        "success": False,
                        "error": str(e)
                    }
                    
                    test_result["reconnection_attempts"].append(reconnect_result)
                    self.logger.error(f"❌ 重连失败 {attempt + 1}: {e}")
                
                # 等待一段时间再尝试下次重连
                if attempt < 2:
                    await asyncio.sleep(5)
            
            successful_reconnects = len([r for r in test_result["reconnection_attempts"] if r["success"]])
            test_result["success"] = successful_reconnects > 0
            test_result["success_rate"] = successful_reconnects / len(test_result["reconnection_attempts"])
            
            self.logger.info(f"✅ {market_type} 连接恢复测试完成，成功率: {test_result['success_rate']:.1%}")
            
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"❌ {market_type} 连接恢复测试失败: {e}")
        
        test_result["end_time"] = time.time()
        test_result["duration"] = test_result["end_time"] - test_result["start_time"]
        
        return test_result
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """运行综合测试"""
        self.logger.info("🚀 开始WebSocket连接健康状态综合测试...")
        
        test_start_time = time.time()
        
        # 测试所有市场类型
        for market_type, url in self.gate_urls.items():
            self.logger.info(f"\n🔍 测试 Gate.io {market_type.upper()} 市场")
            
            # 基本连接测试
            connection_test = await self.test_basic_connection(url, market_type)
            self.test_results["connection_tests"].append(connection_test)
            
            # 心跳机制测试
            heartbeat_test = await self.test_heartbeat_mechanism(url, market_type)
            self.test_results["heartbeat_tests"].append(heartbeat_test)
            
            # 静默断开检测测试
            silent_test = await self.test_silent_disconnect_detection(url, market_type)
            self.test_results["silent_disconnect_tests"].append(silent_test)
            
            # 连接恢复测试
            recovery_test = await self.test_connection_recovery(url, market_type)
            self.test_results["recovery_tests"].append(recovery_test)
            
            await asyncio.sleep(2)  # 测试间隔
        
        # 生成测试报告
        test_report = {
            "test_time": datetime.now().isoformat(),
            "total_duration": time.time() - test_start_time,
            "test_results": self.test_results,
            "summary": self._generate_test_summary()
        }
        
        return test_report
    
    def _generate_test_summary(self) -> Dict[str, Any]:
        """生成测试摘要"""
        summary = {
            "connection_success_rate": 0,
            "heartbeat_success_rate": 0,
            "silent_disconnect_issues": 0,
            "recovery_success_rate": 0,
            "recommendations": []
        }
        
        # 连接成功率
        connection_successes = len([t for t in self.test_results["connection_tests"] if t["success"]])
        if self.test_results["connection_tests"]:
            summary["connection_success_rate"] = connection_successes / len(self.test_results["connection_tests"])
        
        # 心跳成功率
        heartbeat_successes = len([t for t in self.test_results["heartbeat_tests"] if t["success"]])
        if self.test_results["heartbeat_tests"]:
            summary["heartbeat_success_rate"] = heartbeat_successes / len(self.test_results["heartbeat_tests"])
        
        # 静默断开问题
        for test in self.test_results["silent_disconnect_tests"]:
            summary["silent_disconnect_issues"] += len(test.get("silent_periods", []))
        
        # 恢复成功率
        recovery_successes = len([t for t in self.test_results["recovery_tests"] if t["success"]])
        if self.test_results["recovery_tests"]:
            summary["recovery_success_rate"] = recovery_successes / len(self.test_results["recovery_tests"])
        
        # 生成建议
        if summary["connection_success_rate"] < 1.0:
            summary["recommendations"].append("改进基本连接稳定性")
        
        if summary["heartbeat_success_rate"] < 1.0:
            summary["recommendations"].append("优化心跳机制")
        
        if summary["silent_disconnect_issues"] > 0:
            summary["recommendations"].append("增强静默断开检测和处理")
        
        if summary["recovery_success_rate"] < 1.0:
            summary["recommendations"].append("改进连接恢复机制")
        
        return summary


async def main():
    """主函数"""
    tester = WebSocketConnectionHealthTester()
    
    print("🔍 WebSocket连接健康状态测试")
    print("=" * 50)
    
    # 运行综合测试
    report = await tester.run_comprehensive_tests()
    
    # 输出测试结果
    print(f"\n📊 测试完成，总耗时: {report['total_duration']:.1f}秒")
    print(f"连接成功率: {report['summary']['connection_success_rate']:.1%}")
    print(f"心跳成功率: {report['summary']['heartbeat_success_rate']:.1%}")
    print(f"静默断开问题: {report['summary']['silent_disconnect_issues']} 个")
    print(f"恢复成功率: {report['summary']['recovery_success_rate']:.1%}")
    
    if report['summary']['recommendations']:
        print("\n💡 建议:")
        for rec in report['summary']['recommendations']:
            print(f"  • {rec}")
    
    # 保存测试报告
    report_file = f"123/diagnostic_results/websocket_health_test_{int(time.time())}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 详细测试报告已保存到: {report_file}")
    
    return report


if __name__ == "__main__":
    asyncio.run(main())
