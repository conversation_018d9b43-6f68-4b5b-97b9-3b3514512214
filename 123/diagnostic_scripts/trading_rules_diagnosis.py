#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 交易规则获取失败诊断脚本
精准定位SPK-USDT_gate_spot交易规则获取失败的根本原因
"""

import os
import sys
import asyncio
import logging
import time
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TradingRulesDiagnosis:
    """交易规则获取失败诊断器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.diagnosis_results = {}
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('trading_rules_diagnosis.log')
            ]
        )
        
    async def run_diagnosis(self):
        """运行完整诊断"""
        self.setup_logging()
        self.logger.info("🔍 开始交易规则获取失败诊断...")
        
        # 1. 检查环境变量配置
        await self._check_environment_variables()
        
        # 2. 检查全局交易所实例
        await self._check_global_exchanges()
        
        # 3. 检查交易规则预加载器
        await self._check_trading_rules_preloader()
        
        # 4. 检查临时实例创建
        await self._check_temporary_instance_creation()
        
        # 5. 检查API调用能力
        await self._check_api_calling_capability()
        
        # 6. 生成诊断报告
        self._generate_diagnosis_report()
        
    async def _check_environment_variables(self):
        """检查环境变量配置"""
        self.logger.info("🔍 1. 检查环境变量配置...")
        
        required_vars = {
            "GATE_API_KEY": os.getenv("GATE_API_KEY"),
            "GATE_API_SECRET": os.getenv("GATE_API_SECRET"),
            "BYBIT_API_KEY": os.getenv("BYBIT_API_KEY"),
            "BYBIT_API_SECRET": os.getenv("BYBIT_API_SECRET"),
            "OKX_API_KEY": os.getenv("OKX_API_KEY"),
            "OKX_API_SECRET": os.getenv("OKX_API_SECRET"),
            "OKX_API_PASSPHRASE": os.getenv("OKX_API_PASSPHRASE")
        }
        
        missing_vars = []
        for var_name, var_value in required_vars.items():
            if not var_value:
                missing_vars.append(var_name)
                self.logger.warning(f"   ❌ {var_name}: 未配置")
            else:
                self.logger.info(f"   ✅ {var_name}: 已配置 (长度: {len(var_value)})")
                
        self.diagnosis_results["environment_variables"] = {
            "missing_count": len(missing_vars),
            "missing_vars": missing_vars,
            "total_vars": len(required_vars),
            "status": "正常" if len(missing_vars) == 0 else "缺少配置"
        }
        
    async def _check_global_exchanges(self):
        """检查全局交易所实例"""
        self.logger.info("🔍 2. 检查全局交易所实例...")
        
        try:
            # 检查get_global_exchanges函数
            from core.trading_system_initializer import get_global_exchanges
            global_exchanges = get_global_exchanges()
            
            if global_exchanges is None:
                self.logger.warning("   ❌ get_global_exchanges()返回None")
                self.diagnosis_results["global_exchanges"] = {
                    "status": "发现问题",
                    "issue": "get_global_exchanges返回None",
                    "impact": "交易规则预加载无法获取交易所实例"
                }
            else:
                self.logger.info(f"   ✅ get_global_exchanges()返回{len(global_exchanges)}个交易所")
                self.diagnosis_results["global_exchanges"] = {
                    "status": "正常",
                    "count": len(global_exchanges),
                    "exchanges": list(global_exchanges.keys())
                }
                
        except ImportError as e:
            self.logger.warning(f"   ⚠️ 无法导入get_global_exchanges: {e}")
            self.diagnosis_results["global_exchanges"] = {
                "status": "函数不存在",
                "issue": "get_global_exchanges函数不存在或无法导入"
            }
            
    async def _check_trading_rules_preloader(self):
        """检查交易规则预加载器"""
        self.logger.info("🔍 3. 检查交易规则预加载器...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            if preloader is None:
                self.logger.warning("   ❌ 交易规则预加载器为None")
                self.diagnosis_results["preloader"] = {
                    "status": "发现问题",
                    "issue": "预加载器实例为None"
                }
                return
                
            # 检查预加载器状态
            self.logger.info(f"   ✅ 预加载器实例存在")
            self.logger.info(f"   📊 缓存规则数量: {len(preloader.trading_rules)}")
            self.logger.info(f"   📊 预加载完成: {preloader.preload_completed}")
            self.logger.info(f"   📊 正在预加载: {preloader.is_preloading}")
            
            # 测试获取SPK-USDT交易规则
            test_symbol = "SPK-USDT"
            test_exchange = "gate"
            test_market = "spot"
            
            self.logger.info(f"   🧪 测试获取交易规则: {test_symbol}_{test_exchange}_{test_market}")
            rule = preloader.get_trading_rule(test_exchange, test_symbol, test_market)
            
            if rule:
                self.logger.info(f"   ✅ 成功获取交易规则: {rule}")
                self.diagnosis_results["preloader"] = {
                    "status": "正常",
                    "cache_count": len(preloader.trading_rules),
                    "test_result": "成功"
                }
            else:
                self.logger.warning(f"   ❌ 无法获取交易规则: {test_symbol}_{test_exchange}_{test_market}")
                self.diagnosis_results["preloader"] = {
                    "status": "发现问题",
                    "issue": "无法获取测试交易规则",
                    "cache_count": len(preloader.trading_rules)
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ 检查预加载器失败: {e}")
            self.diagnosis_results["preloader"] = {
                "status": "检查失败",
                "error": str(e)
            }
            
    async def _check_temporary_instance_creation(self):
        """检查临时实例创建"""
        self.logger.info("🔍 4. 检查临时实例创建...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            if preloader is None:
                self.logger.warning("   ❌ 预加载器为None，无法测试临时实例创建")
                return
                
            # 测试创建三个交易所的临时实例
            exchanges_to_test = ["gate", "bybit", "okx"]
            creation_results = {}
            
            for exchange_name in exchanges_to_test:
                self.logger.info(f"   🧪 测试创建{exchange_name}临时实例...")
                
                try:
                    instance = preloader._create_temporary_exchange_instance_sync(exchange_name)
                    
                    if instance:
                        self.logger.info(f"   ✅ {exchange_name}临时实例创建成功: {type(instance).__name__}")
                        creation_results[exchange_name] = "成功"
                    else:
                        self.logger.warning(f"   ❌ {exchange_name}临时实例创建失败: 返回None")
                        creation_results[exchange_name] = "失败-返回None"
                        
                except Exception as e:
                    self.logger.error(f"   ❌ {exchange_name}临时实例创建异常: {e}")
                    creation_results[exchange_name] = f"异常-{str(e)}"
                    
            self.diagnosis_results["temporary_instances"] = {
                "results": creation_results,
                "success_count": sum(1 for result in creation_results.values() if result == "成功"),
                "total_count": len(exchanges_to_test)
            }
            
        except Exception as e:
            self.logger.error(f"   ❌ 检查临时实例创建失败: {e}")
            self.diagnosis_results["temporary_instances"] = {
                "status": "检查失败",
                "error": str(e)
            }
            
    async def _check_api_calling_capability(self):
        """检查API调用能力"""
        self.logger.info("🔍 5. 检查API调用能力...")
        
        # 这里可以添加实际的API调用测试
        # 由于涉及真实API调用，暂时跳过
        self.diagnosis_results["api_capability"] = {
            "status": "跳过",
            "reason": "避免真实API调用"
        }
        
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        self.logger.info("📊 生成诊断报告...")
        
        print("\n" + "="*80)
        print("🔍 交易规则获取失败诊断报告")
        print("="*80)
        
        # 环境变量检查结果
        env_result = self.diagnosis_results.get("environment_variables", {})
        print(f"\n1. 环境变量配置: {env_result.get('status', '未知')}")
        if env_result.get("missing_vars"):
            print(f"   缺少配置: {', '.join(env_result['missing_vars'])}")
        print(f"   配置完整度: {env_result.get('total_vars', 0) - env_result.get('missing_count', 0)}/{env_result.get('total_vars', 0)}")
        
        # 全局交易所实例检查结果
        global_result = self.diagnosis_results.get("global_exchanges", {})
        print(f"\n2. 全局交易所实例: {global_result.get('status', '未知')}")
        if global_result.get("issue"):
            print(f"   问题: {global_result['issue']}")
            print(f"   影响: {global_result.get('impact', '未知')}")
        elif global_result.get("count"):
            print(f"   交易所数量: {global_result['count']}")
            print(f"   交易所列表: {', '.join(global_result.get('exchanges', []))}")
            
        # 预加载器检查结果
        preloader_result = self.diagnosis_results.get("preloader", {})
        print(f"\n3. 交易规则预加载器: {preloader_result.get('status', '未知')}")
        if preloader_result.get("issue"):
            print(f"   问题: {preloader_result['issue']}")
        elif preloader_result.get("cache_count") is not None:
            print(f"   缓存规则数量: {preloader_result['cache_count']}")
            print(f"   测试结果: {preloader_result.get('test_result', '未知')}")
            
        # 临时实例创建检查结果
        temp_result = self.diagnosis_results.get("temporary_instances", {})
        print(f"\n4. 临时实例创建:")
        if temp_result.get("results"):
            for exchange, result in temp_result["results"].items():
                status = "✅" if result == "成功" else "❌"
                print(f"   {status} {exchange}: {result}")
            print(f"   成功率: {temp_result.get('success_count', 0)}/{temp_result.get('total_count', 0)}")
            
        # 根本原因分析
        print(f"\n🎯 根本原因分析:")
        if global_result.get("status") == "发现问题":
            print("   ❌ 核心问题: get_global_exchanges()返回None")
            print("   📋 影响: 交易规则预加载器无法获取交易所实例进行API调用")
            print("   🔧 解决方案: 需要在系统启动时正确设置全局交易所实例")
        elif env_result.get("missing_count", 0) > 0:
            print("   ❌ 核心问题: API密钥配置不完整")
            print("   📋 影响: 无法创建临时交易所实例进行API调用")
            print("   🔧 解决方案: 配置完整的API密钥")
        else:
            print("   ✅ 未发现明显问题，需要进一步调查")
            
        print("\n" + "="*80)

async def main():
    """主函数"""
    diagnosis = TradingRulesDiagnosis()
    await diagnosis.run_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
