#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确诊断脚本：reconnect_tasks未定义错误
分析并定位WebSocket管理器中的变量定义问题
"""

import ast
import re
import os
from typing import List, Dict, Tuple

class ReconnectTasksDiagnosis:
    def __init__(self):
        self.ws_manager_path = "C:\\Users\\<USER>\\Desktop\\66 修复websocket\\123\\websocket\\ws_manager.py"
        self.results = {
            "error_locations": [],
            "variable_usages": [],
            "method_analysis": {},
            "fix_recommendations": []
        }
    
    def analyze_file(self):
        """分析ws_manager.py文件中的reconnect_tasks问题"""
        print("开始分析WebSocket管理器文件...")
        
        try:
            with open(self.ws_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 1. 查找所有reconnect_tasks的使用
            self._find_variable_usages(lines)
            
            # 2. 分析_monitor_status方法
            self._analyze_monitor_status_method(content)
            
            # 3. 检查是否有初始化代码
            self._check_initialization(lines)
            
            # 4. 生成修复建议
            self._generate_fix_recommendations()
            
            return self.results
            
        except Exception as e:
            print(f"分析文件时出错: {e}")
            return None
    
    def _find_variable_usages(self, lines: List[str]):
        """查找所有reconnect_tasks的使用位置"""
        print("查找reconnect_tasks使用位置...")
        
        for line_num, line in enumerate(lines, 1):
            if 'reconnect_tasks' in line:
                usage_type = self._classify_usage(line)
                self.results["variable_usages"].append({
                    "line_number": line_num,
                    "line_content": line.strip(),
                    "usage_type": usage_type
                })
                
                if usage_type in ["extend", "append", "if_check"]:
                    self.results["error_locations"].append({
                        "line": line_num,
                        "content": line.strip(),
                        "error": "Variable 'reconnect_tasks' is not defined",
                        "severity": "HIGH"
                    })
    
    def _classify_usage(self, line: str) -> str:
        """分类变量使用类型"""
        line = line.strip()
        if 'reconnect_tasks.extend(' in line:
            return "extend"
        elif 'reconnect_tasks.append(' in line:
            return "append"
        elif 'if reconnect_tasks:' in line:
            return "if_check"
        elif 'reconnect_tasks =' in line:
            return "assignment"
        else:
            return "other"
    
    def _analyze_monitor_status_method(self, content: str):
        """分析_monitor_status方法结构"""
        print("分析_monitor_status方法...")
        
        # 使用正则找到方法定义
        method_pattern = r'async def _monitor_status\(self\):(.*?)(?=^\s{4}async def|\s{4}def|$)'
        match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
        
        if match:
            method_content = match.group(1)
            lines = method_content.split('\n')
            
            # 查找变量初始化
            initializations = []
            for line_num, line in enumerate(lines, 1):
                if '=' in line and not line.strip().startswith('#'):
                    var_name = line.split('=')[0].strip()
                    if not var_name.startswith('self.'):
                        initializations.append({
                            "variable": var_name,
                            "line": line.strip()
                        })
            
            self.results["method_analysis"]["_monitor_status"] = {
                "has_reconnect_tasks_init": any('reconnect_tasks' in init['variable'] for init in initializations),
                "local_variables": initializations,
                "method_length": len(lines)
            }
    
    def _check_initialization(self, lines: List[str]):
        """检查是否有reconnect_tasks的初始化"""
        print("检查变量初始化...")
        
        init_found = False
        for line_num, line in enumerate(lines, 1):
            if 'reconnect_tasks' in line and '=' in line and not line.strip().startswith('#'):
                # 检查是否是初始化
                if 'reconnect_tasks = ' in line or 'reconnect_tasks=' in line:
                    init_found = True
                    self.results["variable_usages"].append({
                        "line_number": line_num,
                        "line_content": line.strip(),
                        "usage_type": "initialization"
                    })
        
        self.results["method_analysis"]["has_initialization"] = init_found
    
    def _generate_fix_recommendations(self):
        """生成修复建议"""
        print("生成修复建议...")
        
        # 基于分析结果生成建议
        if not self.results["method_analysis"].get("has_initialization", False):
            self.results["fix_recommendations"].append({
                "priority": "HIGH",
                "type": "ADD_INITIALIZATION",
                "description": "在_monitor_status方法开始处添加reconnect_tasks = []初始化",
                "code_suggestion": "reconnect_tasks = []  # 初始化重连任务列表",
                "location": "_monitor_status方法开始处"
            })
        
        # 检查使用模式
        usage_types = [usage["usage_type"] for usage in self.results["variable_usages"]]
        if "extend" in usage_types or "append" in usage_types:
            self.results["fix_recommendations"].append({
                "priority": "MEDIUM", 
                "type": "IMPROVE_LOGIC",
                "description": "考虑将reconnect_tasks作为实例变量而非局部变量",
                "code_suggestion": "self.reconnect_tasks = getattr(self, 'reconnect_tasks', [])",
                "location": "__init__方法或_monitor_status方法"
            })
    
    def print_diagnosis_report(self):
        """打印详细诊断报告"""
        print("\n" + "="*60)
        print("RECONNECT_TASKS 错误诊断报告")
        print("="*60)
        
        # 错误位置
        if self.results["error_locations"]:
            print(f"\n发现 {len(self.results['error_locations'])} 个错误位置:")
            for error in self.results["error_locations"]:
                print(f"   第{error['line']}行: {error['content']}")
                print(f"   错误: {error['error']}")
                print()
        
        # 变量使用分析
        print(f"\n变量使用统计:")
        usage_stats = {}
        for usage in self.results["variable_usages"]:
            usage_type = usage["usage_type"]
            usage_stats[usage_type] = usage_stats.get(usage_type, 0) + 1
        
        for usage_type, count in usage_stats.items():
            print(f"   {usage_type}: {count}次")
        
        # 方法分析
        if "_monitor_status" in self.results["method_analysis"]:
            analysis = self.results["method_analysis"]["_monitor_status"]
            print(f"\n_monitor_status方法分析:")
            print(f"   是否有reconnect_tasks初始化: {analysis['has_reconnect_tasks_init']}")
            print(f"   方法长度: {analysis['method_length']}行")
            print(f"   局部变量数量: {len(analysis['local_variables'])}")
        
        # 修复建议
        if self.results["fix_recommendations"]:
            print(f"\n修复建议 ({len(self.results['fix_recommendations'])}项):")
            for i, rec in enumerate(self.results["fix_recommendations"], 1):
                print(f"\n   建议{i} [{rec['priority']}]:")
                print(f"   类型: {rec['type']}")
                print(f"   描述: {rec['description']}")
                print(f"   建议代码: {rec['code_suggestion']}")
                print(f"   修改位置: {rec['location']}")

def main():
    """主函数"""
    print("启动reconnect_tasks错误精确诊断...")
    
    diagnosis = ReconnectTasksDiagnosis()
    results = diagnosis.analyze_file()
    
    if results:
        diagnosis.print_diagnosis_report()
        
        # 保存诊断结果
        print(f"\n诊断结果已保存到内存，包含:")
        print(f"   - {len(results['error_locations'])} 个错误位置")
        print(f"   - {len(results['variable_usages'])} 个变量使用")
        print(f"   - {len(results['fix_recommendations'])} 个修复建议")
        
        return results
    else:
        print("诊断失败")
        return None

if __name__ == "__main__":
    main()