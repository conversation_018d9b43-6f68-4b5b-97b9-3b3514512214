#!/usr/bin/env python3
"""
修复验证脚本
测试API数据优先修复是否生效
"""

import os
import sys
import asyncio
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_fix():
    """测试修复效果"""
    try:
        print("[开始] 测试API数据优先修复...")
        
        # 1. 清理现有缓存
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 清理ICNT-USDT的缓存
        cache_key = "bybit_ICNT-USDT_futures"
        if cache_key in preloader.trading_rules:
            del preloader.trading_rules[cache_key]
            print(f"[清理] 已删除缓存: {cache_key}")
            
        # 2. 强制重新加载
        print(f"\n[测试] 强制重新加载交易规则...")
        
        # 获取规则（这会触发重新加载）
        rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        if rule:
            print(f"\n[结果] 重新加载的规则:")
            print(f"  symbol: {rule.symbol}")
            print(f"  qty_step: {rule.qty_step}")
            print(f"  source: {rule.source}")
            print(f"  timestamp: {rule.timestamp}")
            
            # 3. 验证修复效果
            expected_step_size = 1.0  # 根据API查询结果
            actual_step_size = float(rule.qty_step)
            
            print(f"\n[验证] 修复效果检查:")
            print(f"  期望步长: {expected_step_size}")
            print(f"  实际步长: {actual_step_size}")
            print(f"  数据来源: {rule.source}")
            
            if abs(actual_step_size - expected_step_size) < 0.0001:
                print(f"  OK 修复成功！步长正确")
                
                # 4. 测试166.904的处理
                test_amount = 166.904
                
                # 使用正确的步长进行截取
                from decimal import Decimal
                amount_decimal = Decimal(str(test_amount))
                step_decimal = Decimal(str(actual_step_size))
                truncated = (amount_decimal // step_decimal) * step_decimal
                
                print(f"\n[测试] 166.904数量处理:")
                print(f"  输入: {test_amount}")
                print(f"  步长: {actual_step_size}")
                print(f"  截取后: {float(truncated)}")
                
                # 期望结果：166.904 ÷ 1.0 = 166.904，截取后应该是 166.0
                expected_result = 166.0
                if abs(float(truncated) - expected_result) < 0.0001:
                    print(f"  OK 截取结果正确：{float(truncated)}")
                else:
                    print(f"  ERROR 截取结果错误：期望{expected_result}，实际{float(truncated)}")
                    
                # 5. 测试统一格式化方法
                formatted = preloader.format_amount_unified(test_amount, "bybit", "ICNT-USDT", "futures")
                print(f"\n[测试] format_amount_unified:")
                print(f"  输入: {test_amount}")
                print(f"  格式化结果: '{formatted}'")
                
                if formatted == "166":
                    print(f"  OK 格式化结果正确")
                else:
                    print(f"  WARNING 格式化结果: '{formatted}'，应检查精度设置")
                    
            else:
                print(f"  ERROR 修复失败！步长不正确")
                print(f"      可能仍在使用默认值: {actual_step_size}")
                
        else:
            print("[错误] 无法获取交易规则")
            
    except Exception as e:
        print(f"[异常] 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    asyncio.run(test_fix())