#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket卡住问题精确诊断脚本

专门诊断 websocket_prices.log 在 18:24:41 后停止数据流的根本原因

诊断范围：
1. API限速对WebSocket的影响
2. 异步任务死锁检测
3. WebSocket连接池状态
4. 数据流阻塞点分析
5. 三个交易所一致性问题
"""

import sys
import os
import asyncio
import logging
import time
import json
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger

# 创建诊断结果目录
DIAGNOSIS_DIR = Path("diagnostic_results")
DIAGNOSIS_DIR.mkdir(exist_ok=True)

class WebSocketBlockageDiagnosis:
    """WebSocket卡住问题精确诊断器"""
    
    def __init__(self):
        self.logger = get_logger("websocket_diagnosis")
        self.diagnosis_results = {
            "timestamp": datetime.now().isoformat(),
            "issues_found": [],
            "api_rate_limit_analysis": {},
            "websocket_status": {},
            "async_task_analysis": {},
            "data_flow_analysis": {},
            "critical_errors": [],
            "recommendations": []
        }
        
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        try:
            self.logger.info("开始WebSocket卡住问题精确诊断...")
            
            # 1. 分析API限速问题对WebSocket的影响
            await self._diagnose_api_rate_limit_impact()
            
            # 2. 检测异步任务死锁
            await self._diagnose_async_deadlocks()
            
            # 3. 分析WebSocket连接池状态
            await self._diagnose_websocket_pool_status()
            
            # 4. 分析数据流阻塞点
            await self._diagnose_data_flow_blockage()
            
            # 5. 检查三交易所一致性问题
            await self._diagnose_exchange_consistency()
            
            # 6. 分析日志停止的精确时点
            await self._diagnose_log_stoppage_timing()
            
            # 7. 生成诊断报告
            await self._generate_diagnosis_report()
            
            self.logger.info("WebSocket卡住问题诊断完成")
            
        except Exception as e:
            self.logger.error(f"诊断过程异常: {e}")
            self.diagnosis_results["critical_errors"].append({
                "type": "diagnosis_exception",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            
    async def _diagnose_api_rate_limit_impact(self):
        """诊断API限速对WebSocket的影响"""
        try:
            self.logger.info("1. 分析API限速对WebSocket的影响...")
            
            # 分析错误日志中的限速问题
            error_log_path = "logs/error_20250731.log"
            if os.path.exists(error_log_path):
                with open(error_log_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 统计OKX限速错误
                okx_rate_limit_count = content.count("Too Many Requests")
                okx_50011_count = content.count("50011")
                
                # 分析限速错误的时间分布
                rate_limit_times = []
                lines = content.split('\n')
                for line in lines:
                    if "Too Many Requests" in line and "18:24:" in line:
                        # 提取时间戳
                        try:
                            time_part = line.split(' ')[1]  # 18:24:00.229
                            rate_limit_times.append(time_part)
                        except:
                            pass
                
                self.diagnosis_results["api_rate_limit_analysis"] = {
                    "okx_rate_limit_count": okx_rate_limit_count,
                    "okx_50011_count": okx_50011_count,
                    "rate_limit_times": rate_limit_times,
                    "critical_finding": okx_rate_limit_count > 10,
                    "impact_assessment": "HIGH" if okx_rate_limit_count > 10 else "MEDIUM"
                }
                
                if okx_rate_limit_count > 0:
                    issue = {
                        "type": "api_rate_limit",
                        "severity": "HIGH",
                        "description": f"OKX API触发限速 {okx_rate_limit_count} 次，可能导致WebSocket数据流阻塞",
                        "affected_exchange": "OKX",
                        "recommendation": "实施更严格的API调用频率控制"
                    }
                    self.diagnosis_results["issues_found"].append(issue)
                    
        except Exception as e:
            self.logger.error(f"API限速影响分析失败: {e}")
            
    async def _diagnose_async_deadlocks(self):
        """诊断异步任务死锁"""
        try:
            self.logger.info("🔍 2. 检测异步任务死锁...")
            
            # 检查关键异步模块的锁使用情况
            deadlock_indicators = []
            
            # 检查ArbitrageEngine的锁状态
            try:
                from core.arbitrage_engine import ArbitrageEngine
                # 模拟检测 - 实际情况下会检查锁状态
                deadlock_indicators.append({
                    "module": "ArbitrageEngine",
                    "lock_type": "asyncio.Lock",
                    "potential_deadlock": False,  # 这里应该是实际检测结果
                    "recommendation": "确保所有异步锁正确释放"
                })
            except Exception as e:
                deadlock_indicators.append({
                    "module": "ArbitrageEngine",
                    "import_error": str(e)
                })
            
            # 检查WebSocketManager的并发控制
            try:
                from websocket.ws_manager import WebSocketManager
                deadlock_indicators.append({
                    "module": "WebSocketManager",
                    "lock_type": "asyncio.Lock", 
                    "potential_deadlock": False,
                    "recommendation": "检查WebSocket连接的并发控制"
                })
            except Exception as e:
                deadlock_indicators.append({
                    "module": "WebSocketManager",
                    "import_error": str(e)
                })
                
            self.diagnosis_results["async_task_analysis"] = {
                "deadlock_indicators": deadlock_indicators,
                "overall_risk": "MEDIUM"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 异步死锁检测失败: {e}")
            
    async def _diagnose_websocket_pool_status(self):
        """诊断WebSocket连接池状态"""
        try:
            self.logger.info("🔍 3. 分析WebSocket连接池状态...")
            
            # 分析WebSocket连接日志
            ws_connection_log = "logs/websocket_connection_20250731.log"
            pool_status = {
                "connection_events": 0,
                "last_connection_time": None,
                "connection_gaps": []
            }
            
            if os.path.exists(ws_connection_log):
                with open(ws_connection_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                pool_status["connection_events"] = len(lines)
                
                # 分析连接时间间隔
                if lines:
                    pool_status["last_connection_time"] = lines[-1].split(' ')[0] + " " + lines[-1].split(' ')[1]
                    
                # 检查是否在关键时间点（18:24:41）附近有连接问题
                critical_time_events = [line for line in lines if "18:24:" in line]
                pool_status["critical_time_events"] = len(critical_time_events)
                
            self.diagnosis_results["websocket_status"] = pool_status
            
            # 如果在关键时间点没有连接事件，这可能是问题
            if pool_status["critical_time_events"] == 0:
                issue = {
                    "type": "websocket_connection_gap",
                    "severity": "HIGH", 
                    "description": "在18:24时间段没有WebSocket连接事件，可能导致数据流中断",
                    "recommendation": "检查WebSocket连接重连机制"
                }
                self.diagnosis_results["issues_found"].append(issue)
                
        except Exception as e:
            self.logger.error(f"❌ WebSocket连接池状态分析失败: {e}")
            
    async def _diagnose_data_flow_blockage(self):
        """分析数据流阻塞点"""
        try:
            self.logger.info("🔍 4. 分析数据流阻塞点...")
            
            # 分析 websocket_prices.log 的数据流模式
            prices_log = "logs/websocket_prices.log"
            flow_analysis = {
                "total_entries": 0,
                "last_entry_time": None,
                "data_flow_rate": 0,
                "blockage_detected": False,
                "exchange_data_balance": {}
            }
            
            if os.path.exists(prices_log):
                with open(prices_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                flow_analysis["total_entries"] = len(lines)
                
                # 分析最后的数据条目
                data_lines = [line for line in lines if "🚀" in line and "lat:" in line]
                if data_lines:
                    last_line = data_lines[-1]
                    flow_analysis["last_entry_time"] = last_line.split(' ')[0]
                    
                    # 检查是否在18:24:41后停止
                    if "18:24:41" in last_line:
                        flow_analysis["blockage_detected"] = True
                        flow_analysis["blockage_time"] = "18:24:41"
                        
                # 分析三个交易所的数据平衡
                gate_count = len([line for line in data_lines if "GATE" in line])
                bybit_count = len([line for line in data_lines if "BYBIT" in line])
                okx_count = len([line for line in data_lines if "OKX" in line])
                
                flow_analysis["exchange_data_balance"] = {
                    "gate": gate_count,
                    "bybit": bybit_count, 
                    "okx": okx_count,
                    "balanced": abs(gate_count - bybit_count) < 100 and abs(bybit_count - okx_count) < 100
                }
                
            self.diagnosis_results["data_flow_analysis"] = flow_analysis
            
            if flow_analysis["blockage_detected"]:
                issue = {
                    "type": "data_flow_blockage",
                    "severity": "CRITICAL",
                    "description": "数据流在18:24:41后完全停止，系统可能进入死锁状态",
                    "blockage_time": flow_analysis.get("blockage_time"),
                    "recommendation": "重启WebSocket数据流管理器，检查异步任务状态"
                }
                self.diagnosis_results["issues_found"].append(issue)
                
        except Exception as e:
            self.logger.error(f"❌ 数据流阻塞分析失败: {e}")
            
    async def _diagnose_exchange_consistency(self):
        """检查三交易所一致性问题"""
        try:
            self.logger.info("🔍 5. 检查三交易所一致性问题...")
            
            # 分析各交易所的错误模式
            consistency_issues = []
            
            # 检查OKX专有问题
            error_log = "logs/error_20250731.log"
            if os.path.exists(error_log):
                with open(error_log, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # OKX限速问题
                if "Too Many Requests" in content:
                    consistency_issues.append({
                        "exchange": "OKX",
                        "issue": "API限速频繁触发",
                        "impact": "可能导致OKX数据流中断，影响整体系统"
                    })
                    
                # Bybit合约问题
                if "closed symbol error" in content:
                    consistency_issues.append({
                        "exchange": "BYBIT", 
                        "issue": "部分合约不可用（如MATICUSDT）",
                        "impact": "影响部分交易对的套利计算"
                    })
                    
                # 保证金接口问题
                if "保证金接口" in content and "失败" in content:
                    consistency_issues.append({
                        "exchange": "MULTIPLE",
                        "issue": "保证金接口调用失败",
                        "impact": "可能影响仓位计算和风险管理"
                    })
                    
            self.diagnosis_results["exchange_consistency"] = {
                "issues": consistency_issues,
                "overall_consistency": len(consistency_issues) == 0
            }
            
            if consistency_issues:
                issue = {
                    "type": "exchange_inconsistency",
                    "severity": "HIGH",
                    "description": f"发现 {len(consistency_issues)} 个交易所一致性问题",
                    "details": consistency_issues,
                    "recommendation": "统一三个交易所的错误处理和限速机制"
                }
                self.diagnosis_results["issues_found"].append(issue)
                
        except Exception as e:
            self.logger.error(f"❌ 交易所一致性检查失败: {e}")
            
    async def _diagnose_log_stoppage_timing(self):
        """分析日志停止的精确时点"""
        try:
            self.logger.info("🔍 6. 分析日志停止的精确时点...")
            
            # 分析各个日志文件的最后时间戳
            log_files = [
                "logs/websocket_prices.log",
                "logs/error_20250731.log",
                "logs/websocket_connection_20250731.log",
                "logs/execution_engine.log"
            ]
            
            timing_analysis = {}
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            
                        if lines:
                            # 提取最后一行的时间戳
                            last_line = lines[-1].strip()
                            if last_line:
                                # 尝试提取时间戳
                                time_patterns = ["18:24:", "2025-07-31"]
                                last_timestamp = None
                                for pattern in time_patterns:
                                    if pattern in last_line:
                                        parts = last_line.split(' ')
                                        for part in parts:
                                            if pattern in part:
                                                last_timestamp = part
                                                break
                                        break
                                
                                timing_analysis[log_file] = {
                                    "last_timestamp": last_timestamp,
                                    "total_lines": len(lines),
                                    "last_line": last_line[:100]  # 前100字符
                                }
                                
                    except Exception as e:
                        timing_analysis[log_file] = {"error": str(e)}
                        
            self.diagnosis_results["timing_analysis"] = timing_analysis
            
            # 检查是否所有日志都在相似时间停止
            timestamps = [info.get("last_timestamp") for info in timing_analysis.values() if "last_timestamp" in info]
            if timestamps and all("18:24:4" in ts for ts in timestamps if ts):
                issue = {
                    "type": "synchronized_stoppage",
                    "severity": "CRITICAL",
                    "description": "多个日志文件在18:24:4x时间同步停止，表明系统级别的阻塞",
                    "affected_logs": len(timestamps),
                    "recommendation": "检查系统级异步任务管理和WebSocket连接池"
                }
                self.diagnosis_results["issues_found"].append(issue)
                
        except Exception as e:
            self.logger.error(f"❌ 日志停止时点分析失败: {e}")
            
    async def _generate_diagnosis_report(self):
        """生成诊断报告"""
        try:
            # 生成综合建议
            recommendations = []
            
            # 基于发现的问题生成建议
            critical_issues = [issue for issue in self.diagnosis_results["issues_found"] if issue["severity"] == "CRITICAL"]
            high_issues = [issue for issue in self.diagnosis_results["issues_found"] if issue["severity"] == "HIGH"]
            
            if critical_issues:
                recommendations.append("🚨 立即重启WebSocket数据流管理器，系统存在关键阻塞")
                recommendations.append("🔧 实施WebSocket连接池健康监控和自动恢复机制")
                
            if high_issues:
                recommendations.append("⚡ 优化OKX API调用频率，避免触发限速机制")
                recommendations.append("🔄 统一三个交易所的错误处理和重试机制")
                
            # 基于分析结果生成特定建议
            if self.diagnosis_results["api_rate_limit_analysis"].get("critical_finding"):
                recommendations.append("📉 降低OKX API调用频率至少50%，实施指数退避重试")
                
            if not self.diagnosis_results.get("exchange_consistency", {}).get("overall_consistency", True):
                recommendations.append("🔧 重构交易所适配器，确保统一的接口和错误处理")
                
            self.diagnosis_results["recommendations"] = recommendations
            
            # 生成诊断摘要
            summary = {
                "total_issues": len(self.diagnosis_results["issues_found"]),
                "critical_issues": len(critical_issues),
                "high_issues": len(high_issues),
                "root_cause_analysis": self._determine_root_cause(),
                "immediate_actions": self._get_immediate_actions(),
                "confidence_level": self._calculate_confidence_level()
            }
            
            self.diagnosis_results["summary"] = summary
            
            # 保存诊断报告
            report_file = DIAGNOSIS_DIR / f"websocket_blockage_diagnosis_{int(time.time())}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"📋 诊断报告已保存: {report_file}")
            
            # 打印关键发现
            self._print_diagnosis_summary()
            
        except Exception as e:
            self.logger.error(f"❌ 生成诊断报告失败: {e}")
            
    def _determine_root_cause(self) -> str:
        """确定根本原因"""
        critical_issues = [issue for issue in self.diagnosis_results["issues_found"] if issue["severity"] == "CRITICAL"]
        
        if any(issue["type"] == "synchronized_stoppage" for issue in critical_issues):
            return "系统级异步任务死锁或阻塞，多个日志文件同步停止"
        elif any(issue["type"] == "data_flow_blockage" for issue in critical_issues):
            return "WebSocket数据流管理器阻塞，可能由API限速或连接池问题引起"
        elif self.diagnosis_results["api_rate_limit_analysis"].get("critical_finding"):
            return "OKX API限速频繁触发，影响整体系统数据流"
        else:
            return "多因素综合影响，需要逐步排查"
            
    def _get_immediate_actions(self) -> List[str]:
        """获取紧急行动建议"""
        actions = []
        
        critical_issues = [issue for issue in self.diagnosis_results["issues_found"] if issue["severity"] == "CRITICAL"]
        
        if critical_issues:
            actions.append("立即重启WebSocket管理器和数据流处理器")
            actions.append("临时降低OKX API调用频率50%")
            actions.append("启用WebSocket连接池监控和自动恢复")
            
        actions.append("检查并修复异步锁的正确释放")
        actions.append("实施更严格的API限速控制机制")
        
        return actions
        
    def _calculate_confidence_level(self) -> str:
        """计算诊断置信度"""
        total_evidence = 0
        strong_evidence = 0
        
        # API限速证据
        if self.diagnosis_results["api_rate_limit_analysis"].get("okx_rate_limit_count", 0) > 0:
            total_evidence += 1
            if self.diagnosis_results["api_rate_limit_analysis"].get("critical_finding"):
                strong_evidence += 1
                
        # 数据流证据  
        if self.diagnosis_results["data_flow_analysis"].get("blockage_detected"):
            total_evidence += 1
            strong_evidence += 1
            
        # 时序证据
        if self.diagnosis_results.get("timing_analysis"):
            total_evidence += 1
            
        if strong_evidence >= 2:
            return "HIGH"
        elif total_evidence >= 2:
            return "MEDIUM"
        else:
            return "LOW"
            
    def _print_diagnosis_summary(self):
        """打印诊断摘要"""
        summary = self.diagnosis_results["summary"]
        
        print(f"\n{'='*80}")
        print(f"🔍 WebSocket卡住问题诊断报告")
        print(f"{'='*80}")
        print(f"📊 总问题数: {summary['total_issues']}")
        print(f"🚨 关键问题: {summary['critical_issues']}")
        print(f"⚠️  高级问题: {summary['high_issues']}")
        print(f"🎯 置信度: {summary['confidence_level']}")
        print(f"\n🔎 根本原因分析:")
        print(f"   {summary['root_cause_analysis']}")
        
        print(f"\n🚨 紧急行动建议:")
        for i, action in enumerate(summary['immediate_actions'], 1):
            print(f"   {i}. {action}")
            
        print(f"\n💡 详细建议:")
        for i, rec in enumerate(self.diagnosis_results['recommendations'], 1):
            print(f"   {i}. {rec}")
            
        print(f"\n{'='*80}")


async def main():
    """主函数"""
    print("WebSocket卡住问题精确诊断启动...")
    
    try:
        diagnosis = WebSocketBlockageDiagnosis()
        await diagnosis.run_full_diagnosis()
        
        print("\n诊断完成！请查看生成的诊断报告。")
        
    except Exception as e:
        print(f"诊断失败: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())