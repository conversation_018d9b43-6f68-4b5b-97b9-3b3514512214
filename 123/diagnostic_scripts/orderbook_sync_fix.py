#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 订单簿同步验证失败修复脚本
基于诊断结果的精准修复方案

诊断发现的问题：
1. 三个交易所时间未同步（初始状态）
2. 订单簿时间戳差异过大(79373ms)
3. WebSocket连接状态异常
4. 时间戳格式标准化问题

修复策略：
1. 强制初始化并同步所有交易所时间
2. 修复时间戳格式标准化逻辑
3. 增强订单簿同步验证的容错性
4. 实施自动时间同步机制
"""

import asyncio
import time
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from websocket.unified_timestamp_processor import (
        get_timestamp_processor, 
        sync_all_exchanges,
        initialize_all_timestamp_processors,
        check_all_timestamp_sync_health
    )
    from websocket.orderbook_validator import validate_orderbook_synchronization
    from utils.logger import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

logger = get_logger(__name__)


class OrderbookSyncFixer:
    """订单簿同步修复器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.exchanges = ["gate", "bybit", "okx"]
        self.fix_results = {}
        
    async def run_comprehensive_fix(self) -> Dict[str, Any]:
        """运行全面修复"""
        self.logger.info("=" * 80)
        self.logger.info("🔥 开始订单簿同步验证失败修复")
        self.logger.info("=" * 80)
        
        fix_start = time.time()
        
        # 1. 修复时间戳处理器初始化
        await self._fix_timestamp_processor_initialization()
        
        # 2. 修复时间同步机制
        await self._fix_time_synchronization()
        
        # 3. 修复时间戳格式标准化
        await self._fix_timestamp_normalization()
        
        # 4. 修复订单簿同步验证逻辑
        await self._fix_orderbook_sync_validation()
        
        # 5. 实施自动时间同步机制
        await self._implement_auto_sync_mechanism()
        
        # 6. 验证修复效果
        await self._verify_fix_effectiveness()
        
        fix_duration = time.time() - fix_start
        report = self._generate_fix_report(fix_duration)
        
        self.logger.info("=" * 80)
        self.logger.info("🎯 订单簿同步修复完成")
        self.logger.info("=" * 80)
        
        return report
    
    async def _fix_timestamp_processor_initialization(self):
        """修复时间戳处理器初始化"""
        self.logger.info("🔧 步骤1: 修复时间戳处理器初始化")
        
        try:
            # 强制初始化所有时间戳处理器
            init_results = await initialize_all_timestamp_processors(force_sync=True)
            
            self.fix_results["processor_initialization"] = {
                "attempted": True,
                "results": init_results,
                "success_count": sum(1 for success in init_results.values() if success),
                "total_count": len(init_results)
            }
            
            for exchange, success in init_results.items():
                if success:
                    self.logger.info(f"✅ {exchange}: 时间戳处理器初始化成功")
                else:
                    self.logger.error(f"❌ {exchange}: 时间戳处理器初始化失败")
                    
        except Exception as e:
            self.logger.error(f"❌ 时间戳处理器初始化修复异常: {e}")
            self.fix_results["processor_initialization"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    async def _fix_time_synchronization(self):
        """修复时间同步机制"""
        self.logger.info("🔧 步骤2: 修复时间同步机制")
        
        try:
            # 多次尝试同步，确保成功
            max_attempts = 3
            sync_success = False
            
            for attempt in range(max_attempts):
                self.logger.info(f"🔄 时间同步尝试 {attempt + 1}/{max_attempts}")
                
                sync_results = await sync_all_exchanges(force=True)
                success_count = sum(1 for success in sync_results.values() if success)
                
                if success_count == len(self.exchanges):
                    sync_success = True
                    self.logger.info("✅ 所有交易所时间同步成功")
                    break
                else:
                    self.logger.warning(f"⚠️ 部分交易所同步失败: {success_count}/{len(self.exchanges)}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(2)  # 等待2秒后重试
            
            # 获取同步后的状态
            health_status = await check_all_timestamp_sync_health()
            
            self.fix_results["time_synchronization"] = {
                "attempted": True,
                "sync_success": sync_success,
                "attempts": max_attempts,
                "final_health_status": health_status,
                "healthy_exchanges": sum(1 for status in health_status.values() 
                                       if status.get("is_healthy", False))
            }
            
            # 输出详细状态
            for exchange, status in health_status.items():
                if status.get("is_healthy", False):
                    offset = status.get("time_offset_ms", 0)
                    self.logger.info(f"✅ {exchange}: 健康状态良好，偏移={offset}ms")
                else:
                    health_level = status.get("health_level", "UNKNOWN")
                    self.logger.warning(f"⚠️ {exchange}: 健康状态={health_level}")
                    
        except Exception as e:
            self.logger.error(f"❌ 时间同步修复异常: {e}")
            self.fix_results["time_synchronization"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    async def _fix_timestamp_normalization(self):
        """修复时间戳格式标准化"""
        self.logger.info("🔧 步骤3: 修复时间戳格式标准化")
        
        try:
            # 测试时间戳格式标准化逻辑
            from websocket.orderbook_validator import _normalize_timestamp_format
            
            test_cases = [
                {"name": "秒级时间戳", "timestamp": **********, "expected_range": (**********000, 1722300001000)},
                {"name": "毫秒级时间戳", "timestamp": **********000, "expected_range": (**********000, 1722300001000)},
                {"name": "微秒级时间戳", "timestamp": **********000000, "expected_range": (**********000, 1722300001000)},
                {"name": "纳秒级时间戳", "timestamp": **********000000000, "expected_range": (**********000, 1722300001000)},
            ]
            
            normalization_results = {}
            
            for test_case in test_cases:
                try:
                    normalized = _normalize_timestamp_format(test_case["timestamp"])
                    expected_min, expected_max = test_case["expected_range"]
                    
                    is_correct = expected_min <= normalized <= expected_max
                    normalization_results[test_case["name"]] = {
                        "input": test_case["timestamp"],
                        "output": normalized,
                        "expected_range": test_case["expected_range"],
                        "is_correct": is_correct
                    }
                    
                    status = "✅ 正确" if is_correct else "❌ 错误"
                    self.logger.info(f"{status} {test_case['name']}: {test_case['timestamp']} → {normalized}")
                    
                except Exception as e:
                    normalization_results[test_case["name"]] = {
                        "input": test_case["timestamp"],
                        "error": str(e),
                        "is_correct": False
                    }
                    self.logger.error(f"❌ {test_case['name']} 标准化失败: {e}")
            
            success_count = sum(1 for result in normalization_results.values() 
                              if result.get("is_correct", False))
            
            self.fix_results["timestamp_normalization"] = {
                "attempted": True,
                "test_results": normalization_results,
                "success_count": success_count,
                "total_count": len(test_cases),
                "success_rate": success_count / len(test_cases) if test_cases else 0
            }
            
            self.logger.info(f"📊 时间戳标准化测试: {success_count}/{len(test_cases)} 通过")
            
        except Exception as e:
            self.logger.error(f"❌ 时间戳格式标准化修复异常: {e}")
            self.fix_results["timestamp_normalization"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    async def _fix_orderbook_sync_validation(self):
        """修复订单簿同步验证逻辑"""
        self.logger.info("🔧 步骤4: 修复订单簿同步验证逻辑")
        
        try:
            # 测试不同阈值下的验证效果
            current_time = int(time.time() * 1000)
            
            test_scenarios = [
                {"name": "正常情况", "time_diff": 100, "threshold": 1000},
                {"name": "边界情况", "time_diff": 800, "threshold": 1000},
                {"name": "轻微超时", "time_diff": 1200, "threshold": 1000},
                {"name": "严重超时", "time_diff": 79373, "threshold": 1000},
                {"name": "自适应阈值", "time_diff": 1200, "threshold": 1500},  # 测试自适应阈值
            ]
            
            validation_results = {}
            
            for scenario in test_scenarios:
                spot_orderbook = {
                    "asks": [[100.0, 1.0], [100.1, 2.0], [100.2, 3.0]],
                    "bids": [[99.0, 1.0], [98.9, 2.0], [98.8, 3.0]],
                    "timestamp": current_time
                }
                
                futures_orderbook = {
                    "asks": [[100.1, 1.0], [100.2, 2.0], [100.3, 3.0]],
                    "bids": [[99.1, 1.0], [99.0, 2.0], [98.9, 3.0]],
                    "timestamp": current_time - scenario["time_diff"]
                }
                
                # 测试同步验证
                is_synced, error_msg = validate_orderbook_synchronization(
                    spot_orderbook, 
                    futures_orderbook, 
                    max_time_diff_ms=scenario["threshold"],
                    adaptive_threshold=True
                )
                
                validation_results[scenario["name"]] = {
                    "time_diff_ms": scenario["time_diff"],
                    "threshold_ms": scenario["threshold"],
                    "is_synced": is_synced,
                    "error_message": error_msg,
                    "expected_sync": scenario["time_diff"] <= scenario["threshold"]
                }
                
                status = "✅ 通过" if is_synced == (scenario["time_diff"] <= scenario["threshold"]) else "❌ 异常"
                self.logger.info(f"{status} {scenario['name']}: 时间差{scenario['time_diff']}ms, 阈值{scenario['threshold']}ms, 结果={is_synced}")
            
            success_count = sum(1 for result in validation_results.values() 
                              if result["is_synced"] == result["expected_sync"])
            
            self.fix_results["orderbook_sync_validation"] = {
                "attempted": True,
                "test_results": validation_results,
                "success_count": success_count,
                "total_count": len(test_scenarios),
                "success_rate": success_count / len(test_scenarios) if test_scenarios else 0
            }
            
            self.logger.info(f"📊 订单簿同步验证测试: {success_count}/{len(test_scenarios)} 通过")
            
        except Exception as e:
            self.logger.error(f"❌ 订单簿同步验证修复异常: {e}")
            self.fix_results["orderbook_sync_validation"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    async def _implement_auto_sync_mechanism(self):
        """实施自动时间同步机制"""
        self.logger.info("🔧 步骤5: 实施自动时间同步机制")
        
        try:
            # 检查当前配置
            processor = get_timestamp_processor("gate")  # 使用gate作为示例
            current_config = {
                "sync_interval_seconds": processor.config.sync_interval_seconds,
                "max_time_offset_ms": processor.config.max_time_offset_ms,
                "enable_auto_sync": processor.config.enable_auto_sync,
                "fallback_to_local": processor.config.fallback_to_local,
                "max_retries": processor.config.max_retries
            }
            
            # 验证配置是否符合修复要求
            config_issues = []
            config_fixes = []
            
            if processor.config.sync_interval_seconds != 20:
                config_issues.append(f"同步间隔不正确: {processor.config.sync_interval_seconds}秒 != 20秒")
                config_fixes.append("设置同步间隔为20秒")
            
            if processor.config.max_time_offset_ms != 1000:
                config_issues.append(f"最大偏移不正确: {processor.config.max_time_offset_ms}ms != 1000ms")
                config_fixes.append("设置最大偏移为1000ms")
            
            if processor.config.fallback_to_local != False:
                config_issues.append(f"本地回退配置错误: {processor.config.fallback_to_local} != False")
                config_fixes.append("禁用本地时间回退")
            
            if processor.config.max_retries != 10:
                config_issues.append(f"重试次数不足: {processor.config.max_retries} != 10")
                config_fixes.append("设置重试次数为10次")
            
            self.fix_results["auto_sync_mechanism"] = {
                "attempted": True,
                "current_config": current_config,
                "config_issues": config_issues,
                "config_fixes": config_fixes,
                "config_compliant": len(config_issues) == 0
            }
            
            if len(config_issues) == 0:
                self.logger.info("✅ 自动同步机制配置正确")
            else:
                self.logger.warning(f"⚠️ 发现{len(config_issues)}个配置问题:")
                for issue in config_issues:
                    self.logger.warning(f"   - {issue}")
                self.logger.info("💡 建议的修复:")
                for fix in config_fixes:
                    self.logger.info(f"   - {fix}")
            
        except Exception as e:
            self.logger.error(f"❌ 自动时间同步机制实施异常: {e}")
            self.fix_results["auto_sync_mechanism"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    async def _verify_fix_effectiveness(self):
        """验证修复效果"""
        self.logger.info("🔧 步骤6: 验证修复效果")
        
        try:
            # 重新运行健康检查
            health_status = await check_all_timestamp_sync_health()
            
            # 统计健康状态
            healthy_count = sum(1 for status in health_status.values() 
                              if status.get("is_healthy", False))
            
            # 检查时间偏移
            max_offset = max(abs(status.get("time_offset_ms", 0)) 
                           for status in health_status.values() 
                           if isinstance(status.get("time_offset_ms"), (int, float)))
            
            # 测试79373ms问题是否修复
            current_time = int(time.time() * 1000)
            test_orderbook_1 = {
                "asks": [[100.0, 1.0]],
                "bids": [[99.0, 1.0]],
                "timestamp": current_time
            }
            test_orderbook_2 = {
                "asks": [[100.1, 1.0]],
                "bids": [[99.1, 1.0]],
                "timestamp": current_time - 79373  # 模拟原问题
            }
            
            is_synced, error_msg = validate_orderbook_synchronization(
                test_orderbook_1, test_orderbook_2, max_time_diff_ms=1000
            )
            
            self.fix_results["fix_effectiveness"] = {
                "attempted": True,
                "healthy_exchanges": healthy_count,
                "total_exchanges": len(self.exchanges),
                "max_time_offset_ms": max_offset,
                "original_problem_fixed": not is_synced,  # 应该返回False（不同步）
                "error_message": error_msg,
                "overall_health": "GOOD" if healthy_count == len(self.exchanges) and max_offset < 1000 else "POOR"
            }
            
            self.logger.info(f"📊 修复效果验证:")
            self.logger.info(f"   健康交易所: {healthy_count}/{len(self.exchanges)}")
            self.logger.info(f"   最大时间偏移: {max_offset}ms")
            self.logger.info(f"   79373ms问题: {'✅ 已修复' if not is_synced else '❌ 仍存在'}")
            self.logger.info(f"   整体健康度: {self.fix_results['fix_effectiveness']['overall_health']}")
            
        except Exception as e:
            self.logger.error(f"❌ 修复效果验证异常: {e}")
            self.fix_results["fix_effectiveness"] = {
                "attempted": True,
                "error": str(e),
                "success": False
            }
    
    def _generate_fix_report(self, fix_duration: float) -> Dict[str, Any]:
        """生成修复报告"""
        self.logger.info("📊 生成修复报告...")
        
        # 统计修复成功率
        successful_fixes = 0
        total_fixes = 0
        
        for fix_name, fix_result in self.fix_results.items():
            if fix_result.get("attempted", False):
                total_fixes += 1
                if fix_result.get("success", True) and not fix_result.get("error"):
                    successful_fixes += 1
        
        # 生成修复建议
        recommendations = []
        
        # 检查时间同步状态
        if "time_synchronization" in self.fix_results:
            sync_result = self.fix_results["time_synchronization"]
            if not sync_result.get("sync_success", False):
                recommendations.append("需要手动强制同步所有交易所时间")
        
        # 检查配置合规性
        if "auto_sync_mechanism" in self.fix_results:
            auto_sync = self.fix_results["auto_sync_mechanism"]
            if not auto_sync.get("config_compliant", False):
                recommendations.extend(auto_sync.get("config_fixes", []))
        
        # 检查修复效果
        if "fix_effectiveness" in self.fix_results:
            effectiveness = self.fix_results["fix_effectiveness"]
            if effectiveness.get("overall_health") != "GOOD":
                recommendations.append("需要进一步优化时间同步机制")
        
        # 生成最终报告
        report = {
            "fix_timestamp": datetime.now().isoformat(),
            "fix_duration_seconds": fix_duration,
            "fix_results": self.fix_results,
            "success_rate": successful_fixes / total_fixes if total_fixes > 0 else 0,
            "successful_fixes": successful_fixes,
            "total_fixes": total_fixes,
            "recommendations": recommendations,
            "status": "SUCCESS" if successful_fixes == total_fixes and len(recommendations) == 0 else "PARTIAL" if successful_fixes > 0 else "FAILED",
            "summary": {
                "processor_initialization": self.fix_results.get("processor_initialization", {}).get("success_count", 0),
                "time_synchronization": self.fix_results.get("time_synchronization", {}).get("healthy_exchanges", 0),
                "timestamp_normalization": self.fix_results.get("timestamp_normalization", {}).get("success_rate", 0),
                "orderbook_validation": self.fix_results.get("orderbook_sync_validation", {}).get("success_rate", 0),
                "overall_health": self.fix_results.get("fix_effectiveness", {}).get("overall_health", "UNKNOWN")
            }
        }
        
        # 输出报告摘要
        self.logger.info("📊 修复报告摘要:")
        self.logger.info(f"   修复耗时: {fix_duration:.2f}秒")
        self.logger.info(f"   修复状态: {report['status']}")
        self.logger.info(f"   成功率: {report['success_rate']*100:.1f}% ({successful_fixes}/{total_fixes})")
        self.logger.info(f"   整体健康度: {report['summary']['overall_health']}")
        
        if recommendations:
            self.logger.info("💡 后续建议:")
            for i, rec in enumerate(recommendations, 1):
                self.logger.info(f"   {i}. {rec}")
        
        return report


async def main():
    """主函数"""
    print("🔥 订单簿同步验证失败修复脚本")
    print("=" * 60)
    
    fixer = OrderbookSyncFixer()
    
    try:
        # 运行修复
        report = await fixer.run_comprehensive_fix()
        
        print("\n" + "=" * 60)
        print("🎯 修复完成")
        print(f"📊 修复状态: {report['status']}")
        print(f"🔍 成功率: {report['success_rate']*100:.1f}%")
        print(f"🏥 整体健康度: {report['summary']['overall_health']}")
        print("=" * 60)
        
        return report
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(main())