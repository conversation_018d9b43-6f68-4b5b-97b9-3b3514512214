#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 异步调用缺陷和精度错误诊断脚本
根据深度代码审查结果，精确定位系统中的异步调用和精度问题

发现的关键问题：
1. execution_engine.py 中 asyncio.gather 异常处理不充分
2. WebSocket 异步调用的同步包装问题
3. unified_order_spread_calculator.py 中 float/Decimal 混用
4. round() 函数引入精度误差
5. numpy float64 与 Decimal 不一致
"""

import asyncio
import sys
import os
import json
import traceback
import time
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Any, Tuple, Optional
# import numpy as np  # 移除numpy依赖

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class AsyncPrecisionDiagnostic:
    """异步调用缺陷和精度错误诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "async_issues": [],
            "precision_issues": [],
            "critical_errors": [],
            "warnings": [],
            "test_summary": {
                "total_tests": 0,
                "passed": 0,
                "failed": 0,
                "critical": 0
            }
        }
        
    def log_issue(self, category: str, severity: str, description: str, details: Dict[str, Any] = None):
        """记录问题"""
        issue = {
            "severity": severity,
            "description": description,
            "details": details or {},
            "timestamp": time.time()
        }
        
        if category == "async":
            self.results["async_issues"].append(issue)
        elif category == "precision":
            self.results["precision_issues"].append(issue)
            
        if severity == "CRITICAL":
            self.results["critical_errors"].append(issue)
            self.results["test_summary"]["critical"] += 1
        elif severity == "WARNING":
            self.results["warnings"].append(issue)
            
        self.results["test_summary"]["total_tests"] += 1
        if severity in ["CRITICAL", "ERROR"]:
            self.results["test_summary"]["failed"] += 1
        else:
            self.results["test_summary"]["passed"] += 1

    async def diagnose_execution_engine_async_issues(self):
        """诊断 ExecutionEngine 中的异步调用问题"""
        print("诊断 ExecutionEngine 异步调用问题...")
        
        try:
            from core.execution_engine import ExecutionEngine
            engine = ExecutionEngine()
            
            # 测试1: 检查 asyncio.gather 异常处理
            print("  1. 检查 asyncio.gather 异常处理模式...")
            
            # 模拟 asyncio.gather 异常处理
            async def failing_task():
                await asyncio.sleep(0.001)
                raise ValueError("模拟异步任务失败")
                
            async def success_task():
                await asyncio.sleep(0.001)
                return "success"
            
            # 测试是否正确处理部分失败
            try:
                results = await asyncio.gather(
                    success_task(),
                    failing_task(),
                    success_task(),
                    return_exceptions=True
                )
                
                # 检查结果处理逻辑
                exception_count = sum(1 for r in results if isinstance(r, Exception))
                if exception_count > 0:
                    self.log_issue(
                        "async", "WARNING",
                        "asyncio.gather 中存在异常，需要验证异常处理逻辑",
                        {
                            "total_tasks": len(results),
                            "exceptions": exception_count,
                            "exception_types": [type(r).__name__ for r in results if isinstance(r, Exception)]
                        }
                    )
                    
            except Exception as e:
                self.log_issue(
                    "async", "CRITICAL",
                    "asyncio.gather 异常处理失败",
                    {"error": str(e), "traceback": traceback.format_exc()}
                )
            
            # 测试2: 检查锁机制的异常释放
            print("  2. 检查执行锁异常释放机制...")
            if hasattr(engine, 'execution_lock'):
                # 模拟锁异常情况
                try:
                    async with engine.execution_lock:
                        # 模拟异常
                        raise RuntimeError("模拟执行异常")
                except RuntimeError:
                    # 检查锁是否正确释放
                    if engine.execution_lock.locked():
                        self.log_issue(
                            "async", "CRITICAL", 
                            "执行锁在异常后未正确释放，可能导致死锁",
                            {"lock_status": "locked"}
                        )
                    else:
                        print("    执行锁异常释放机制正常")
            
            # 测试3: 检查 WebSocket 异步调用
            print("  3. 检查 WebSocket 异步调用模式...")
            if hasattr(engine, '_get_websocket_orderbook_async'):
                # 检查是否是真正的异步方法
                import inspect
                if inspect.iscoroutinefunction(engine._get_websocket_orderbook_async):
                    print("    WebSocket 异步方法签名正确")
                else:
                    self.log_issue(
                        "async", "ERROR",
                        "WebSocket _get_websocket_orderbook_async 不是真正的异步方法",
                        {"method_type": str(type(engine._get_websocket_orderbook_async))}
                    )
                    
        except Exception as e:
            self.log_issue(
                "async", "CRITICAL",
                "ExecutionEngine 异步诊断失败",
                {"error": str(e), "traceback": traceback.format_exc()}
            )

    async def diagnose_precision_issues(self):
        """诊断精度计算问题"""
        print("🔍 诊断精度计算问题...")
        
        # 测试1: float/Decimal 混用问题
        print("  1. 测试 float/Decimal 混用精度损失...")
        
        test_price1 = 50234.12345678
        test_price2 = 50234.87654321
        
        # 使用 float 计算
        float_spread = (test_price2 - test_price1) / test_price1
        
        # 使用 Decimal 计算
        decimal_price1 = Decimal(str(test_price1))
        decimal_price2 = Decimal(str(test_price2))
        decimal_spread = float((decimal_price2 - decimal_price1) / decimal_price1)
        
        # 检查精度差异
        precision_diff = abs(float_spread - decimal_spread)
        if precision_diff > 1e-12:
            self.log_issue(
                "precision", "WARNING",
                "float 和 Decimal 计算存在精度差异",
                {
                    "float_result": float_spread,
                    "decimal_result": decimal_spread,
                    "precision_diff": precision_diff,
                    "test_prices": [test_price1, test_price2]
                }
            )
        else:
            print("    ✅ float/Decimal 精度差异在可接受范围内")
        
        # 测试2: round() 函数精度问题
        print("  2. 测试 round() 函数精度损失...")
        
        original_value = 50234.123456789012345
        rounded_value = round(original_value, 8)
        
        # 检查是否有精度损失
        decimal_original = Decimal(str(original_value))
        decimal_rounded = Decimal(str(rounded_value))
        
        precision_loss = abs(float(decimal_original - decimal_rounded))
        if precision_loss > 1e-8:
            self.log_issue(
                "precision", "WARNING",
                "round() 函数造成精度损失",
                {
                    "original": original_value,
                    "rounded": rounded_value,
                    "precision_loss": precision_loss
                }
            )
        
        # 测试3: 原生Python与Decimal一致性（替代numpy测试）
        print("  3. 测试原生Python计算与Decimal一致性...")
        
        test_values = [123.456789, 789.123456, 456.789123]
        
        for i, value in enumerate(test_values):
            python_value = float(value)  # 原生Python处理
            decimal_value = float(Decimal(str(value)))
            
            if abs(python_value - decimal_value) > 1e-12:
                self.log_issue(
                    "precision", "WARNING",
                    "原生Python与Decimal存在精度不一致",
                    {
                        "original": value,
                        "python_result": python_value,
                        "decimal_result": decimal_value,
                        "difference": abs(python_value - decimal_value)
                    }
                )

    async def diagnose_unified_order_spread_calculator(self):
        """诊断统一Order差价计算器的精度问题"""
        print("🔍 诊断统一Order差价计算器...")
        
        try:
            from core.unified_order_spread_calculator import UnifiedOrderSpreadCalculator
            calculator = UnifiedOrderSpreadCalculator()
            
            # 测试模拟数据
            spot_orderbook = {
                'asks': [[50234.12, 0.1], [50234.15, 0.2]],
                'bids': [[50233.98, 0.1], [50233.95, 0.2]],
                'timestamp': int(time.time() * 1000)
            }
            
            futures_orderbook = {
                'asks': [[50256.34, 0.1], [50256.37, 0.2]],
                'bids': [[50256.21, 0.1], [50256.18, 0.2]],
                'timestamp': int(time.time() * 1000)
            }
            
            # 测试差价计算精度
            result = calculator.calculate_order_based_spread(
                spot_orderbook, futures_orderbook, 100.0, "opening"
            )
            
            if result:
                # 验证精度处理
                spread_str = f"{result.executable_spread:.12f}"
                if len(spread_str.split('.')[-1]) < 8:
                    self.log_issue(
                        "precision", "WARNING",
                        "Order差价计算器可能存在精度截断",
                        {
                            "calculated_spread": result.executable_spread,
                            "precision_digits": len(spread_str.split('.')[-1])
                        }
                    )
                else:
                    print("    ✅ Order差价计算器精度正常")
            else:
                self.log_issue(
                    "precision", "ERROR",
                    "Order差价计算器返回None，可能存在计算错误",
                    {"test_data": {"spot": spot_orderbook, "futures": futures_orderbook}}
                )
                
        except Exception as e:
            self.log_issue(
                "precision", "CRITICAL",
                "Order差价计算器诊断失败",
                {"error": str(e), "traceback": traceback.format_exc()}
            )

    async def test_real_world_scenarios(self):
        """测试真实场景下的异步和精度问题"""
        print("🔍 测试真实场景...")
        
        # 场景1: 高并发异步任务
        print("  1. 测试高并发异步任务处理...")
        
        async def simulated_api_call(delay: float):
            await asyncio.sleep(delay)
            if delay > 0.1:  # 模拟部分失败
                raise asyncio.TimeoutError(f"模拟超时: {delay}")
            return f"success_{delay}"
        
        tasks = [simulated_api_call(i * 0.05) for i in range(10)]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = len(results) - success_count
            
            if error_count > 0:
                self.log_issue(
                    "async", "INFO",
                    f"高并发测试: {success_count}成功, {error_count}失败",
                    {
                        "success_rate": success_count / len(results),
                        "error_types": [type(r).__name__ for r in results if isinstance(r, Exception)]
                    }
                )
                
        except Exception as e:
            self.log_issue(
                "async", "ERROR",
                "高并发异步测试失败",
                {"error": str(e)}
            )
        
        # 场景2: 精度累积误差测试
        print("  2. 测试精度累积误差...")
        
        initial_value = Decimal('50234.123456789')
        accumulated_error = Decimal('0')
        
        # 模拟多次计算
        for i in range(1000):
            # 模拟 float 转换损失
            float_value = float(initial_value)
            back_to_decimal = Decimal(str(float_value))
            error = abs(initial_value - back_to_decimal)
            accumulated_error += error
            
        if accumulated_error > Decimal('1e-6'):
            self.log_issue(
                "precision", "WARNING",
                "多次 float/Decimal 转换产生累积误差",
                {
                    "iterations": 1000,
                    "accumulated_error": float(accumulated_error),
                    "average_error": float(accumulated_error / 1000)
                }
            )

    def generate_diagnosis_report(self) -> str:
        """生成诊断报告"""
        report = {
            "diagnosis_summary": {
                "timestamp": self.results["timestamp"],
                "total_issues": len(self.results["async_issues"]) + len(self.results["precision_issues"]),
                "critical_issues": len(self.results["critical_errors"]),
                "async_issues": len(self.results["async_issues"]),
                "precision_issues": len(self.results["precision_issues"]),
                "test_summary": self.results["test_summary"]
            },
            "detailed_results": self.results
        }
        
        return json.dumps(report, indent=2, ensure_ascii=False)

    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("开始异步调用缺陷和精度错误诊断...")
        print("=" * 80)
        
        try:
            await self.diagnose_execution_engine_async_issues()
            await self.diagnose_precision_issues()
            await self.diagnose_unified_order_spread_calculator()
            await self.test_real_world_scenarios()
            
            print("=" * 80)
            print("诊断完成")
            
            # 生成报告
            report = self.generate_diagnosis_report()
            
            # 保存报告
            report_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "diagnostic_results", 
                f"async_precision_diagnosis_{int(time.time())}.json"
            )
            
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"诊断报告已保存: {report_path}")
            
            # 打印摘要
            summary = self.results["test_summary"]
            print(f"\n诊断摘要:")
            print(f"  总测试数: {summary['total_tests']}")
            print(f"  通过: {summary['passed']}")
            print(f"  失败: {summary['failed']}")
            print(f"  严重错误: {summary['critical']}")
            print(f"  异步问题: {len(self.results['async_issues'])}")
            print(f"  精度问题: {len(self.results['precision_issues'])}")
            
            return report_path
            
        except Exception as e:
            print(f"诊断过程异常: {e}")
            traceback.print_exc()
            return None

async def main():
    """主函数"""
    diagnostic = AsyncPrecisionDiagnostic()
    report_path = await diagnostic.run_full_diagnosis()
    
    if report_path:
        print(f"\n下一步: 基于诊断结果进行针对性修复")
        print(f"详细报告: {report_path}")
    else:
        print("\n诊断失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())