# UnifiedPortfolioInput

Input for the portfolio margin calculator.
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**spot_balances** | [**list[MockSpotBalance]**](MockSpotBalance.md) | Spot | [optional] 
**spot_orders** | [**list[MockSpotOrder]**](MockSpotOrder.md) | Spot orders | [optional] 
**futures_positions** | [**list[MockFuturesPosition]**](MockFuturesPosition.md) | Futures positions | [optional] 
**futures_orders** | [**list[MockFuturesOrder]**](MockFuturesOrder.md) | Futures order | [optional] 
**options_positions** | [**list[MockOptionsPosition]**](MockOptionsPosition.md) | Options positions | [optional] 
**options_orders** | [**list[MockOptionsOrder]**](MockOptionsOrder.md) | Option orders | [optional] 
**spot_hedge** | **bool** | Whether to enable spot hedging. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


