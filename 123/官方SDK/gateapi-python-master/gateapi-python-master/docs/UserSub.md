# UserSub

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**uid** | **int** | User ID | [optional] 
**belong** | **str** | The system to which the user belongs (partner referral). If empty, it means not belonging to any system. | [optional] 
**type** | **int** | Type (0-not in the system 1-direct subordinate agent 2-indirect subordinate agent 3-direct direct customer 4-indirect direct customer 5-ordinary user) | [optional] 
**ref_uid** | **int** | Inviter user ID | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


