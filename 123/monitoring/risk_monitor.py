"""
风险监控模块
实时监控套利风险，包括价差风险、资金风险、技术风险等
"""

# 修复导入路径
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import time
import json
import os
import statistics
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading

from config.debug_config import DEBUG
from utils.logger import get_logger

logger = get_logger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RiskType(Enum):
    SPREAD_RISK = "spread_risk"           # 价差风险
    LIQUIDITY_RISK = "liquidity_risk"     # 流动性风险
    FUNDING_RISK = "funding_risk"         # 资金风险
    TECHNICAL_RISK = "technical_risk"     # 技术风险
    COUNTERPARTY_RISK = "counterparty_risk"  # 交易对手风险
    MARKET_RISK = "market_risk"           # 市场风险
    OPERATIONAL_RISK = "operational_risk" # 操作风险

@dataclass
class RiskAlert:
    """风险告警"""
    id: str
    risk_type: RiskType
    risk_level: RiskLevel
    title: str
    description: str
    data: Dict
    timestamp: float
    is_resolved: bool = False
    resolved_time: Optional[float] = None

    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "risk_type": self.risk_type.value,
            "risk_level": self.risk_level.value,
            "title": self.title,
            "description": self.description,
            "data": self.data,
            "timestamp": self.timestamp,
            "is_resolved": self.is_resolved,
            "resolved_time": self.resolved_time
        }

@dataclass
class RiskMetrics:
    """风险指标"""
    # 价差风险
    current_spread: float = 0.0
    spread_volatility: float = 0.0
    max_spread_deviation: float = 0.0

    # 流动性风险
    spot_depth_ratio: float = 0.0      # 现货深度比例
    futures_depth_ratio: float = 0.0   # 期货深度比例
    min_depth_threshold: float = 0.0

    # 资金风险
    total_balance: float = 0.0
    available_balance: float = 0.0
    margin_ratio: float = 0.0
    leverage_ratio: float = 0.0

    # 技术风险
    api_latency_ms: float = 0.0
    websocket_delays: float = 0.0
    connection_failures: int = 0
    order_failures: int = 0

    # 市场风险
    price_volatility: float = 0.0
    volume_ratio: float = 0.0
    funding_rate: float = 0.0

    # 操作风险
    hedge_failure_rate: float = 0.0
    position_imbalance_ratio: float = 0.0
    profit_loss_ratio: float = 0.0

    timestamp: float = field(default_factory=time.time)

    def calculate_overall_risk_score(self) -> float:
        """计算综合风险评分 (0-100)"""
        risk_score = 0.0

        # 价差风险 (25%)
        spread_risk = min(self.spread_volatility * 1000, 25)  # 波动率转换为风险分
        risk_score += spread_risk * 0.25

        # 流动性风险 (20%)
        liquidity_risk = max(0, 20 - min(self.spot_depth_ratio, self.futures_depth_ratio) * 20)
        risk_score += liquidity_risk * 0.20

        # 资金风险 (20%)
        funding_risk = max(0, 20 - self.margin_ratio * 20) if self.margin_ratio > 0 else 20
        risk_score += funding_risk * 0.20

        # 技术风险 (15%)
        tech_risk = min(self.api_latency_ms / 10, 15) + self.connection_failures * 2
        risk_score += min(tech_risk, 15) * 0.15

        # 操作风险 (20%)
        ops_risk = self.hedge_failure_rate * 20 + self.position_imbalance_ratio * 10
        risk_score += min(ops_risk, 20) * 0.20

        return min(risk_score, 100.0)

    def get_risk_level(self) -> RiskLevel:
        """获取风险等级"""
        score = self.calculate_overall_risk_score()

        if score >= 80:
            return RiskLevel.CRITICAL
        elif score >= 60:
            return RiskLevel.HIGH
        elif score >= 40:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

class RiskMonitor:
    """风险监控器"""

    def __init__(self, position_monitor=None, order_manager=None, exchanges: Dict[str, Any] = None):
        self.position_monitor = position_monitor
        self.order_manager = order_manager
        self.exchanges = exchanges or {}
        self.logger = get_logger("RiskMonitor")

        # 风险配置
        self.risk_config = self._load_risk_config()

        # 风险数据存储
        self.current_metrics = RiskMetrics()
        self.risk_alerts: Dict[str, RiskAlert] = {}
        self.metrics_history: List[RiskMetrics] = []

        # 监控状态
        self.is_running = False
        self.monitor_thread = None
        self.check_interval = 10  # 10秒检查一次

        # 历史数据
        self.spread_history: List[Tuple[float, float]] = []  # (timestamp, spread)
        self.latency_history: List[Tuple[float, float]] = []
        self.max_history_length = 1000

        # 回调函数
        self.risk_alert_callbacks: List[Callable] = []
        self.critical_risk_callbacks: List[Callable] = []

        # 统计信息
        self.total_checks = 0
        self.alert_count = 0
        self.critical_alert_count = 0

        if DEBUG:
            self.logger.info("RiskMonitor initialized")

    async def initialize(self):
        """初始化风险监控器"""
        try:
            self.logger.info("初始化风险监控器...")

            # 执行初始风险检查
            await self._initial_risk_check()

            self.logger.info("✅ 风险监控器初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 风险监控器初始化失败: {e}")
            raise

    async def _initial_risk_check(self):
        """执行初始风险检查"""
        try:
            # 检查资金风险
            funding_alerts = await self.check_funding_risk()
            if funding_alerts:
                self.logger.warning(f"发现 {len(funding_alerts)} 个资金风险告警")

            # 更新当前风险指标
            await self._update_current_metrics()

        except Exception as e:
            self.logger.error(f"初始风险检查失败: {e}")

    async def _update_current_metrics(self):
        """更新当前风险指标"""
        try:
            # 计算综合风险评分
            risk_score = self.current_metrics.calculate_overall_risk_score()
            risk_level = self.current_metrics.get_risk_level()

            self.logger.info(f"当前风险评分: {risk_score:.1f}, 风险等级: {risk_level.value}")

        except Exception as e:
            self.logger.error(f"更新风险指标失败: {e}")

    def _load_risk_config(self) -> Dict:
        """加载风险配置"""
        try:
            # 从环境变量读取风险配置
            config = {
                # 价差风险阈值
                "max_spread_deviation": float(os.getenv("MAX_SPREAD_DEVIATION", "0.005")),  # 0.5%
                "spread_volatility_threshold": float(os.getenv("SPREAD_VOLATILITY_THRESHOLD", "0.001")),  # 0.1%

                # 流动性风险阈值
                "min_depth_ratio": float(os.getenv("MIN_DEPTH_RATIO", "0.1")),  # 10%
                "min_order_book_depth": float(os.getenv("MIN_ORDER_BOOK_DEPTH", "50000")),  # $50k

                # 🔥 修复：资金风险阈值统一使用3倍杠杆
                "min_margin_ratio": float(os.getenv("MIN_MARGIN_RATIO", "0.3")),  # 30%
                "max_leverage_ratio": float(os.getenv("MAX_LEVERAGE_RATIO", "3.0")),  # 3x
                "min_available_balance": float(os.getenv("MIN_AVAILABLE_BALANCE", "1000")),  # $1000

                # 技术风险阈值
                "max_api_latency_ms": float(os.getenv("MAX_API_LATENCY_MS", "500")),  # 500ms
                "max_websocket_delay_ms": float(os.getenv("MAX_WEBSOCKET_DELAY_MS", "100")),  # 100ms
                "max_connection_failures": int(os.getenv("MAX_CONNECTION_FAILURES", "3")),

                # 操作风险阈值
                "max_hedge_failure_rate": float(os.getenv("MAX_HEDGE_FAILURE_RATE", "0.05")),  # 5%
                "max_position_imbalance_ratio": float(os.getenv("MAX_POSITION_IMBALANCE_RATIO", "0.01")),  # 1%
            }

            if DEBUG:
                self.logger.debug(f"Risk config loaded: {config}")

            return config

        except Exception as e:
            self.logger.error(f"Error loading risk config: {e}")
            return {}

    def add_risk_alert_callback(self, callback: Callable):
        """添加风险告警回调"""
        self.risk_alert_callbacks.append(callback)
        if DEBUG:
            self.logger.debug("Risk alert callback added")

    def add_critical_risk_callback(self, callback: Callable):
        """添加严重风险回调"""
        self.critical_risk_callbacks.append(callback)
        if DEBUG:
            self.logger.debug("Critical risk callback added")

    async def check_spread_risk(self, symbol_pair: str, spot_price: float,
                              futures_price: float) -> List[RiskAlert]:
        """
        检查价差风险

        Args:
            symbol_pair: 交易对
            spot_price: 现货价格
            futures_price: 期货价格

        Returns:
            List[RiskAlert]: 风险告警列表
        """
        alerts = []
        current_time = time.time()

        try:
            # 🔥 修复：使用OpportunityScanner统一Order差价计算进行精确风险监控
            if spot_price > 0 and futures_price > 0:
                try:
                    from core.arbitrage_engine import get_arbitrage_engine
                    engine = get_arbitrage_engine()
                    scanner = engine.opportunity_scanner if engine else None

                    if scanner:
                        # 尝试使用Order差价计算进行精确风险监控
                        try:
                            # 🔥 修复：从ArbitrageEngine获取实际监控参数，而不是硬编码默认值
                            current_session = engine.current_session if engine else None

                            if current_session and current_session.opportunity:
                                # 使用当前交易会话的实际参数
                                symbol = current_session.opportunity.symbol
                                spot_exchange = current_session.opportunity.buy_exchange if current_session.opportunity.buy_market == "spot" else current_session.opportunity.sell_exchange
                                futures_exchange = current_session.opportunity.sell_exchange if current_session.opportunity.sell_market == "futures" else current_session.opportunity.buy_exchange

                                # 使用实际交易金额而不是固定100.0
                                target_amount = current_session.opportunity.base_amount * spot_price

                                # 根据交易方向确定execution_context
                                execution_context = "opening" if current_session.opportunity.buy_market == "spot" else "closing"
                            else:
                                # 🔥 降级处理：从symbol_pair参数推断，避免硬编码
                                symbol = symbol_pair.replace('USDT', '-USDT') if 'USDT' in symbol_pair else symbol_pair
                                # 使用传入的价格信息推断交易所（从scanner的market_data中查找）
                                spot_exchange = None
                                futures_exchange = None

                                # 从scanner的market_data中查找匹配的交易所
                                for key in scanner.market_data.keys():
                                    if symbol in key:
                                        if 'spot' in key:
                                            spot_exchange = key.split('_')[0]
                                        elif 'futures' in key:
                                            futures_exchange = key.split('_')[0]

                                # 🔥 修复：如果找不到交易所信息，直接返回空告警，不降级
                                if not spot_exchange or not futures_exchange:
                                    self.logger.warning(f"⚠️ 无法获取交易所信息，跳过风险检查")
                                    return alerts

                                target_amount = 100.0  # 使用固定金额进行风险监控
                                execution_context = "opening"  # 风险监控使用开仓上下文

                            # 构建市场数据键
                            spot_key = f"{spot_exchange}_spot_{symbol}"
                            futures_key = f"{futures_exchange}_futures_{symbol}"

                            # 获取订单簿数据
                            spot_data = scanner.market_data.get(spot_key)
                            futures_data = scanner.market_data.get(futures_key)

                            if spot_data and futures_data:
                                spot_orderbook = spot_data.orderbook if hasattr(spot_data, 'orderbook') and spot_data.orderbook else {}
                                futures_orderbook = futures_data.orderbook if hasattr(futures_data, 'orderbook') and futures_data.orderbook else {}

                                # 🔥 修复：使用统一Order差价计算器
                                from core.unified_order_spread_calculator import get_order_spread_calculator
                                calculator = get_order_spread_calculator()
                                order_result = calculator.calculate_order_based_spread_with_snapshot(
                                    spot_orderbook, futures_orderbook, target_amount, execution_context, force_snapshot=True
                                )
                                spread = order_result.executable_spread if order_result else None

                                if spread is not None:
                                    self.logger.debug(f"✅ Order精确风险监控价差: {spread*100:.3f}%")
                                else:
                                    # 🔥 修复：不降级，直接返回空告警
                                    self.logger.warning(f"⚠️ Order差价计算返回None，跳过风险检查")
                                    return alerts
                            else:
                                # 🔥 修复：不降级，直接返回空告警
                                self.logger.warning(f"⚠️ 订单簿数据不可用，跳过风险检查")
                                return alerts

                        except Exception as order_calc_error:
                            self.logger.warning(f"Order差价计算失败: {order_calc_error}，跳过风险检查")
                            # 🔥 修复：不降级，直接返回空告警
                            return alerts
                    else:
                        self.logger.error("OpportunityScanner不可用，跳过价差计算")
                        return alerts
                except Exception as e:
                    self.logger.error(f"获取OpportunityScanner失败: {e}")
                    return alerts
                self.current_metrics.current_spread = spread

                # 记录价差历史
                self.spread_history.append((current_time, spread))
                if len(self.spread_history) > self.max_history_length:
                    self.spread_history.pop(0)

                # 计算价差波动率
                if len(self.spread_history) >= 10:
                    recent_spreads = [s[1] for s in self.spread_history[-10:]]
                    if len(recent_spreads) > 1:
                        self.current_metrics.spread_volatility = statistics.stdev(recent_spreads)
                    else:
                        self.current_metrics.spread_volatility = 0.0

                # 检查价差偏离阈值
                max_deviation = self.risk_config.get("max_spread_deviation", 0.005)
                if abs(spread) > max_deviation:
                    alert = RiskAlert(
                        id=f"spread_deviation_{symbol_pair}_{int(current_time)}",
                        risk_type=RiskType.SPREAD_RISK,
                        risk_level=RiskLevel.HIGH if abs(spread) > max_deviation * 2 else RiskLevel.MEDIUM,
                        title=f"价差偏离告警 - {symbol_pair}",
                        description=f"当前价差 {spread:.4f} 超过阈值 {max_deviation:.4f}",
                        data={
                            "symbol_pair": symbol_pair,
                            "current_spread": spread,
                            "threshold": max_deviation,
                            "spot_price": spot_price,
                            "futures_price": futures_price
                        },
                        timestamp=current_time
                    )
                    alerts.append(alert)

                # 检查价差波动率
                volatility_threshold = self.risk_config.get("spread_volatility_threshold", 0.001)
                if self.current_metrics.spread_volatility > volatility_threshold:
                    alert = RiskAlert(
                        id=f"spread_volatility_{symbol_pair}_{int(current_time)}",
                        risk_type=RiskType.SPREAD_RISK,
                        risk_level=RiskLevel.MEDIUM,
                        title=f"价差波动过大 - {symbol_pair}",
                        description=f"价差波动率 {self.current_metrics.spread_volatility:.6f} 超过阈值 {volatility_threshold:.6f}",
                        data={
                            "symbol_pair": symbol_pair,
                            "volatility": self.current_metrics.spread_volatility,
                            "threshold": volatility_threshold
                        },
                        timestamp=current_time
                    )
                    alerts.append(alert)

        except Exception as e:
            self.logger.error(f"Error checking spread risk: {e}")

        return alerts

    async def check_liquidity_risk(self, symbol: str, exchange_name: str,
                                 orderbook: Dict) -> List[RiskAlert]:
        """
        检查流动性风险

        Args:
            symbol: 交易对
            exchange_name: 交易所名称
            orderbook: 订单簿数据

        Returns:
            List[RiskAlert]: 风险告警列表
        """
        alerts = []
        current_time = time.time()

        try:
            # 分析订单簿深度
            asks = orderbook.get('asks', [])
            bids = orderbook.get('bids', [])

            if not asks or not bids:
                alert = RiskAlert(
                    id=f"no_liquidity_{exchange_name}_{symbol}_{int(current_time)}",
                    risk_type=RiskType.LIQUIDITY_RISK,
                    risk_level=RiskLevel.CRITICAL,
                    title=f"流动性缺失 - {exchange_name}:{symbol}",
                    description="订单簿数据缺失或为空",
                    data={
                        "exchange": exchange_name,
                        "symbol": symbol,
                        "asks_count": len(asks),
                        "bids_count": len(bids)
                    },
                    timestamp=current_time
                )
                alerts.append(alert)
                return alerts

            # 🔥 修复：使用统一深度分析器替代重复的深度计算
            from core.unified_depth_analyzer import get_depth_analyzer
            depth_analyzer = get_depth_analyzer()

            # 使用统一深度分析器计算深度
            orderbook = {'asks': asks, 'bids': bids}
            ask_result = depth_analyzer.analyze_orderbook_depth(orderbook, 999999, "buy", 0, exchange_name, symbol)
            bid_result = depth_analyzer.analyze_orderbook_depth(orderbook, 999999, "sell", 0, exchange_name, symbol)

            ask_depth = ask_result.total_volume * (asks[0][0] if asks else 0)  # 价值
            bid_depth = bid_result.total_volume * (bids[0][0] if bids else 0)  # 价值
            total_depth = ask_depth + bid_depth

            # 更新指标
            if exchange_name.lower().find('spot') != -1 or 'spot' in symbol.lower():
                self.current_metrics.spot_depth_ratio = min(ask_depth, bid_depth) / max(ask_depth, bid_depth) if max(ask_depth, bid_depth) > 0 else 0
            else:
                self.current_metrics.futures_depth_ratio = min(ask_depth, bid_depth) / max(ask_depth, bid_depth) if max(ask_depth, bid_depth) > 0 else 0

            # 检查最小深度要求
            min_depth = self.risk_config.get("min_order_book_depth", 50000)
            if total_depth < min_depth:
                alert = RiskAlert(
                    id=f"low_liquidity_{exchange_name}_{symbol}_{int(current_time)}",
                    risk_type=RiskType.LIQUIDITY_RISK,
                    risk_level=RiskLevel.HIGH if total_depth < min_depth * 0.5 else RiskLevel.MEDIUM,
                    title=f"流动性不足 - {exchange_name}:{symbol}",
                    description=f"订单簿深度 ${total_depth:,.0f} 低于阈值 ${min_depth:,.0f}",
                    data={
                        "exchange": exchange_name,
                        "symbol": symbol,
                        "total_depth": total_depth,
                        "ask_depth": ask_depth,
                        "bid_depth": bid_depth,
                        "threshold": min_depth
                    },
                    timestamp=current_time
                )
                alerts.append(alert)

            # 检查买卖深度不平衡
            if ask_depth > 0 and bid_depth > 0:
                depth_ratio = min(ask_depth, bid_depth) / max(ask_depth, bid_depth)
                min_ratio = self.risk_config.get("min_depth_ratio", 0.1)

                if depth_ratio < min_ratio:
                    alert = RiskAlert(
                        id=f"depth_imbalance_{exchange_name}_{symbol}_{int(current_time)}",
                        risk_type=RiskType.LIQUIDITY_RISK,
                        risk_level=RiskLevel.MEDIUM,
                        title=f"订单簿不平衡 - {exchange_name}:{symbol}",
                        description=f"买卖深度比例 {depth_ratio:.3f} 低于阈值 {min_ratio:.3f}",
                        data={
                            "exchange": exchange_name,
                            "symbol": symbol,
                            "depth_ratio": depth_ratio,
                            "ask_depth": ask_depth,
                            "bid_depth": bid_depth,
                            "threshold": min_ratio
                        },
                        timestamp=current_time
                    )
                    alerts.append(alert)

        except Exception as e:
            self.logger.error(f"Error checking liquidity risk: {e}")

        return alerts

    async def check_funding_risk(self) -> List[RiskAlert]:
        """检查资金风险"""
        alerts = []
        current_time = time.time()

        try:
            total_balance = 0.0
            total_margin = 0.0
            total_available = 0.0
            exchange_count = 0

            # 获取所有交易所的资金状况
            for exchange_name, exchange in self.exchanges.items():
                try:
                    balance_info = None

                    # 尝试不同的余额查询方法
                    if hasattr(exchange, 'get_account_info'):
                        balance_info = await exchange.get_account_info()
                    elif hasattr(exchange, 'get_balance'):
                        # 修复：添加account_type参数，优先查询现货余额
                        from exchanges.exchanges_base import AccountType
                        try:
                            spot_balance = await exchange.get_balance(AccountType.SPOT)
                            futures_balance = await exchange.get_balance(AccountType.FUTURES)
                            # 合并现货和期货余额
                            balance_info = {}
                            for currency, amount in spot_balance.items():
                                balance_info[currency] = balance_info.get(currency, 0) + amount
                            for currency, amount in futures_balance.items():
                                balance_info[currency] = balance_info.get(currency, 0) + amount
                        except Exception as e:
                            # 如果统一账户，只查询现货余额
                            balance_info = await exchange.get_balance(AccountType.SPOT)
                    elif hasattr(exchange, 'get_account_balance'):
                        balance_info = await exchange.get_account_balance()

                    if balance_info:
                        # 🔥 修复：正确处理OKX等交易所的余额格式
                        if isinstance(balance_info, dict):
                            # 处理标准格式: {"USDT": {"available": 100.0, "locked": 0.0}}
                            usdt_info = balance_info.get('USDT', {})
                            if isinstance(usdt_info, dict):
                                # OKX统一账户格式
                                available = float(usdt_info.get('available', 0))
                                locked = float(usdt_info.get('locked', 0))
                                balance = available + locked
                                margin = 0  # OKX统一账户无单独保证金概念
                                self.logger.debug(f"🔍 [API调用] {exchange_name}余额解析: available={available}, locked={locked}")
                            else:
                                # 旧格式兼容
                                balance = float(balance_info.get('total_balance', balance_info.get('total', 0)))
                                margin = float(balance_info.get('margin_balance', balance_info.get('margin', 0)))
                                available = float(balance_info.get('available_balance', balance_info.get('available', balance)))
                        elif isinstance(balance_info, list):
                            # 如果是余额列表，计算USDT总额
                            usdt_balance = next((item for item in balance_info if item.get('asset') == 'USDT'), {})
                            balance = float(usdt_balance.get('free', 0)) + float(usdt_balance.get('locked', 0))
                            margin = 0
                            available = float(usdt_balance.get('free', 0))
                        else:
                            continue

                        total_balance += balance
                        total_margin += margin
                        total_available += available
                        exchange_count += 1

                        if DEBUG:
                            self.logger.debug(f"{exchange_name} balance: total={balance}, available={available}")

                except Exception as e:
                    self.logger.warning(f"Error getting balance from {exchange_name}: {e}")
                    continue

            # 更新指标
            self.current_metrics.total_balance = total_balance
            self.current_metrics.available_balance = total_available
            self.current_metrics.margin_ratio = total_margin / total_balance if total_balance > 0 else 0

            # 检查最小可用余额
            min_available = self.risk_config.get("min_available_balance", 1000)
            if total_available < min_available:
                alert = RiskAlert(
                    id=f"low_balance_{int(current_time)}",
                    risk_type=RiskType.FUNDING_RISK,
                    risk_level=RiskLevel.HIGH if total_available < min_available * 0.5 else RiskLevel.MEDIUM,
                    title="可用余额不足",
                    description=f"可用余额 ${total_available:,.2f} 低于阈值 ${min_available:,.2f}",
                    data={
                        "available_balance": total_available,
                        "total_balance": total_balance,
                        "threshold": min_available,
                        "exchange_count": exchange_count
                    },
                    timestamp=current_time
                )
                alerts.append(alert)

            # 检查保证金比例（如果有保证金交易）
            if total_margin > 0:
                min_margin_ratio = self.risk_config.get("min_margin_ratio", 0.3)
                if self.current_metrics.margin_ratio < min_margin_ratio:
                    alert = RiskAlert(
                        id=f"low_margin_{int(current_time)}",
                        risk_type=RiskType.FUNDING_RISK,
                        risk_level=RiskLevel.CRITICAL if self.current_metrics.margin_ratio < min_margin_ratio * 0.5 else RiskLevel.HIGH,
                        title="保证金比例过低",
                        description=f"保证金比例 {self.current_metrics.margin_ratio:.1%} 低于阈值 {min_margin_ratio:.1%}",
                        data={
                            "margin_ratio": self.current_metrics.margin_ratio,
                            "threshold": min_margin_ratio,
                            "total_balance": total_balance,
                            "margin_balance": total_margin
                        },
                        timestamp=current_time
                    )
                    alerts.append(alert)

            # 检查总资金是否过低
            min_capital_requirement = float(os.getenv("MIN_CAPITAL_REQUIREMENT", "200"))
            if total_balance < min_capital_requirement:
                alert = RiskAlert(
                    id=f"insufficient_capital_{int(current_time)}",
                    risk_type=RiskType.FUNDING_RISK,
                    risk_level=RiskLevel.CRITICAL,
                    title="资金不足",
                    description=f"总资金 ${total_balance:,.2f} 过低，建议增加资金",
                    data={
                        "total_balance": total_balance,
                        "min_recommended": min_capital_requirement,
                        "exchange_count": exchange_count
                    },
                    timestamp=current_time
                )
                alerts.append(alert)

        except Exception as e:
            self.logger.error(f"Error checking funding risk: {e}")

        return alerts

    async def check_technical_risk(self) -> List[RiskAlert]:
        """检查技术风险"""
        alerts = []
        current_time = time.time()

        try:
            # 检查API延迟和连接状态
            total_latency = 0.0
            latency_count = 0
            connection_failures = 0

            for exchange_name, exchange in self.exchanges.items():
                try:
                    # 测试API响应时间
                    start_time = time.time()

                    # 尝试不同的ping方法
                    if hasattr(exchange, 'ping'):
                        await asyncio.wait_for(exchange.ping(), timeout=5.0)
                    elif hasattr(exchange, 'get_server_time'):
                        await asyncio.wait_for(exchange.get_server_time(), timeout=5.0)
                    elif hasattr(exchange, 'get_exchange_info'):
                        await asyncio.wait_for(exchange.get_exchange_info(), timeout=5.0)
                    else:
                        # 如果没有专门的ping方法，跳过
                        continue

                    latency = (time.time() - start_time) * 1000
                    total_latency += latency
                    latency_count += 1

                    # 记录延迟历史
                    self.latency_history.append((current_time, latency))

                    if DEBUG:
                        self.logger.debug(f"{exchange_name} API latency: {latency:.1f}ms")

                except asyncio.TimeoutError:
                    connection_failures += 1
                    self.logger.warning(f"API timeout for {exchange_name}")
                except Exception as e:
                    connection_failures += 1
                    self.logger.warning(f"Connection test failed for {exchange_name}: {e}")

            # 清理历史数据
            if len(self.latency_history) > self.max_history_length:
                self.latency_history = self.latency_history[-self.max_history_length:]

            # 更新指标
            self.current_metrics.api_latency_ms = total_latency / latency_count if latency_count > 0 else 0
            self.current_metrics.connection_failures = connection_failures

            # 检查API延迟
            max_latency = self.risk_config.get("max_api_latency_ms", 500)
            if self.current_metrics.api_latency_ms > max_latency:
                alert = RiskAlert(
                    id=f"high_latency_{int(current_time)}",
                    risk_type=RiskType.TECHNICAL_RISK,
                    risk_level=RiskLevel.HIGH if self.current_metrics.api_latency_ms > max_latency * 2 else RiskLevel.MEDIUM,
                    title="API延迟过高",
                    description=f"平均API延迟 {self.current_metrics.api_latency_ms:.1f}ms 超过阈值 {max_latency}ms",
                    data={
                        "average_latency_ms": self.current_metrics.api_latency_ms,
                        "threshold_ms": max_latency,
                        "tested_exchanges": latency_count
                    },
                    timestamp=current_time
                )
                alerts.append(alert)

            # 检查连接失败
            max_failures = self.risk_config.get("max_connection_failures", 3)
            if connection_failures > max_failures:
                alert = RiskAlert(
                    id=f"connection_failures_{int(current_time)}",
                    risk_type=RiskType.TECHNICAL_RISK,
                    risk_level=RiskLevel.CRITICAL,
                    title="连接失败过多",
                    description=f"连接失败数量 {connection_failures} 超过阈值 {max_failures}",
                    data={
                        "connection_failures": connection_failures,
                        "threshold": max_failures,
                        "total_exchanges": len(self.exchanges)
                    },
                    timestamp=current_time
                )
                alerts.append(alert)

        except Exception as e:
            self.logger.error(f"Error checking technical risk: {e}")

        return alerts

    async def check_operational_risk(self) -> List[RiskAlert]:
        """检查操作风险"""
        alerts = []
        current_time = time.time()

        try:
            # 检查订单管理器性能
            if self.order_manager:
                try:
                    stats = self.order_manager.get_performance_stats()

                    # 检查对冲失败率
                    total_orders = stats.get('total_orders', 0)
                    failed_orders = stats.get('failed_orders', 0)

                    if total_orders > 0:
                        hedge_failure_rate = failed_orders / total_orders
                        self.current_metrics.hedge_failure_rate = hedge_failure_rate

                        max_failure_rate = self.risk_config.get("max_hedge_failure_rate", 0.05)
                        if hedge_failure_rate > max_failure_rate:
                            alert = RiskAlert(
                                id=f"high_failure_rate_{int(current_time)}",
                                risk_type=RiskType.OPERATIONAL_RISK,
                                risk_level=RiskLevel.HIGH if hedge_failure_rate > max_failure_rate * 2 else RiskLevel.MEDIUM,
                                title="对冲失败率过高",
                                description=f"对冲失败率 {hedge_failure_rate:.1%} 超过阈值 {max_failure_rate:.1%}",
                                data={
                                    "failure_rate": hedge_failure_rate,
                                    "threshold": max_failure_rate,
                                    "total_orders": total_orders,
                                    "failed_orders": failed_orders
                                },
                                timestamp=current_time
                            )
                            alerts.append(alert)

                except Exception as e:
                    self.logger.warning(f"Error getting order manager stats: {e}")

            # 检查仓位监控器状态
            if self.position_monitor:
                try:
                    # 获取不平衡仓位
                    imbalanced_positions = self.position_monitor.get_imbalanced_positions()
                    all_positions = getattr(self.position_monitor, 'hedge_pairs', {})

                    if len(all_positions) > 0:
                        imbalance_ratio = len(imbalanced_positions) / len(all_positions)
                        self.current_metrics.position_imbalance_ratio = imbalance_ratio

                        max_imbalance_ratio = self.risk_config.get("max_position_imbalance_ratio", 0.01)
                        if imbalance_ratio > max_imbalance_ratio:
                            alert = RiskAlert(
                                id=f"position_imbalance_{int(current_time)}",
                                risk_type=RiskType.OPERATIONAL_RISK,
                                risk_level=RiskLevel.MEDIUM,
                                title="仓位不平衡比例过高",
                                description=f"不平衡仓位比例 {imbalance_ratio:.1%} 超过阈值 {max_imbalance_ratio:.1%}",
                                data={
                                    "imbalance_ratio": imbalance_ratio,
                                    "threshold": max_imbalance_ratio,
                                    "total_positions": len(all_positions),
                                    "imbalanced_positions": len(imbalanced_positions)
                                },
                                timestamp=current_time
                            )
                            alerts.append(alert)

                except Exception as e:
                    self.logger.warning(f"Error getting position monitor data: {e}")

        except Exception as e:
            self.logger.error(f"Error checking operational risk: {e}")

        return alerts

    async def run_risk_check(self) -> List[RiskAlert]:
        """执行完整风险检查"""
        all_alerts = []

        try:
            # 检查资金风险
            funding_alerts = await self.check_funding_risk()
            all_alerts.extend(funding_alerts)

            # 检查技术风险
            technical_alerts = await self.check_technical_risk()
            all_alerts.extend(technical_alerts)

            # 检查操作风险
            operational_alerts = await self.check_operational_risk()
            all_alerts.extend(operational_alerts)

            # 处理所有告警
            for alert in all_alerts:
                await self._process_alert(alert)

            # 更新指标历史
            self.current_metrics.timestamp = time.time()
            self.metrics_history.append(self.current_metrics)
            if len(self.metrics_history) > self.max_history_length:
                self.metrics_history.pop(0)

            self.total_checks += 1

            if DEBUG and all_alerts:
                self.logger.debug(f"Risk check completed: {len(all_alerts)} alerts generated")

        except Exception as e:
            self.logger.error(f"Error in risk check: {e}")

        return all_alerts

    async def _process_alert(self, alert: RiskAlert):
        """处理风险告警"""
        try:
            # 检查是否已存在相同告警（避免重复）
            existing_alert = None
            for existing_id, existing in self.risk_alerts.items():
                if (existing.risk_type == alert.risk_type and
                    existing.title == alert.title and
                    not existing.is_resolved and
                    abs(existing.timestamp - alert.timestamp) < 300):  # 5分钟内的相同告警
                    existing_alert = existing
                    break

            if existing_alert:
                # 更新现有告警的时间戳
                existing_alert.timestamp = alert.timestamp
                existing_alert.data = alert.data
                return

            # 存储新告警
            self.risk_alerts[alert.id] = alert
            self.alert_count += 1

            if alert.risk_level == RiskLevel.CRITICAL:
                self.critical_alert_count += 1

            # 调用回调函数
            for callback in self.risk_alert_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(alert)
                    else:
                        callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in risk alert callback: {e}")

            # 严重风险特殊处理
            if alert.risk_level == RiskLevel.CRITICAL:
                for callback in self.critical_risk_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(alert)
                        else:
                            callback(alert)
                    except Exception as e:
                        self.logger.error(f"Error in critical risk callback: {e}")

            # 记录日志
            level_str = alert.risk_level.value.upper()
            self.logger.warning(f"[{level_str}] {alert.title}: {alert.description}")

        except Exception as e:
            self.logger.error(f"Error processing alert: {e}")

    def resolve_alert(self, alert_id: str) -> bool:
        """解决风险告警"""
        try:
            if alert_id in self.risk_alerts:
                self.risk_alerts[alert_id].is_resolved = True
                self.risk_alerts[alert_id].resolved_time = time.time()

                if DEBUG:
                    self.logger.info(f"Risk alert resolved: {alert_id}")
                return True
            else:
                self.logger.warning(f"Alert not found: {alert_id}")
                return False
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False

    def start_monitoring(self):
        """启动风险监控"""
        if self.is_running:
            self.logger.warning("Risk monitoring is already running")
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        self.logger.info("Risk monitoring started")

    def stop_monitoring(self):
        """停止风险监控"""
        self.is_running = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)

        self.logger.info("Risk monitoring stopped")

    def _monitoring_loop(self):
        """监控循环"""
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()

        while self.is_running:
            try:
                loop.run_until_complete(self.run_risk_check())
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                time.sleep(self.check_interval)

        loop.close()

    def get_current_risk_level(self) -> RiskLevel:
        """获取当前风险等级"""
        return self.current_metrics.get_risk_level()

    def get_risk_level(self) -> RiskLevel:
        """获取当前风险等级 (兼容方法)"""
        return self.get_current_risk_level()

    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        active_alerts = [alert for alert in self.risk_alerts.values() if not alert.is_resolved]

        return {
            "overall_risk_score": self.current_metrics.calculate_overall_risk_score(),
            "current_risk_level": self.current_metrics.get_risk_level().value,
            "total_alerts": len(self.risk_alerts),
            "active_alerts": len(active_alerts),
            "critical_alerts": len([a for a in active_alerts if a.risk_level == RiskLevel.CRITICAL]),
            "high_alerts": len([a for a in active_alerts if a.risk_level == RiskLevel.HIGH]),
            "medium_alerts": len([a for a in active_alerts if a.risk_level == RiskLevel.MEDIUM]),
            "low_alerts": len([a for a in active_alerts if a.risk_level == RiskLevel.LOW]),
            "last_check_time": self.current_metrics.timestamp,
            "is_monitoring": self.is_running
        }

    def get_active_alerts(self, risk_level: Optional[RiskLevel] = None) -> List[Dict]:
        """获取活跃的风险告警"""
        active_alerts = [alert for alert in self.risk_alerts.values() if not alert.is_resolved]

        if risk_level:
            active_alerts = [alert for alert in active_alerts if alert.risk_level == risk_level]

        return [alert.to_dict() for alert in active_alerts]

    def get_current_metrics(self) -> Dict:
        """获取当前风险指标"""
        return {
            "overall_risk_score": self.current_metrics.calculate_overall_risk_score(),
            "risk_level": self.current_metrics.get_risk_level().value,
            "spread_risk": {
                "current_spread": self.current_metrics.current_spread,
                "spread_volatility": self.current_metrics.spread_volatility,
                "max_deviation": self.current_metrics.max_spread_deviation
            },
            "liquidity_risk": {
                "spot_depth_ratio": self.current_metrics.spot_depth_ratio,
                "futures_depth_ratio": self.current_metrics.futures_depth_ratio,
                "min_depth_threshold": self.current_metrics.min_depth_threshold
            },
            "funding_risk": {
                "total_balance": self.current_metrics.total_balance,
                "available_balance": self.current_metrics.available_balance,
                "margin_ratio": self.current_metrics.margin_ratio
            },
            "technical_risk": {
                "api_latency_ms": self.current_metrics.api_latency_ms,
                "connection_failures": self.current_metrics.connection_failures,
                "websocket_delays": self.current_metrics.websocket_delays
            },
            "operational_risk": {
                "hedge_failure_rate": self.current_metrics.hedge_failure_rate,
                "position_imbalance_ratio": self.current_metrics.position_imbalance_ratio,
                "profit_loss_ratio": self.current_metrics.profit_loss_ratio
            },
            "timestamp": self.current_metrics.timestamp
        }

    def get_monitor_stats(self) -> Dict:
        """获取监控统计"""
        return {
            "is_running": self.is_running,
            "total_checks": self.total_checks,
            "alert_count": self.alert_count,
            "critical_alert_count": self.critical_alert_count,
            "metrics_history_count": len(self.metrics_history),
            "spread_history_count": len(self.spread_history),
            "latency_history_count": len(self.latency_history),
            "check_interval": self.check_interval,
            "last_check_time": self.current_metrics.timestamp
        }

    def get_risk_history(self, hours: int = 24) -> List[Dict]:
        """获取风险历史数据"""
        cutoff_time = time.time() - (hours * 3600)

        recent_metrics = [
            metrics for metrics in self.metrics_history
            if metrics.timestamp >= cutoff_time
        ]

        return [{
            "timestamp": metrics.timestamp,
            "risk_score": metrics.calculate_overall_risk_score(),
            "risk_level": metrics.get_risk_level().value,
            "api_latency": metrics.api_latency_ms,
            "total_balance": metrics.total_balance,
            "hedge_failure_rate": metrics.hedge_failure_rate
        } for metrics in recent_metrics]

    def export_risk_data(self, filename: Optional[str] = None) -> str:
        """导出风险数据"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"risk_data_{timestamp}.json"

            data = {
                "export_time": time.time(),
                "risk_config": self.risk_config,
                "current_metrics": self.get_current_metrics(),
                "active_alerts": self.get_active_alerts(),
                "resolved_alerts": [alert.to_dict() for alert in self.risk_alerts.values() if alert.is_resolved],
                "risk_history": self.get_risk_history(168),  # 一周历史
                "monitor_stats": self.get_monitor_stats()
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Risk data exported to {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"Error exporting risk data: {e}")
            return ""

    def cleanup_old_alerts(self, hours: int = 24):
        """清理旧的已解决告警"""
        cutoff_time = time.time() - (hours * 3600)

        old_alert_ids = [
            alert_id for alert_id, alert in self.risk_alerts.items()
            if alert.is_resolved and alert.resolved_time and alert.resolved_time < cutoff_time
        ]

        for alert_id in old_alert_ids:
            del self.risk_alerts[alert_id]

        if old_alert_ids:
            self.logger.info(f"Cleaned up {len(old_alert_ids)} old resolved alerts")

# 风险管理辅助函数
def calculate_position_risk(position_value: float, total_balance: float) -> float:
    """计算仓位风险比例"""
    if total_balance <= 0:
        return 1.0
    return position_value / total_balance

def calculate_var(returns: List[float], confidence_level: float = 0.95) -> float:
    """计算风险价值 (Value at Risk)"""
    if not returns or len(returns) < 2:
        return 0.0

    returns_sorted = sorted(returns)
    index = int((1 - confidence_level) * len(returns_sorted))

    if index >= len(returns_sorted):
        return returns_sorted[-1]

    return returns_sorted[index]

def calculate_max_drawdown(equity_curve: List[float]) -> float:
    """计算最大回撤"""
    if not equity_curve:
        return 0.0

    peak = equity_curve[0]
    max_drawdown = 0.0

    for value in equity_curve:
        if value > peak:
            peak = value
        else:
            drawdown = (peak - value) / peak if peak > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)

    return max_drawdown

def calculate_correlation(x: List[float], y: List[float]) -> float:
    """计算相关系数"""
    if len(x) != len(y) or len(x) < 2:
        return 0.0

    try:
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(xi * yi for xi, yi in zip(x, y))
        sum_x2 = sum(xi * xi for xi in x)
        sum_y2 = sum(yi * yi for yi in y)

        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5

        if denominator == 0:
            return 0.0

        return numerator / denominator
    except:
        return 0.0

# 测试和调试功能
async def test_risk_monitor():
    """测试风险监控器"""
    logger.info("Testing RiskMonitor...")

    # 创建模拟组件
    class MockPositionMonitor:
        def get_imbalanced_positions(self):
            return [{"symbol": "BTCUSDT", "quantity_diff": 0.001}]

        @property
        def hedge_pairs(self):
            return {"BTCUSDT": {"balanced": False}, "ETHUSDT": {"balanced": True}}

    class MockOrderManager:
        def get_performance_stats(self):
            return {
                "total_orders": 100,
                "failed_orders": 5,
                "success_rate": 95.0
            }

    class MockExchange:
        def __init__(self, name):
            self.name = name

        async def ping(self):
            await asyncio.sleep(0.05)  # 50ms延迟
            return {"ping": "pong"}

        async def get_account_info(self):
            return {
                "total_balance": 5000.0,
                "margin_balance": 1500.0,
                "available_balance": 3500.0
            }

    # 创建模拟组件
    position_monitor = MockPositionMonitor()
    order_manager = MockOrderManager()
    exchanges = {
        "gate": MockExchange("gate"),
        "bybit": MockExchange("bybit")
    }

    # 创建风险监控器
    risk_monitor = RiskMonitor(position_monitor, order_manager, exchanges)

    # 添加回调函数
    def risk_alert_callback(alert):
        logger.info(f"Risk alert: {alert.title} [{alert.risk_level.value}]")

    def critical_risk_callback(alert):
        logger.warning(f"CRITICAL RISK: {alert.title}")

    risk_monitor.add_risk_alert_callback(risk_alert_callback)
    risk_monitor.add_critical_risk_callback(critical_risk_callback)

    # 执行风险检查
    alerts = await risk_monitor.run_risk_check()
    logger.info(f"Generated {len(alerts)} alerts")

    # 测试价差风险检查
    spread_alerts = await risk_monitor.check_spread_risk("BTCUSDT", 50000.0, 50200.0)
    logger.info(f"Spread risk alerts: {len(spread_alerts)}")

    # 测试流动性风险检查
    mock_orderbook = {
        "asks": [["50100", "1.0"], ["50110", "2.0"]],
        "bids": [["50090", "1.1"], ["50080", "2.1"]]
    }
    liquidity_alerts = await risk_monitor.check_liquidity_risk("BTCUSDT", "gate", mock_orderbook)
    logger.info(f"Liquidity risk alerts: {len(liquidity_alerts)}")

    # 获取风险摘要
    summary = risk_monitor.get_risk_summary()
    assert "overall_risk_score" in summary, "Should have overall risk score"
    assert "current_risk_level" in summary, "Should have current risk level"
    logger.info(f"Risk summary: score={summary['overall_risk_score']:.1f}, level={summary['current_risk_level']}")

    # 获取当前指标
    metrics = risk_monitor.get_current_metrics()
    assert "overall_risk_score" in metrics, "Should have metrics"
    logger.info(f"Current metrics API latency: {metrics['technical_risk']['api_latency_ms']:.1f}ms")

    # 获取监控统计
    stats = risk_monitor.get_monitor_stats()
    assert stats["total_checks"] >= 1, "Should have at least 1 check"
    logger.info(f"Monitor stats: {stats['total_checks']} checks, {stats['alert_count']} alerts")

    # 测试数据导出
    filename = risk_monitor.export_risk_data()
    if filename:
        logger.info(f"Risk data exported to: {filename}")
        # 清理测试文件
        try:
            os.remove(filename)
        except:
            pass

    logger.info("✅ RiskMonitor test completed successfully")

if __name__ == '__main__':
    print("🧪 风险监控模块独立测试开始...")
    asyncio.run(test_risk_monitor())
    print("✅ 风险监控模块测试完成")