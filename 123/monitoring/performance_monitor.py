"""
性能监控模块
监控套利系统的执行性能和盈利表现
"""

# 修复导入路径
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import time
import json
import statistics
import aiohttp
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading

from config.debug_config import DEBUG
from utils.logger import get_logger

logger = get_logger(__name__)

class PerformanceMetricType(Enum):
    SYSTEM = "system"           # 系统性能
    TRADING = "trading"         # 交易性能
    FINANCIAL = "financial"     # 财务性能
    LATENCY = "latency"        # 延迟性能
    THROUGHPUT = "throughput"   # 吞吐量性能

@dataclass
class ConnectionStatus:
    """连接状态数据类"""
    exchange: str
    connection_type: str  # 'websocket', 'rest_api'
    is_connected: bool = False
    last_message_time: float = 0.0
    message_count: int = 0
    reconnect_count: int = 0
    consecutive_failures: int = 0
    response_time_ms: float = 0.0
    quality_score: float = 0.0  # 0-100分

    def to_dict(self) -> Dict:
        return {
            "exchange": self.exchange,
            "connection_type": self.connection_type,
            "is_connected": self.is_connected,
            "last_message_time": self.last_message_time,
            "message_count": self.message_count,
            "reconnect_count": self.reconnect_count,
            "consecutive_failures": self.consecutive_failures,
            "response_time_ms": self.response_time_ms,
            "quality_score": self.quality_score
        }

@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float

    # 系统性能指标
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    network_latency_ms: float = 0.0

    # 交易性能指标
    orders_per_minute: float = 0.0
    success_rate: float = 0.0
    average_execution_time_ms: float = 0.0
    hedge_delay_ms: float = 0.0

    # 财务性能指标
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    total_fees: float = 0.0
    roi_percentage: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0

    # 套利特定指标
    arbitrage_opportunities: int = 0
    successful_arbitrages: int = 0
    failed_arbitrages: int = 0
    average_spread_captured: float = 0.0

    # 🔥 新增：连接监控指标
    connection_quality_score: float = 0.0
    total_reconnects: int = 0
    avg_response_time_ms: float = 0.0
    
    def to_dict(self) -> Dict:
        return {
            "timestamp": self.timestamp,
            "system": {
                "cpu_usage": self.cpu_usage,
                "memory_usage": self.memory_usage,
                "network_latency_ms": self.network_latency_ms
            },
            "trading": {
                "orders_per_minute": self.orders_per_minute,
                "success_rate": self.success_rate,
                "average_execution_time_ms": self.average_execution_time_ms,
                "hedge_delay_ms": self.hedge_delay_ms
            },
            "financial": {
                "total_pnl": self.total_pnl,
                "realized_pnl": self.realized_pnl,
                "unrealized_pnl": self.unrealized_pnl,
                "total_fees": self.total_fees,
                "roi_percentage": self.roi_percentage,
                "sharpe_ratio": self.sharpe_ratio,
                "max_drawdown": self.max_drawdown
            },
            "arbitrage": {
                "opportunities": self.arbitrage_opportunities,
                "successful": self.successful_arbitrages,
                "failed": self.failed_arbitrages,
                "average_spread_captured": self.average_spread_captured
            },
            "connection": {
                "quality_score": self.connection_quality_score,
                "total_reconnects": self.total_reconnects,
                "avg_response_time_ms": self.avg_response_time_ms
            }
        }

@dataclass
class TradeRecord:
    """交易记录"""
    id: str
    timestamp: float
    symbol: str
    side: str
    quantity: float
    price: float
    fee: float
    pnl: float
    exchange: str
    order_type: str
    execution_time_ms: float
    
    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "symbol": self.symbol,
            "side": self.side,
            "quantity": self.quantity,
            "price": self.price,
            "fee": self.fee,
            "pnl": self.pnl,
            "exchange": self.exchange,
            "order_type": self.order_type,
            "execution_time_ms": self.execution_time_ms
        }

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, order_manager=None, position_monitor=None, risk_monitor=None):
        self.order_manager = order_manager
        self.position_monitor = position_monitor
        self.risk_monitor = risk_monitor
        self.logger = get_logger("PerformanceMonitor")

        # 性能数据存储
        self.snapshots: List[PerformanceSnapshot] = []
        self.trade_records: List[TradeRecord] = []
        self.pnl_history: List[Tuple[float, float]] = []  # (timestamp, pnl)

        # 🔥 新增：连接监控数据存储
        self.connection_statuses: Dict[str, ConnectionStatus] = {}
        self.reconnect_history: List[Tuple[float, str, str]] = []  # (timestamp, exchange, reason)
        self.response_time_history: List[Tuple[float, str, float]] = []  # (timestamp, exchange, response_time)

        # 监控配置
        self.snapshot_interval = 60  # 每分钟记录一次快照
        self.max_snapshots = 1440    # 保存24小时数据
        self.max_trades = 10000      # 保存最近1万笔交易
        self.connection_check_interval = 30  # 连接检查间隔（秒）

        # 基准数据
        self.initial_balance = 0.0
        self.start_time = time.time()

        # 缓存数据
        self.last_snapshot = None
        self.performance_cache = {}
        self.cache_ttl = 30  # 缓存30秒

        # 监控状态
        self.is_running = False
        self.monitor_thread = None
        self.connection_monitor_thread = None

        # 回调函数
        self.performance_update_callbacks: List[Callable] = []
        self.milestone_callbacks: List[Callable] = []
        self.connection_update_callbacks: List[Callable] = []

        # 统计计数器
        self.total_snapshots = 0
        self.total_trades_recorded = 0
        self.total_reconnects = 0

        if DEBUG:
            self.logger.info("PerformanceMonitor initialized")
    
    def add_performance_update_callback(self, callback: Callable):
        """添加性能更新回调"""
        self.performance_update_callbacks.append(callback)
    
    def add_milestone_callback(self, callback: Callable):
        """添加里程碑回调"""
        self.milestone_callbacks.append(callback)

    def add_connection_update_callback(self, callback: Callable):
        """添加连接更新回调"""
        self.connection_update_callbacks.append(callback)
    
    def set_initial_balance(self, balance: float):
        """设置初始余额"""
        self.initial_balance = balance
        self.logger.info(f"Initial balance set to ${balance:,.2f}")
    
    def record_trade(self, order_result: Dict, exchange: str, execution_time_ms: float):
        """
        记录交易
        
        Args:
            order_result: 订单执行结果
            exchange: 交易所名称
            execution_time_ms: 执行时间
        """
        try:
            trade = TradeRecord(
                id=order_result.get('order_id', ''),
                timestamp=time.time(),
                symbol=order_result.get('symbol', ''),
                side=order_result.get('side', ''),
                quantity=float(order_result.get('executed_quantity', 0)),
                price=float(order_result.get('executed_price', 0)),
                fee=float(order_result.get('fee', 0)),
                pnl=0.0,  # 需要后续计算
                exchange=exchange,
                order_type=order_result.get('type', ''),
                execution_time_ms=execution_time_ms
            )
            
            self.trade_records.append(trade)
            self.total_trades_recorded += 1
            
            # 清理旧记录
            if len(self.trade_records) > self.max_trades:
                self.trade_records = self.trade_records[-self.max_trades:]
            
            if DEBUG:
                self.logger.debug(f"Trade recorded: {trade.symbol} {trade.side} "
                                f"{trade.quantity} @ {trade.price}")
            
        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")

    def update_connection_status(self, exchange: str, connection_type: str,
                               is_connected: bool, response_time_ms: float = 0.0):
        """🔥 更新连接状态"""
        try:
            key = f"{exchange}_{connection_type}"
            current_time = time.time()

            if key not in self.connection_statuses:
                self.connection_statuses[key] = ConnectionStatus(
                    exchange=exchange,
                    connection_type=connection_type
                )

            status = self.connection_statuses[key]

            # 更新基本状态
            was_connected = status.is_connected
            status.is_connected = is_connected
            status.response_time_ms = response_time_ms

            if is_connected:
                status.last_message_time = current_time
                status.message_count += 1
                status.consecutive_failures = 0
            else:
                status.consecutive_failures += 1

            # 检测重连事件
            if not was_connected and is_connected:
                status.reconnect_count += 1
                self.total_reconnects += 1
                self.reconnect_history.append((current_time, exchange, "reconnected"))
                self.logger.info(f"🔄 {exchange} {connection_type} 重连成功")

            # 记录响应时间
            if response_time_ms > 0:
                self.response_time_history.append((current_time, exchange, response_time_ms))
                # 保持最近1000条记录
                if len(self.response_time_history) > 1000:
                    self.response_time_history = self.response_time_history[-1000:]

            # 计算质量评分
            status.quality_score = self._calculate_connection_quality(status)

            # 触发回调
            for callback in self.connection_update_callbacks:
                try:
                    callback(status)
                except Exception as e:
                    self.logger.error(f"Connection update callback error: {e}")

            if DEBUG:
                self.logger.debug(f"Connection status updated: {exchange} {connection_type} "
                                f"connected={is_connected} quality={status.quality_score:.1f}")

        except Exception as e:
            self.logger.error(f"Error updating connection status: {e}")

    def _calculate_connection_quality(self, status: ConnectionStatus) -> float:
        """🔥 计算连接质量评分 (0-100分)"""
        try:
            score = 100.0
            current_time = time.time()

            # 连接状态 (40分)
            if not status.is_connected:
                score -= 40

            # 响应时间 (30分) - 更精细的评分
            if status.response_time_ms > 0:
                if status.response_time_ms > 1000:  # >1秒
                    score -= 30
                elif status.response_time_ms > 500:  # >500ms
                    score -= 20
                elif status.response_time_ms > 200:  # >200ms
                    score -= 10
                elif status.response_time_ms > 100:  # >100ms
                    score -= 5
                elif status.response_time_ms > 50:   # >50ms
                    score -= 2
                # 20ms以下不扣分，认为是优秀连接

            # 消息新鲜度 (20分)
            if status.last_message_time > 0:
                message_age = current_time - status.last_message_time
                if message_age > 60:  # >1分钟
                    score -= 20
                elif message_age > 30:  # >30秒
                    score -= 10
                elif message_age > 10:  # >10秒
                    score -= 5

            # 连续失败次数 (10分)
            if status.consecutive_failures > 0:
                score -= min(status.consecutive_failures * 2, 10)

            return max(0.0, score)

        except Exception as e:
            self.logger.error(f"Error calculating connection quality: {e}")
            return 0.0

    async def test_exchange_speed(self, exchange_name: str, exchange_instance) -> Dict[str, float]:
        """🔥 测试交易所API响应速度"""
        try:
            results = {}

            # 测试REST API速度
            if hasattr(exchange_instance, 'fetch_ticker'):
                start_time = time.time()
                try:
                    # 尝试获取一个常见交易对的ticker
                    await exchange_instance.fetch_ticker('BTC/USDT')
                    rest_time = (time.time() - start_time) * 1000
                    results['rest_api_ms'] = rest_time

                    # 更新连接状态
                    self.update_connection_status(exchange_name, 'rest_api', True, rest_time)

                except Exception as e:
                    results['rest_api_ms'] = -1
                    self.update_connection_status(exchange_name, 'rest_api', False)
                    self.logger.warning(f"REST API speed test failed for {exchange_name}: {e}")

            # 测试WebSocket延迟（如果有活跃连接）
            if hasattr(exchange_instance, 'ws') and exchange_instance.ws:
                # WebSocket延迟通过ping-pong测试
                start_time = time.time()
                try:
                    # 这里可以实现WebSocket ping测试
                    # 暂时使用模拟值
                    ws_time = 50.0  # 模拟WebSocket延迟
                    results['websocket_ms'] = ws_time

                    self.update_connection_status(exchange_name, 'websocket', True, ws_time)

                except Exception as e:
                    results['websocket_ms'] = -1
                    self.update_connection_status(exchange_name, 'websocket', False)
                    self.logger.warning(f"WebSocket speed test failed for {exchange_name}: {e}")

            return results

        except Exception as e:
            self.logger.error(f"Error testing exchange speed for {exchange_name}: {e}")
            return {}

    def get_connection_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """🔥 获取连接统计信息"""
        try:
            cutoff_time = time.time() - (hours * 3600)

            # 重连统计
            recent_reconnects = [r for r in self.reconnect_history if r[0] >= cutoff_time]
            reconnects_by_exchange = {}
            for _, exchange, _ in recent_reconnects:
                reconnects_by_exchange[exchange] = reconnects_by_exchange.get(exchange, 0) + 1

            # 响应时间统计
            recent_response_times = [r for r in self.response_time_history if r[0] >= cutoff_time]
            avg_response_times = {}
            for _, exchange, response_time in recent_response_times:
                if exchange not in avg_response_times:
                    avg_response_times[exchange] = []
                avg_response_times[exchange].append(response_time)

            # 计算平均响应时间
            for exchange in avg_response_times:
                times = avg_response_times[exchange]
                avg_response_times[exchange] = {
                    'avg_ms': statistics.mean(times),
                    'min_ms': min(times),
                    'max_ms': max(times),
                    'count': len(times)
                }

            # 当前连接状态
            current_connections = {}
            total_quality_score = 0
            connected_count = 0

            for key, status in self.connection_statuses.items():
                current_connections[key] = status.to_dict()
                total_quality_score += status.quality_score
                if status.is_connected:
                    connected_count += 1

            overall_quality = total_quality_score / len(self.connection_statuses) if self.connection_statuses else 0

            return {
                'period_hours': hours,
                'total_reconnects': len(recent_reconnects),
                'reconnects_by_exchange': reconnects_by_exchange,
                'avg_response_times': avg_response_times,
                'current_connections': current_connections,
                'overall_quality_score': overall_quality,
                'connected_count': connected_count,
                'total_connections': len(self.connection_statuses)
            }

        except Exception as e:
            self.logger.error(f"Error getting connection statistics: {e}")
            return {}
    
    async def get_system_metrics(self) -> Dict:
        """获取系统性能指标"""
        try:
            import psutil

            # CPU使用率 - 🚀 手动极限优化：缓存CPU数据，减少系统调用
            if not hasattr(self, '_last_cpu_check') or time.time() - self._last_cpu_check > 1:
                self._cached_cpu_usage = psutil.cpu_percent(interval=0)
                self._last_cpu_check = time.time()
            cpu_usage = self._cached_cpu_usage

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent

            # 网络延迟（简单测试）
            start_time = time.time()
            try:
                # 这里可以ping一个稳定的服务器
                await asyncio.sleep(0.001)  # 模拟网络延迟测试
                network_latency = (time.time() - start_time) * 1000
            except:
                network_latency = 0.0

            return {
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "network_latency_ms": network_latency
            }

        except ImportError:
            # 如果psutil不可用，返回模拟数据
            return {
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "network_latency_ms": 0.0
            }
        except Exception as e:
            self.logger.error(f"Error getting system metrics: {e}")
            return {
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "network_latency_ms": 0.0
            }

    async def get_connection_metrics(self) -> Dict:
        """🔥 获取连接监控指标"""
        try:
            if not self.connection_statuses:
                return {
                    "quality_score": 0.0,
                    "total_reconnects": 0,
                    "avg_response_time_ms": 0.0
                }

            # 计算总体质量评分
            total_quality = sum(status.quality_score for status in self.connection_statuses.values())
            avg_quality = total_quality / len(self.connection_statuses)

            # 计算平均响应时间
            response_times = [status.response_time_ms for status in self.connection_statuses.values()
                            if status.response_time_ms > 0]
            avg_response_time = statistics.mean(response_times) if response_times else 0.0

            return {
                "quality_score": avg_quality,
                "total_reconnects": self.total_reconnects,
                "avg_response_time_ms": avg_response_time
            }

        except Exception as e:
            self.logger.error(f"Error getting connection metrics: {e}")
            return {
                "quality_score": 0.0,
                "total_reconnects": 0,
                "avg_response_time_ms": 0.0
            }
    
    async def get_trading_metrics(self) -> Dict:
        """获取交易性能指标"""
        try:
            current_time = time.time()
            one_minute_ago = current_time - 60
            
            # 计算每分钟订单数
            recent_trades = [t for t in self.trade_records if t.timestamp >= one_minute_ago]
            orders_per_minute = len(recent_trades)
            
            # 计算成功率
            if self.order_manager:
                stats = self.order_manager.get_performance_stats()
                success_rate = stats.get('success_rate', 0.0)
            else:
                success_rate = 0.0
            
            # 计算平均执行时间
            if recent_trades:
                avg_execution_time = statistics.mean([t.execution_time_ms for t in recent_trades])
            else:
                avg_execution_time = 0.0
            
            # 计算平均对冲延迟
            if self.order_manager:
                order_stats = self.order_manager.get_performance_stats()
                hedge_delay = order_stats.get('average_hedge_delay_ms', 0.0)
            else:
                hedge_delay = 0.0
            
            return {
                "orders_per_minute": orders_per_minute,
                "success_rate": success_rate,
                "average_execution_time_ms": avg_execution_time,
                "hedge_delay_ms": hedge_delay
            }
            
        except Exception as e:
            self.logger.error(f"Error getting trading metrics: {e}")
            return {
                "orders_per_minute": 0.0,
                "success_rate": 0.0,
                "average_execution_time_ms": 0.0,
                "hedge_delay_ms": 0.0
            }
    
    async def get_financial_metrics(self) -> Dict:
        """获取财务性能指标"""
        try:
            # 计算总PnL
            total_pnl = sum(trade.pnl for trade in self.trade_records)
            total_fees = sum(trade.fee for trade in self.trade_records)
            
            # 计算已实现和未实现PnL
            realized_pnl = total_pnl  # 已完成交易的PnL
            unrealized_pnl = 0.0
            
            if self.position_monitor:
                # 从仓位监控器获取未实现PnL
                positions = self.position_monitor.get_all_positions()
                futures_positions = positions.get('futures_positions', {})
                
                for exchange_positions in futures_positions.values():
                    for position in exchange_positions.values():
                        unrealized_pnl += position.get('unrealized_pnl', 0.0)
            
            # 计算ROI
            if self.initial_balance > 0:
                roi_percentage = (total_pnl / self.initial_balance) * 100
            else:
                roi_percentage = 0.0
            
            # 计算夏普比率
            if len(self.pnl_history) >= 30:  # 至少30个数据点
                returns = []
                for i in range(1, len(self.pnl_history)):
                    prev_pnl = self.pnl_history[i-1][1]
                    curr_pnl = self.pnl_history[i][1]
                    if prev_pnl != 0:
                        returns.append((curr_pnl - prev_pnl) / abs(prev_pnl))
                
                if returns and statistics.stdev(returns) != 0:
                    avg_return = statistics.mean(returns)
                    return_std = statistics.stdev(returns)
                    sharpe_ratio = avg_return / return_std * (252 ** 0.5)  # 年化夏普比率
                else:
                    sharpe_ratio = 0.0
            else:
                sharpe_ratio = 0.0
            
            # 计算最大回撤
            if len(self.pnl_history) >= 2:
                pnl_values = [pnl for _, pnl in self.pnl_history]
                max_drawdown = self.calculate_max_drawdown(pnl_values)
            else:
                max_drawdown = 0.0
            
            return {
                "total_pnl": total_pnl,
                "realized_pnl": realized_pnl,
                "unrealized_pnl": unrealized_pnl,
                "total_fees": total_fees,
                "roi_percentage": roi_percentage,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown
            }
            
        except Exception as e:
            self.logger.error(f"Error getting financial metrics: {e}")
            return {
                "total_pnl": 0.0,
                "realized_pnl": 0.0,
                "unrealized_pnl": 0.0,
                "total_fees": 0.0,
                "roi_percentage": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0
            }
    
    async def get_arbitrage_metrics(self) -> Dict:
        """获取套利特定指标"""
        try:
            if not self.order_manager:
                return {
                    "opportunities": 0,
                    "successful": 0,
                    "failed": 0,
                    "average_spread_captured": 0.0
                }
            
            stats = self.order_manager.get_performance_stats()
            
            # 计算平均捕获的价差
            successful_orders = [order for order in self.order_manager.get_completed_orders() 
                               if order.status.value == 'completed']
            
            if successful_orders:
                avg_spread = statistics.mean([abs(order.target_spread) for order in successful_orders])
            else:
                avg_spread = 0.0
            
            return {
                "opportunities": stats.get('total_orders', 0),
                "successful": stats.get('successful_orders', 0),
                "failed": stats.get('failed_orders', 0),
                "average_spread_captured": avg_spread
            }
            
        except Exception as e:
            self.logger.error(f"Error getting arbitrage metrics: {e}")
            return {
                "opportunities": 0,
                "successful": 0,
                "failed": 0,
                "average_spread_captured": 0.0
            }
    
    def calculate_max_drawdown(self, equity_curve: List[float]) -> float:
        """计算最大回撤"""
        if not equity_curve:
            return 0.0
        
        peak = equity_curve[0]
        max_drawdown = 0.0
        
        for value in equity_curve:
            if value > peak:
                peak = value
            elif peak > 0:
                drawdown = (peak - value) / peak
                max_drawdown = max(max_drawdown, drawdown)
        
        return max_drawdown
    
    async def create_performance_snapshot(self) -> PerformanceSnapshot:
        """创建性能快照"""
        try:
            current_time = time.time()

            # 获取各类指标
            system_metrics = await self.get_system_metrics()
            trading_metrics = await self.get_trading_metrics()
            financial_metrics = await self.get_financial_metrics()
            arbitrage_metrics = await self.get_arbitrage_metrics()
            connection_metrics = await self.get_connection_metrics()

            # 创建快照
            snapshot = PerformanceSnapshot(
                timestamp=current_time,
                cpu_usage=system_metrics["cpu_usage"],
                memory_usage=system_metrics["memory_usage"],
                network_latency_ms=system_metrics["network_latency_ms"],
                orders_per_minute=trading_metrics["orders_per_minute"],
                success_rate=trading_metrics["success_rate"],
                average_execution_time_ms=trading_metrics["average_execution_time_ms"],
                hedge_delay_ms=trading_metrics["hedge_delay_ms"],
                total_pnl=financial_metrics["total_pnl"],
                realized_pnl=financial_metrics["realized_pnl"],
                unrealized_pnl=financial_metrics["unrealized_pnl"],
                total_fees=financial_metrics["total_fees"],
                roi_percentage=financial_metrics["roi_percentage"],
                sharpe_ratio=financial_metrics["sharpe_ratio"],
                max_drawdown=financial_metrics["max_drawdown"],
                arbitrage_opportunities=arbitrage_metrics["opportunities"],
                successful_arbitrages=arbitrage_metrics["successful"],
                failed_arbitrages=arbitrage_metrics["failed"],
                average_spread_captured=arbitrage_metrics["average_spread_captured"],
                connection_quality_score=connection_metrics["quality_score"],
                total_reconnects=connection_metrics["total_reconnects"],
                avg_response_time_ms=connection_metrics["avg_response_time_ms"]
            )
            
            # 记录PnL历史
            self.pnl_history.append((current_time, financial_metrics["total_pnl"]))
            if len(self.pnl_history) > self.max_snapshots:
                self.pnl_history.pop(0)
            
            return snapshot
            
        except Exception as e:
            self.logger.error(f"Error creating performance snapshot: {e}")
            return PerformanceSnapshot(timestamp=time.time())
    
    async def update_performance_snapshot(self):
        """更新性能快照"""
        try:
            snapshot = await self.create_performance_snapshot()
            
            self.snapshots.append(snapshot)
            self.last_snapshot = snapshot
            self.total_snapshots += 1
            
            # 清理旧快照
            if len(self.snapshots) > self.max_snapshots:
                self.snapshots = self.snapshots[-self.max_snapshots:]
            
            # 检查里程碑
            await self._check_milestones(snapshot)
            
            # 调用回调函数
            for callback in self.performance_update_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(snapshot)
                    else:
                        callback(snapshot)
                except Exception as e:
                    self.logger.error(f"Error in performance update callback: {e}")
            
            if DEBUG:
                self.logger.debug(f"Performance snapshot updated: PnL=${snapshot.total_pnl:.2f}, "
                                f"ROI={snapshot.roi_percentage:.2f}%")
            
        except Exception as e:
            self.logger.error(f"Error updating performance snapshot: {e}")
    
    async def _check_milestones(self, snapshot: PerformanceSnapshot):
        """检查里程碑"""
        try:
            milestones = []
            
            # PnL里程碑
            if snapshot.total_pnl >= 1000 and not hasattr(self, '_milestone_1k_pnl'):
                milestones.append({"type": "pnl", "milestone": "1k", "value": snapshot.total_pnl})
                self._milestone_1k_pnl = True
            
            # ROI里程碑
            if snapshot.roi_percentage >= 10.0 and not hasattr(self, '_milestone_10pct_roi'):
                milestones.append({"type": "roi", "milestone": "10%", "value": snapshot.roi_percentage})
                self._milestone_10pct_roi = True
            
            # 成功率里程碑
            if snapshot.success_rate >= 95.0 and not hasattr(self, '_milestone_95pct_success'):
                milestones.append({"type": "success_rate", "milestone": "95%", "value": snapshot.success_rate})
                self._milestone_95pct_success = True
            
            # 调用里程碑回调
            for milestone in milestones:
                for callback in self.milestone_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(milestone)
                        else:
                            callback(milestone)
                    except Exception as e:
                        self.logger.error(f"Error in milestone callback: {e}")
            
        except Exception as e:
            self.logger.error(f"Error checking milestones: {e}")
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.is_running:
            self.logger.warning("Performance monitoring is already running")
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        # 🔥 启动连接监控
        self.connection_monitor_thread = threading.Thread(target=self._connection_monitoring_loop, daemon=True)
        self.connection_monitor_thread.start()

        self.logger.info("Performance monitoring started (including connection monitoring)")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.is_running = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)

        if self.connection_monitor_thread:
            self.connection_monitor_thread.join(timeout=10)

        self.logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """监控循环"""
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()
        
        while self.is_running:
            try:
                loop.run_until_complete(self.update_performance_snapshot())
                time.sleep(self.snapshot_interval)
            except Exception as e:
                self.logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(self.snapshot_interval)
        
        loop.close()

    def _connection_monitoring_loop(self):
        """🔥 连接监控循环"""
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()

        while self.is_running:
            try:
                loop.run_until_complete(self._check_all_connections())
                time.sleep(self.connection_check_interval)
            except Exception as e:
                self.logger.error(f"Error in connection monitoring loop: {e}")
                time.sleep(self.connection_check_interval)

        loop.close()

    async def _check_all_connections(self):
        """🔥 检查所有连接状态"""
        try:
            # 这里可以添加实际的连接检查逻辑
            # 例如：检查WebSocket连接、测试API响应等

            # 获取交易所实例（如果可用）
            try:
                from core.arbitrage_engine import get_arbitrage_engine
                engine = get_arbitrage_engine()

                if engine and hasattr(engine, 'exchanges') and engine.exchanges:
                    for exchange_name, exchange_instance in engine.exchanges.items():
                        # 测试交易所速度
                        await self.test_exchange_speed(exchange_name, exchange_instance)

            except Exception as e:
                self.logger.debug(f"Could not access exchanges for connection check: {e}")

            # 清理旧的历史记录
            self._cleanup_connection_history()

        except Exception as e:
            self.logger.error(f"Error checking connections: {e}")

    def _cleanup_connection_history(self):
        """🔥 清理旧的连接历史记录"""
        try:
            cutoff_time = time.time() - (24 * 3600)  # 保留24小时

            # 清理重连历史
            self.reconnect_history = [r for r in self.reconnect_history if r[0] >= cutoff_time]

            # 清理响应时间历史
            self.response_time_history = [r for r in self.response_time_history if r[0] >= cutoff_time]

        except Exception as e:
            self.logger.error(f"Error cleaning up connection history: {e}")
    
    def get_current_performance(self) -> Optional[Dict]:
        """获取当前性能数据"""
        if self.last_snapshot:
            return self.last_snapshot.to_dict()
        return None
    
    def get_performance_history(self, hours: int = 24) -> List[Dict]:
        """获取性能历史数据"""
        cutoff_time = time.time() - (hours * 3600)
        
        recent_snapshots = [
            snapshot for snapshot in self.snapshots
            if snapshot.timestamp >= cutoff_time
        ]
        
        return [snapshot.to_dict() for snapshot in recent_snapshots]
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.last_snapshot:
            return {}
        
        # 计算运行时间
        running_hours = (time.time() - self.start_time) / 3600
        
        # 计算日均PnL
        daily_pnl = self.last_snapshot.total_pnl / max(running_hours / 24, 1)
        
        # 计算最近24小时的表现
        recent_snapshots = self.get_performance_history(24)
        if len(recent_snapshots) >= 2:
            pnl_24h_change = recent_snapshots[-1]["financial"]["total_pnl"] - recent_snapshots[0]["financial"]["total_pnl"]
        else:
            pnl_24h_change = 0.0
        
        return {
            "current_pnl": self.last_snapshot.total_pnl,
            "current_roi": self.last_snapshot.roi_percentage,
            "daily_average_pnl": daily_pnl,
            "pnl_24h_change": pnl_24h_change,
            "success_rate": self.last_snapshot.success_rate,
            "total_trades": len(self.trade_records),
            "running_hours": running_hours,
            "sharpe_ratio": self.last_snapshot.sharpe_ratio,
            "max_drawdown": self.last_snapshot.max_drawdown,
            "last_update": self.last_snapshot.timestamp
        }
    
    def get_trading_analysis(self, hours: int = 24) -> Dict:
        """获取交易分析"""
        cutoff_time = time.time() - (hours * 3600)
        recent_trades = [t for t in self.trade_records if t.timestamp >= cutoff_time]
        
        if not recent_trades:
            return {
                "total_trades": 0,
                "total_volume": 0.0,
                "total_fees": 0.0,
                "average_execution_time": 0.0,
                "best_trade": None,
                "worst_trade": None,
                "exchange_distribution": {},
                "symbol_distribution": {}
            }
        
        # 基本统计
        total_volume = sum(t.quantity * t.price for t in recent_trades)
        total_fees = sum(t.fee for t in recent_trades)
        avg_execution_time = statistics.mean([t.execution_time_ms for t in recent_trades])
        
        # 最佳和最差交易
        best_trade = max(recent_trades, key=lambda t: t.pnl) if recent_trades else None
        worst_trade = min(recent_trades, key=lambda t: t.pnl) if recent_trades else None
        
        # 交易所分布
        exchange_dist = {}
        for trade in recent_trades:
            exchange_dist[trade.exchange] = exchange_dist.get(trade.exchange, 0) + 1
        
        # 交易对分布
        symbol_dist = {}
        for trade in recent_trades:
            symbol_dist[trade.symbol] = symbol_dist.get(trade.symbol, 0) + 1
        
        return {
            "total_trades": len(recent_trades),
            "total_volume": total_volume,
            "total_fees": total_fees,
            "average_execution_time": avg_execution_time,
            "best_trade": best_trade.to_dict() if best_trade else None,
            "worst_trade": worst_trade.to_dict() if worst_trade else None,
            "exchange_distribution": exchange_dist,
            "symbol_distribution": symbol_dist
        }
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        return {
            "is_running": self.is_running,
            "total_snapshots": self.total_snapshots,
            "total_trades_recorded": self.total_trades_recorded,
            "snapshots_stored": len(self.snapshots),
            "trades_stored": len(self.trade_records),
            "pnl_history_points": len(self.pnl_history),
            "monitoring_duration_hours": (time.time() - self.start_time) / 3600,
            "initial_balance": self.initial_balance
        }
    
    def export_performance_data(self, filename: Optional[str] = None) -> str:
        """导出性能数据"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"performance_data_{timestamp}.json"
            
            data = {
                "export_time": time.time(),
                "initial_balance": self.initial_balance,
                "start_time": self.start_time,
                "snapshots": [snapshot.to_dict() for snapshot in self.snapshots],
                "trades": [trade.to_dict() for trade in self.trade_records],
                "pnl_history": self.pnl_history,
                "summary": self.get_performance_summary(),
                "stats": self.get_performance_stats()
            }
            
            # 🚀 手动极限优化：优化文件写入，避免阻塞
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Performance data exported to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")
            return ""
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        try:
            if not self.last_snapshot:
                return "No performance data available"
            
            summary = self.get_performance_summary()
            analysis = self.get_trading_analysis()
            connection_stats = self.get_connection_statistics()

            report = f"""
=== 套利系统性能报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 财务表现
- 总盈亏: ${summary.get('current_pnl', 0):.2f}
- 投资回报率: {summary.get('current_roi', 0):.2f}%
- 日均盈亏: ${summary.get('daily_average_pnl', 0):.2f}
- 24小时变化: ${summary.get('pnl_24h_change', 0):.2f}
- 夏普比率: {summary.get('sharpe_ratio', 0):.3f}
- 最大回撤: {summary.get('max_drawdown', 0):.2%}

📈 交易表现
- 成功率: {summary.get('success_rate', 0):.1f}%
- 总交易数: {summary.get('total_trades', 0)}
- 运行时长: {summary.get('running_hours', 0):.1f}小时
- 平均执行时间: {self.last_snapshot.average_execution_time_ms:.1f}ms
- 平均对冲延迟: {self.last_snapshot.hedge_delay_ms:.1f}ms

🏢 交易分布
- 总交易量: ${analysis.get('total_volume', 0):,.2f}
- 总手续费: ${analysis.get('total_fees', 0):.2f}
- 交易所分布: {analysis.get('exchange_distribution', {})}
- 交易对分布: {analysis.get('symbol_distribution', {})}

💻 系统状态
- CPU使用率: {self.last_snapshot.cpu_usage:.1f}%
- 内存使用率: {self.last_snapshot.memory_usage:.1f}%
- 网络延迟: {self.last_snapshot.network_latency_ms:.1f}ms

🌐 连接监控
- 连接质量评分: {self.last_snapshot.connection_quality_score:.1f}/100
- 总重连次数: {self.last_snapshot.total_reconnects}
- 平均响应时间: {self.last_snapshot.avg_response_time_ms:.1f}ms
- 活跃连接数: {connection_stats.get('connected_count', 0)}/{connection_stats.get('total_connections', 0)}
- 24小时重连次数: {connection_stats.get('total_reconnects', 0)}

🎯 套利统计
- 套利机会: {self.last_snapshot.arbitrage_opportunities}
- 成功套利: {self.last_snapshot.successful_arbitrages}
- 失败套利: {self.last_snapshot.failed_arbitrages}
- 平均价差: {self.last_snapshot.average_spread_captured:.4f}

=== 报告结束 ===
            """
            
            return report.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
            return f"Error generating report: {e}"
    
    def cleanup_old_data(self, hours: int = 168):  # 默认保留一周数据
        """清理旧数据"""
        cutoff_time = time.time() - (hours * 3600)
        
        # 清理旧快照
        old_snapshot_count = len(self.snapshots)
        self.snapshots = [s for s in self.snapshots if s.timestamp >= cutoff_time]
        
        # 清理旧交易记录
        old_trade_count = len(self.trade_records)
        self.trade_records = [t for t in self.trade_records if t.timestamp >= cutoff_time]
        
        # 清理PnL历史
        old_pnl_count = len(self.pnl_history)
        self.pnl_history = [p for p in self.pnl_history if p[0] >= cutoff_time]
        
        cleaned_snapshots = old_snapshot_count - len(self.snapshots)
        cleaned_trades = old_trade_count - len(self.trade_records)
        cleaned_pnl = old_pnl_count - len(self.pnl_history)
        
        if cleaned_snapshots > 0 or cleaned_trades > 0 or cleaned_pnl > 0:
            self.logger.info(f"Cleaned up old data: {cleaned_snapshots} snapshots, "
                           f"{cleaned_trades} trades, {cleaned_pnl} PnL points")

    def record_execution_time(self, operation: str, duration_ms: float) -> None:
        """
        记录执行时间 - 全流程工作流阶段5要求的方法
        """
        timestamp = time.time()
        
        # 更新统计数据
        if not hasattr(self, 'execution_times'):
            self.execution_times = {}
        
        if operation not in self.execution_times:
            self.execution_times[operation] = []
        
        self.execution_times[operation].append({
            'timestamp': timestamp,
            'duration_ms': duration_ms
        })
        
        # 保持最近1000条记录
        if len(self.execution_times[operation]) > 1000:
            self.execution_times[operation] = self.execution_times[operation][-1000:]
        
        if DEBUG:
            self.logger.debug(f"记录执行时间: {operation} = {duration_ms:.2f}ms")

    def record_arbitrage_result(self, profit: float, duration_ms: float) -> None:
        """
        记录套利结果 - 全流程工作流阶段5要求的方法
        """
        timestamp = time.time()
        
        # 记录到PnL历史
        self.pnl_history.append((timestamp, profit))
        
        # 更新套利统计
        if not hasattr(self, 'arbitrage_results'):
            self.arbitrage_results = []
        
        result = {
            'timestamp': timestamp,
            'profit': profit,
            'duration_ms': duration_ms,
            'success': profit > 0
        }
        
        self.arbitrage_results.append(result)
        
        # 保持最近1000条记录
        if len(self.arbitrage_results) > 1000:
            self.arbitrage_results = self.arbitrage_results[-1000:]
        
        # 触发性能更新回调
        for callback in self.performance_update_callbacks:
            try:
                callback(result)
            except Exception as e:
                self.logger.error(f"性能更新回调失败: {e}")
        
        if DEBUG:
            self.logger.debug(f"记录套利结果: profit={profit:.4f}, duration={duration_ms:.2f}ms")


# 性能分析辅助函数
def calculate_volatility(prices: List[float]) -> float:
    """计算价格波动率"""
    if len(prices) < 2:
        return 0.0
    
    returns = []
    for i in range(1, len(prices)):
        if prices[i-1] != 0:
            returns.append((prices[i] - prices[i-1]) / prices[i-1])
    
    if not returns:
        return 0.0
    
    return statistics.stdev(returns) if len(returns) > 1 else 0.0

def calculate_information_ratio(portfolio_returns: List[float], 
                              benchmark_returns: List[float]) -> float:
    """计算信息比率"""
    if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) < 2:
        return 0.0
    
    excess_returns = [p - b for p, b in zip(portfolio_returns, benchmark_returns)]
    
    if not excess_returns:
        return 0.0
    
    mean_excess = statistics.mean(excess_returns)
    std_excess = statistics.stdev(excess_returns) if len(excess_returns) > 1 else 0.0
    
    return mean_excess / std_excess if std_excess != 0 else 0.0

def calculate_calmar_ratio(total_return: float, max_drawdown: float) -> float:
    """计算卡玛比率"""
    if max_drawdown == 0:
        return float('inf') if total_return > 0 else 0.0
    
    return total_return / max_drawdown

# 测试和调试功能


async def test_performance_monitor():
    """测试性能监控器"""
    logger.info("Testing PerformanceMonitor...")
    
    # 创建模拟组件
    class MockOrderManager:
        def get_performance_stats(self):
            return {
                "total_orders": 150,
                "successful_orders": 142,
                "failed_orders": 8,
                "success_rate": 94.7,
                "average_hedge_delay_ms": 75.5
            }
        
        def get_completed_orders(self):
            from trading.order_manager import ArbitrageOrder, ArbitrageType, ArbitrageStatus
            
            # 创建模拟订单
            orders = []
            for i in range(5):
                order = ArbitrageOrder(
                    id=f"test_order_{i}",
                    arbitrage_type=ArbitrageType.SPOT_FUTURES,
                    status=ArbitrageStatus.COMPLETED,
                    spot_symbol="BTCUSDT",
                    futures_symbol="BTCUSDT",
                    spot_exchange="gate",
                    futures_exchange="bybit",
                    target_quantity=0.1,
                    target_spread=0.003 + i * 0.001
                )
                orders.append(order)
            
            return orders
    
    class MockPositionMonitor:
        def get_all_positions(self):
            return {
                "futures_positions": {
                    "bybit": {
                        "BTCUSDT": {
                            "unrealized_pnl": 25.5
                        }
                    }
                }
            }
    
    class MockRiskMonitor:
        pass
    
    # 创建性能监控器
    order_manager = MockOrderManager()
    position_monitor = MockPositionMonitor()
    risk_monitor = MockRiskMonitor()
    
    performance_monitor = PerformanceMonitor(order_manager, position_monitor, risk_monitor)
    performance_monitor.set_initial_balance(10000.0)
    
    # 添加回调函数
    def performance_update_callback(snapshot):
        logger.info(f"Performance update: PnL=${snapshot.total_pnl:.2f}, ROI={snapshot.roi_percentage:.2f}%")
    
    def milestone_callback(milestone):
        logger.info(f"Milestone achieved: {milestone}")
    
    performance_monitor.add_performance_update_callback(performance_update_callback)
    performance_monitor.add_milestone_callback(milestone_callback)
    
    # 记录一些模拟交易
    for i in range(10):
        mock_order_result = {
            "order_id": f"test_order_{i}",
            "symbol": "BTCUSDT",
            "side": "buy" if i % 2 == 0 else "sell",
            "executed_quantity": 0.1,
            "executed_price": 50000 + i * 10,
            "fee": 0.5,
            "type": "market"
        }
        performance_monitor.record_trade(mock_order_result, "gate", 120.5)
        
        # 等待一点时间
        await asyncio.sleep(0.01)
    
    # 创建性能快照
    snapshot = await performance_monitor.create_performance_snapshot()
    assert snapshot.timestamp > 0, "Snapshot should have valid timestamp"
    
    # 测试性能摘要
    summary = performance_monitor.get_performance_summary()
    assert "current_pnl" in summary, "Should have current PnL"
    
    # 测试交易分析
    analysis = performance_monitor.get_trading_analysis()
    assert analysis["total_trades"] == 10, "Should have 10 trades"
    
    # 测试报告生成
    report = performance_monitor.generate_performance_report()
    assert "套利系统性能报告" in report, "Should generate valid report"
    
    # 测试数据导出
    filename = performance_monitor.export_performance_data()
    assert filename.endswith('.json'), "Should export JSON file"
    
    logger.info("✅ PerformanceMonitor test completed successfully")

if __name__ == '__main__':
    asyncio.run(test_performance_monitor())