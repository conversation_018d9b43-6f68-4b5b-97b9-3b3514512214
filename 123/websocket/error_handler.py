"""
WebSocket统一错误处理器
根据08文档标准实现的错误分类和处理机制
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from .websocket_logger import (get_websocket_logger, log_websocket_error_recovery,
                              log_websocket_silent_disconnect, log_websocket_subscription_failure)

class ErrorType(Enum):
    """错误类型枚举"""
    CONNECTION_ERROR = "connection_error"
    SUBSCRIPTION_ERROR = "subscription_error"
    DATA_ERROR = "data_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHENTICATION_ERROR = "authentication_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class ErrorEvent:
    """错误事件数据类"""
    timestamp: float
    error_type: ErrorType
    exchange: str
    error_message: str
    exception: Optional[Exception] = None
    retry_count: int = 0
    resolved: bool = False

class UnifiedErrorHandler:
    """统一错误处理器
    
    根据08文档标准实现：
    - 错误分类: connection, subscription, data, rate_limit
    - 重连策略: 指数退避 (1s, 2s, 4s, 8s, 16s)
    - 超时设置: 连接10s，心跳30s，订阅15-25s
    - 可靠性: 错误率 < 0.1%, 在线时间 > 99.9%
    """
    
    def __init__(self):
        self.logger = logging.getLogger("websocket.error_handler")
        
        # 错误分类映射
        self.error_classification = {
            # 连接错误
            "ConnectionClosed": ErrorType.CONNECTION_ERROR,
            "ConnectionTimeout": ErrorType.CONNECTION_ERROR,
            "ConnectionRefused": ErrorType.CONNECTION_ERROR,
            "ConnectionResetError": ErrorType.CONNECTION_ERROR,
            "ConnectionError": ErrorType.CONNECTION_ERROR,  # 🔥 新增：标准ConnectionError
            "WebSocketConnectionClosedException": ErrorType.CONNECTION_ERROR,
            "WebSocketTimeoutException": ErrorType.CONNECTION_ERROR,
            
            # 订阅错误
            "InvalidChannel": ErrorType.SUBSCRIPTION_ERROR,
            "SubscriptionFailed": ErrorType.SUBSCRIPTION_ERROR,
            "60018": ErrorType.SUBSCRIPTION_ERROR,  # OKX交易对不存在
            "10001": ErrorType.SUBSCRIPTION_ERROR,  # Bybit参数错误
            "10016": ErrorType.SUBSCRIPTION_ERROR,  # Bybit订阅频道过多
            "10017": ErrorType.SUBSCRIPTION_ERROR,  # Bybit订阅参数错误
            
            # 数据错误
            "InvalidData": ErrorType.DATA_ERROR,
            "ParseError": ErrorType.DATA_ERROR,
            "JSONDecodeError": ErrorType.DATA_ERROR,
            "ValueError": ErrorType.DATA_ERROR,
            "KeyError": ErrorType.DATA_ERROR,
            
            # 限流错误
            "TooManyRequests": ErrorType.RATE_LIMIT_ERROR,
            "RateLimitExceeded": ErrorType.RATE_LIMIT_ERROR,
            "60014": ErrorType.RATE_LIMIT_ERROR,  # OKX请求过于频繁
            
            # 认证错误
            "AuthenticationFailed": ErrorType.AUTHENTICATION_ERROR,
            "InvalidApiKey": ErrorType.AUTHENTICATION_ERROR,
            "60004": ErrorType.AUTHENTICATION_ERROR,  # OKX无效时间戳
        }
        
        # 重连策略配置
        self.reconnect_strategies = {
            ErrorType.CONNECTION_ERROR: {
                "backoff_delays": [1, 2, 4, 8, 16],  # 指数退避
                "max_attempts": 5,
                "immediate_retry": False
            },
            ErrorType.SUBSCRIPTION_ERROR: {
                "backoff_delays": [2, 5, 10, 20],  # 🔥 优化：增加更长的重试间隔
                "max_attempts": 4,  # 增加重试次数
                "immediate_retry": False,
                "description": "WebSocket订阅错误恢复策略"
            },
            ErrorType.DATA_ERROR: {
                "backoff_delays": [0.5, 1, 2],  # 快速重试
                "max_attempts": 3,
                "immediate_retry": True
            },
            ErrorType.RATE_LIMIT_ERROR: {
                "backoff_delays": [10, 30, 60, 120],  # 🔥 优化：更渐进的限速恢复策略
                "max_attempts": 4,  # 增加重试次数
                "immediate_retry": False,
                "description": "REST API限速错误恢复策略"
            },
            ErrorType.AUTHENTICATION_ERROR: {
                "backoff_delays": [60],  # 长时间等待，通常需要人工干预
                "max_attempts": 1,
                "immediate_retry": False
            }
        }
        
        # 错误事件记录
        self.error_events: List[ErrorEvent] = []
        self.error_handlers: Dict[ErrorType, List[Callable]] = {
            error_type: [] for error_type in ErrorType
        }
        
        # 性能监控
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {error_type.value: 0 for error_type in ErrorType},
            "errors_by_exchange": {},
            "recovery_success_rate": 0.0
        }
    
    def classify_error(self, error: Exception, error_code: str = "") -> ErrorType:
        """分类错误类型"""
        error_name = type(error).__name__
        
        # 优先检查错误码
        if error_code and error_code in self.error_classification:
            return self.error_classification[error_code]
        
        # 检查异常类型
        if error_name in self.error_classification:
            return self.error_classification[error_name]
        
        # 检查错误消息中的关键词
        error_message = str(error).lower()
        if "connection" in error_message or "connect" in error_message:
            return ErrorType.CONNECTION_ERROR
        elif "timeout" in error_message:
            return ErrorType.CONNECTION_ERROR
        elif "json" in error_message or "parse" in error_message:
            return ErrorType.DATA_ERROR
        elif "rate" in error_message or "limit" in error_message:
            return ErrorType.RATE_LIMIT_ERROR
        elif "auth" in error_message or "permission" in error_message:
            return ErrorType.AUTHENTICATION_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    def record_error(self, exchange: str, error: Exception, error_code: str = "") -> ErrorEvent:
        """记录错误事件"""
        error_type = self.classify_error(error, error_code)
        
        error_event = ErrorEvent(
            timestamp=time.time(),
            error_type=error_type,
            exchange=exchange,
            error_message=str(error),
            exception=error
        )
        
        self.error_events.append(error_event)
        
        # 更新统计
        self.error_stats["total_errors"] += 1
        self.error_stats["errors_by_type"][error_type.value] += 1
        if exchange not in self.error_stats["errors_by_exchange"]:
            self.error_stats["errors_by_exchange"][exchange] = 0
        self.error_stats["errors_by_exchange"][exchange] += 1
        
        # 记录日志
        self.logger.error(f"[{exchange}] {error_type.value}: {error}")
        
        return error_event
    
    async def handle_error(self, exchange: str, error: Exception, error_code: str = "",
                          context: Dict[str, Any] = None) -> bool:
        """处理错误并尝试恢复"""
        error_event = self.record_error(exchange, error, error_code)

        # 🔥 新增：记录到WebSocket专用错误恢复日志
        log_websocket_error_recovery("warning", f"开始处理错误",
                                   exchange=exchange, error_type=error_event.error_type.value,
                                   error_message=str(error), error_code=error_code)
        
        # 调用注册的错误处理器
        for handler in self.error_handlers[error_event.error_type]:
            try:
                await handler(error_event, context or {})
            except Exception as e:
                self.logger.error(f"错误处理器执行失败: {e}")
        
        # 根据错误类型决定是否重试
        strategy = self.reconnect_strategies.get(error_event.error_type)
        if not strategy:
            return False
        
        # 执行重连策略
        return await self._execute_recovery_strategy(error_event, strategy, context or {})
    
    async def _execute_recovery_strategy(self, error_event: ErrorEvent, strategy: Dict, 
                                       context: Dict[str, Any]) -> bool:
        """执行恢复策略"""
        if strategy["immediate_retry"] and error_event.retry_count == 0:
            # 立即重试一次
            self.logger.info(f"[{error_event.exchange}] 立即重试恢复...")
            if await self._attempt_recovery(error_event, context):
                error_event.resolved = True
                return True
        
        # 指数退避重试
        for delay in strategy["backoff_delays"]:
            if error_event.retry_count >= strategy["max_attempts"]:
                break
            
            error_event.retry_count += 1
            self.logger.info(f"[{error_event.exchange}] 等待{delay}秒后重试 (尝试 {error_event.retry_count}/{strategy['max_attempts']})")
            
            await asyncio.sleep(delay)
            
            if await self._attempt_recovery(error_event, context):
                error_event.resolved = True
                self._update_recovery_stats(True)
                return True
        
        self.logger.error(f"[{error_event.exchange}] 恢复失败，已达到最大重试次数")
        self._update_recovery_stats(False)
        return False
    
    async def _attempt_recovery(self, error_event: ErrorEvent, context: Dict[str, Any]) -> bool:
        """尝试恢复"""
        try:
            # 根据错误类型执行不同的恢复策略
            if error_event.error_type == ErrorType.CONNECTION_ERROR:
                return await self._recover_connection(error_event.exchange, context)
            elif error_event.error_type == ErrorType.SUBSCRIPTION_ERROR:
                return await self._recover_subscription(error_event.exchange, context)
            elif error_event.error_type == ErrorType.DATA_ERROR:
                return await self._recover_data_processing(error_event.exchange, context)
            elif error_event.error_type == ErrorType.RATE_LIMIT_ERROR:
                return await self._recover_rate_limit(error_event.exchange, context)
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"恢复尝试异常: {e}")
            return False
    
    async def _recover_connection(self, exchange: str, context: Dict[str, Any]) -> bool:
        """恢复连接"""
        # 这里应该调用具体的重连逻辑
        # 由于这是统一错误处理器，具体的重连逻辑应该通过回调函数提供
        reconnect_callback = context.get("reconnect_callback")
        if reconnect_callback:
            return await reconnect_callback(exchange)
        return False
    
    async def _recover_subscription(self, exchange: str, context: Dict[str, Any]) -> bool:
        """恢复订阅"""
        resubscribe_callback = context.get("resubscribe_callback")
        if resubscribe_callback:
            return await resubscribe_callback(exchange)
        return False
    
    async def _recover_data_processing(self, exchange: str, context: Dict[str, Any]) -> bool:
        """恢复数据处理"""
        # 数据错误通常不需要特殊恢复，只需要跳过错误数据
        return True
    
    async def _recover_rate_limit(self, exchange: str, context: Dict[str, Any]) -> bool:
        """恢复限流"""
        # 限流错误需要等待，这里已经通过延迟处理了
        return True
    
    def register_error_handler(self, error_type: ErrorType, handler: Callable):
        """注册错误处理器"""
        self.error_handlers[error_type].append(handler)
    
    def _update_recovery_stats(self, success: bool):
        """更新恢复统计"""
        total_attempts = sum(len([e for e in self.error_events if e.retry_count > 0]))
        successful_recoveries = sum(len([e for e in self.error_events if e.resolved]))
        
        if total_attempts > 0:
            self.error_stats["recovery_success_rate"] = successful_recoveries / total_attempts

    def get_strategy_info(self) -> Dict[str, Any]:
        """🔥 新增：获取错误处理策略信息 - 供诊断脚本使用"""
        strategy_info = {}

        for error_type, strategy in self.reconnect_strategies.items():
            strategy_info[error_type.value] = {
                "has_strategy": True,
                "backoff_delays": strategy["backoff_delays"],
                "max_attempts": strategy["max_attempts"],
                "immediate_retry": strategy["immediate_retry"],
                "description": strategy.get("description", f"{error_type.value}处理策略")
            }

        return {
            "strategies": strategy_info,
            "total_strategies": len(self.reconnect_strategies),
            "has_rate_limit_strategy": ErrorType.RATE_LIMIT_ERROR in self.reconnect_strategies,
            "has_subscription_error_strategy": ErrorType.SUBSCRIPTION_ERROR in self.reconnect_strategies,
            "rate_limit_backoff_delays": self.reconnect_strategies.get(ErrorType.RATE_LIMIT_ERROR, {}).get("backoff_delays", []),
            "subscription_backoff_delays": self.reconnect_strategies.get(ErrorType.SUBSCRIPTION_ERROR, {}).get("backoff_delays", [])
        }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        recent_errors = [e for e in self.error_events if time.time() - e.timestamp < 3600]  # 最近1小时
        
        return {
            "total_errors": self.error_stats["total_errors"],
            "recent_errors_1h": len(recent_errors),
            "errors_by_type": self.error_stats["errors_by_type"],
            "errors_by_exchange": self.error_stats["errors_by_exchange"],
            "recovery_success_rate": self.error_stats["recovery_success_rate"],
            "error_rate_per_hour": len(recent_errors),
            "most_common_error": max(self.error_stats["errors_by_type"].items(),
                                   key=lambda x: x[1])[0] if self.error_stats["total_errors"] > 0 else None
        }

    async def handle_rate_limit_error(self, exchange: str, error: Exception,
                                    context: Dict[str, Any] = None) -> bool:
        """🔥 新增：处理限速错误"""
        self.logger.warning(f"[{exchange}] 限速错误: {error}")

        # 记录限速错误
        await self.handle_error(exchange, error, "RATE_LIMIT", context or {})

        # 获取限速恢复策略
        strategy = self.get_recovery_strategy(ErrorType.RATE_LIMIT_ERROR)

        # 执行限速恢复：等待更长时间
        backoff_delay = strategy["backoff_delays"][0] if strategy["backoff_delays"] else 10.0
        self.logger.info(f"[{exchange}] 限速恢复：等待{backoff_delay}秒...")
        await asyncio.sleep(backoff_delay)

        return True

    async def handle_subscription_error(self, exchange: str, error: Exception,
                                      context: Dict[str, Any] = None) -> bool:
        """🔥 新增：处理订阅错误"""
        self.logger.warning(f"[{exchange}] 订阅错误: {error}")

        # 🔥 新增：记录到WebSocket专用订阅失效日志
        symbol = context.get("symbol", "unknown") if context else "unknown"
        log_websocket_subscription_failure("warning", f"订阅错误发生",
                                         exchange=exchange, symbol=symbol, error_message=str(error))

        # 记录订阅错误
        await self.handle_error(exchange, error, "SUBSCRIPTION", context or {})

        # 获取订阅恢复策略
        strategy = self.get_recovery_strategy(ErrorType.SUBSCRIPTION_ERROR)

        # 执行订阅恢复：重新订阅
        try:
            # 这里可以添加重新订阅的逻辑
            # 目前返回True表示处理完成
            self.logger.info(f"[{exchange}] 订阅错误已处理")
            return True
        except Exception as e:
            self.logger.error(f"[{exchange}] 订阅恢复失败: {e}")
            return False

    def get_recovery_strategy(self, error_type: ErrorType) -> Dict[str, Any]:
        """🔥 新增：获取恢复策略"""
        # 返回预定义的恢复策略
        strategies = {
            ErrorType.CONNECTION_ERROR: {
                "immediate_retry": True,
                "max_attempts": 5,
                "backoff_delays": [1, 2, 5, 10, 20],
                "description": "连接错误恢复策略"
            },
            ErrorType.RATE_LIMIT_ERROR: {
                "immediate_retry": False,
                "max_attempts": 4,
                "backoff_delays": [10, 30, 60, 120],
                "description": "限速错误恢复策略"
            },
            ErrorType.SUBSCRIPTION_ERROR: {
                "immediate_retry": True,
                "max_attempts": 4,
                "backoff_delays": [2, 5, 10, 20],
                "description": "订阅错误恢复策略"
            },
            ErrorType.DATA_ERROR: {
                "immediate_retry": False,
                "max_attempts": 3,
                "backoff_delays": [1, 3, 5],
                "description": "数据错误恢复策略"
            },
            ErrorType.UNKNOWN_ERROR: {
                "immediate_retry": False,
                "max_attempts": 2,
                "backoff_delays": [5, 10],
                "description": "未知错误恢复策略"
            }
        }

        return strategies.get(error_type, strategies[ErrorType.UNKNOWN_ERROR])

# 全局实例
_error_handler = None

def get_unified_error_handler() -> UnifiedErrorHandler:
    """获取统一错误处理器单例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = UnifiedErrorHandler()
    return _error_handler

async def handle_websocket_error(exchange: str, error: Exception, error_code: str = "", 
                                context: Dict[str, Any] = None) -> bool:
    """快速错误处理接口"""
    handler = get_unified_error_handler()
    return await handler.handle_error(exchange, error, error_code, context or {})
