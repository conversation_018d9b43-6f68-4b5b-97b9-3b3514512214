"""
WebSocket客户端基类
为所有交易所WebSocket客户端提供基础功能
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Callable, Optional
from abc import ABC, abstractmethod
import websockets
import sys
import os
from datetime import datetime
import ssl
import inspect

# 尝试导入自定义日志系统
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger
    # 获取websocket模块的日志器
    logger = get_logger("websocket")
    USE_CUSTOM_LOGGER = True
except ImportError:
    # 如果没有找到自定义日志系统，使用标准日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("websocket")
    USE_CUSTOM_LOGGER = False


class WebSocketClient(ABC):
    """WebSocket客户端基类
    :param exchange_name: 交易所名称
    :param settings: 全局配置实例（推荐，所有参数从settings读取）
    """
    
    def __init__(self, exchange_name: str, settings=None):
        self.exchange_name = exchange_name
        self.ws = None
        self.running = False
        self.callbacks = {}
        self.last_message_time = time.time()
        self.connect_time = 0
        self.reconnect_count = 0
        # 参数合规化
        if settings is not None and hasattr(settings, 'system'):
            self.max_reconnect_attempts = getattr(settings.system, 'ws_reconnect_max_attempts', 10)
            self.heartbeat_interval = getattr(settings.system, 'ws_heartbeat_interval', 20)  # 🔥 官方文档修复：Bybit/OKX官方建议20秒
            self.connection_timeout = getattr(settings.system, 'ws_connect_timeout', 10)
            self.connect_warning_threshold = getattr(settings.system, 'ws_connect_timeout', 1.0) / 1000.0
            self.reconnect_delay = getattr(settings.system, 'ws_reconnect_delay', 2.0)  # 添加重连延迟
            self.stats_interval = getattr(settings.system, 'ws_stats_interval', 60)
        else:
            # 兼容测试/无settings用法
            self.max_reconnect_attempts = 10
            self.heartbeat_interval = 20  # 🔥 官方文档修复：Bybit/OKX官方建议20秒
            self.connection_timeout = 10
            self.connect_warning_threshold = 1.0
            self.reconnect_delay = 2.0  # 添加重连延迟
            self.stats_interval = 60
        self.message_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        self.heartbeat_task = None
        self.reconnect_lock = asyncio.Lock()



        # 🔥 新增：集成统一错误处理器
        try:
            from websocket.error_handler import get_unified_error_handler
            self.error_handler = get_unified_error_handler()
        except ImportError:
            self.error_handler = None
            self._log_warning("统一错误处理器不可用")

        self._log_info(f"初始化{exchange_name} WebSocket客户端")

        # 🔥 修复：初始化重连日志文件
        self.reconnect_log_file = f"logs/websocket_reconnect_{datetime.now().strftime('%Y%m%d')}.log"
    
    def _log_debug(self, message: str):
        """输出调试日志 - VPS模式：完全禁用DEBUG输出"""
        # 🔥 VPS修复：完全禁用DEBUG日志，避免污染websocket_prices.log
        pass

    def _log_info(self, message: str):
        """输出信息日志 - VPS模式：只记录关键信息"""
        # 🔥 VPS修复：只记录关键信息，避免刷屏
        if any(keyword in message for keyword in ['连接成功', '订阅完成', '错误', '失败', '启动', '初始化']):
            logger.info(f"[{self.exchange_name}] {message}")

    def _log_warning(self, message: str):
        """输出警告日志"""
        logger.warning(f"[{self.exchange_name}] {message}")
        # 🔥 修复：增强重连和掉线日志记录
        if any(keyword in message for keyword in ['重连', '连接关闭', '连接失败', '掉线', 'reconnect', 'disconnect']):
            self._log_to_reconnect_file(f"WARNING: {message}")

    def _log_error(self, message: str, exc_info=None):
        """输出错误日志"""
        if exc_info:
            logger.error(f"[{self.exchange_name}] {message}", exc_info=exc_info)
        else:
            logger.error(f"[{self.exchange_name}] {message}")
        # 🔥 修复：增强重连和掉线错误日志记录
        if any(keyword in message for keyword in ['重连', '连接', '掉线', 'reconnect', 'disconnect', 'connection']):
            self._log_to_reconnect_file(f"ERROR: {message}")

    def _log_to_reconnect_file(self, message: str):
        """记录重连相关日志到专门文件"""
        try:
            # 确保日志目录存在
            os.makedirs("logs", exist_ok=True)

            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            log_entry = f"{timestamp} [{self.exchange_name}] {message}\n"

            with open(self.reconnect_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            # 避免日志记录失败影响主流程
            pass


    
    def register_callback(self, event: str, callback: Callable):
        """
        注册回调函数
        :param event: 事件名称
        :param callback: 回调函数
        """
        if event not in self.callbacks:
            self.callbacks[event] = []
        self.callbacks[event].append(callback)
        self._log_debug(f"已注册 {event} 的回调函数")
    
    def emit(self, event: str, data: Dict[str, Any]):
        """
        触发事件
        :param event: 事件名称
        :param data: 事件数据
        """
        # 🔥 VPS修复：完全删除所有DEBUG print语句，避免终端刷屏

        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    # 检查是否是协程函数
                    if inspect.iscoroutinefunction(callback):
                        # 如果是协程函数，使用create_task来异步执行
                        asyncio.create_task(callback(data))
                    else:
                        # 普通函数直接调用
                        callback(data)
                except Exception as e:
                    self._log_error(f"执行 {event} 回调时出错: {str(e)}", exc_info=True)
                    self.error_count += 1
                    # 🔥 新增：使用统一错误处理器
                    if self.error_handler:
                        asyncio.create_task(self.error_handler.handle_error(
                            self.exchange_name, e, context={"event": event, "callback": callback.__name__}
                        ))
    
    @abstractmethod
    def get_ws_url(self) -> str:
        """
        获取WebSocket URL
        :return: WebSocket URL
        """
        pass
    
    @abstractmethod
    async def subscribe_channels(self):
        """订阅频道"""
        pass
    
    @abstractmethod
    async def handle_message(self, message: Dict[str, Any]):
        """
        处理消息
        :param message: 消息内容
        """
        pass
    
    @abstractmethod
    async def send_heartbeat(self):
        """发送心跳"""
        pass
    
    async def send(self, message):
        """
        发送WebSocket消息
        
        Args:
            message: 要发送的消息对象（字典、列表或字符串）
            
        Returns:
            bool: 发送是否成功
        """
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket未连接，无法发送消息")
            return False
            
        try:
            # 如果消息是字典或列表，则转换为JSON字符串
            if isinstance(message, (dict, list)):
                message_str = json.dumps(message)
            else:
                message_str = str(message)
                
            await self.ws.send(message_str)
            self._log_debug(f"发送消息: {message_str[:100]}...")
            return True
        except Exception as e:
            self._log_error(f"发送消息失败: {str(e)}", exc_info=True)
            return False
    
    def _log_stats(self):
        """记录统计信息"""
        now = time.time()
        elapsed = now - self.last_stats_time
        if elapsed > 0:
            self._log_debug(f"消息统计: 总计={self.message_count}, 错误={self.error_count}")
            self.message_count = 0
            self.error_count = 0
            self.last_stats_time = now
    
    async def _connect(self):
        """建立WebSocket连接"""
        if self.ws:
            try:
                await self.ws.close()
            except Exception:
                pass
            self.ws = None

        url = self.get_ws_url()
        self._log_info(f"正在连接到 {url}")
        
        try:
            # 设置SSL上下文
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # 直接使用await连接
            start_time = time.time()
            
            try:
                # 使用asyncio.wait_for设置连接超时
                self.ws = await asyncio.wait_for(
                    websockets.connect(
                        url,
                        ssl=ssl_context,
                        ping_interval=None,  # 禁用自动ping，由我们的心跳机制控制
                        ping_timeout=None,
                        close_timeout=5,  # 关闭超时5秒
                        max_size=10 * 1024 * 1024  # 10MB消息大小限制
                    ),
                    timeout=self.connection_timeout
                )
                
                self.connect_time = time.time() - start_time
                
                # 检查连接是否超过警告阈值
                if self.connect_time > self.connect_warning_threshold:
                    self._log_warning(f"连接建立时间过长: {self.connect_time:.3f}秒，超过目标{self.connect_warning_threshold}秒")
                else:
                    self._log_info(f"连接成功，耗时: {self.connect_time:.3f}秒")
                
                # 连接成功，重置计数器和状态
                self.reconnect_count = 0
                self.running = True
                self.last_message_time = time.time()
                
                # 订阅频道
                try:
                    # 🔥 修复：根据网络延迟调整订阅超时时间
                    if self.exchange_name == "GATE":
                        # Gate.io网络延迟高，需要更长时间
                        subscribe_timeout = 25  # 25秒订阅超时
                    else:
                        subscribe_timeout = 15   # 其他交易所15秒

                    success = await asyncio.wait_for(
                        self.subscribe_channels(),
                        timeout=subscribe_timeout
                    )
                    if not success:
                        self._log_warning("频道订阅失败，将重连")
                        await self.ws.close()
                        return False
                except asyncio.TimeoutError:
                    self._log_error(f"频道订阅超时（{subscribe_timeout}秒）")
                    await self.ws.close()
                    return False
                
                return True
                
            except asyncio.TimeoutError:
                self._log_error(f"连接超时，超过{self.connection_timeout}秒")
                self.error_count += 1
                return False
                
        except Exception as e:
            self.connect_time = time.time() - start_time if 'start_time' in locals() else 0
            self._log_error(f"连接错误: {e}", exc_info=True)
            self.error_count += 1

            # 🔥 新增：使用统一错误处理器
            if self.error_handler:
                context = {
                    "reconnect_callback": self._reconnect,
                    "url": self.get_ws_url(),
                    "connect_time": self.connect_time
                }
                asyncio.create_task(self.error_handler.handle_error(self.exchange_name, e, context=context))

            return False
    
    async def _reconnect(self):
        """重连WebSocket"""
        # 使用锁防止并发重连
        async with self.reconnect_lock:
            if not self.running:
                return False
                
            if self.reconnect_count >= self.max_reconnect_attempts:
                # 🔥 修复：永不放弃重连策略 - 重置计数器继续尝试
                self._log_warning(f"达到最大重连次数 ({self.max_reconnect_attempts})，重置计数器继续尝试")
                self.reconnect_count = 0  # 重置计数器
                # 增加更长的等待时间，但不停止重连
                await asyncio.sleep(60)  # 等待1分钟后重新开始重连周期
                # 不设置 self.running = False，继续重连
            
            # 增加重连计数
            self.reconnect_count += 1
            
            # 🔥 修复：渐进式重连策略，针对心跳失败优化
            if self.exchange_name == "GATE":
                # Gate.io使用更温和的重连策略
                delay = min(60, self.reconnect_delay * (1.2 ** min(self.reconnect_count - 1, 8)))
            else:
                # 其他交易所使用标准策略
                delay = min(30, self.reconnect_delay * (1.5 ** min(self.reconnect_count - 1, 5)))
            
            # 记录开始重连的时间
            start_time = time.time()
            self._log_warning(f"将在 {delay:.1f} 秒后尝试重连 (尝试 {self.reconnect_count}/{self.max_reconnect_attempts})")
            await asyncio.sleep(delay)
            
            # 重连前确认客户端仍在运行
            if not self.running:
                return False
                
            # 尝试重连
            connected = await self._connect()
            
            # 记录重连完成的时间
            reconnect_time = time.time() - start_time
            
            # 如果重连时间超过3秒，记录警告
            if reconnect_time > 3.0:
                self._log_warning(f"重连时间过长: {reconnect_time:.3f}秒，超过目标3秒")
            
            if connected:
                self._log_info(f"重连成功，总耗时: {reconnect_time:.3f}秒")
                # 重连成功后重置最后消息时间
                self.last_message_time = time.time()
            else:
                self._log_error(f"重连失败，总耗时: {reconnect_time:.3f}秒")
                
            return connected
    
    async def _handle_raw_message(self, message: str):
        """
        处理原始消息
        :param message: 原始消息字符串
        """
        try:
            # 更新最后消息时间
            self.last_message_time = time.time()
            self.message_count += 1
            
            # 定期记录统计信息
            if time.time() - self.last_stats_time > self.stats_interval:
                # 非异步调用_log_stats，避免产生错误
                self._log_stats()
            
            # 记录原始消息（仅调试级别，只写入日志文件）
            if message and len(message) > 0:
                self._log_debug(f"收到消息: {message[:200]}{'...' if len(message) > 200 else ''}")
            else:
                self._log_warning("收到空消息")
                return
            
            # 解析JSON
            try:
                data = json.loads(message)
                await self.handle_message(data)
            except json.JSONDecodeError:
                self._log_warning(f"无效JSON: {message[:100]}...")
                self.error_count += 1
                
        except Exception as e:
            self._log_error(f"处理消息异常: {e}", exc_info=True)
            self.error_count += 1
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        self._log_debug("启动心跳循环")
        retry_count = 0
        max_heartbeat_failures = 5  # 🔥 修复：增加心跳失败容错，从3次增加到5次
        
        while self.running:
            try:
                now = time.time()
                # 检查是否需要发送心跳
                if now - self.last_message_time > self.heartbeat_interval:
                    self._log_debug("发送心跳...")
                    success = await self.send_heartbeat()
                    if success:
                        self._log_debug("心跳发送成功")
                        # 更新最后消息时间以防止频繁心跳
                        self.last_message_time = time.time()
                        retry_count = 0  # 重置失败计数
                    else:
                        self._log_warning("心跳发送失败，可能连接已断开")
                        retry_count += 1
                        
                        # 如果连续失败达到阈值，尝试重连
                        if retry_count >= max_heartbeat_failures:
                            self._log_error(f"心跳连续失败{max_heartbeat_failures}次，触发重连")
                            if not await self._check_connection():
                                # 如果检查连接失败，不更新时间戳，让重连机制启动
                                pass
                            retry_count = 0  # 重置失败计数
                
                # 🚀 手动极限优化：智能心跳检查，平衡性能和稳定性
                check_interval = min(0.5, self.heartbeat_interval / 20)  # 更高频率检查
                await asyncio.sleep(check_interval)
            except asyncio.CancelledError:
                self._log_debug("心跳循环被取消")
                break
            except Exception as e:
                self._log_error(f"心跳循环异常: {e}", exc_info=True)
                await asyncio.sleep(0.5)  # 🚀 手动极限优化：更快恢复，减少等待时间
    
    async def run(self):
        """运行WebSocket客户端"""
        self.running = True
        self.reconnect_count = 0
        self.message_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        self.last_message_time = time.time()  # 初始化为当前时间
        
        # 首次连接
        connected = await self._connect()
        if not connected:
            connected = await self._reconnect()
            if not connected:
                self._log_error(f"无法建立WebSocket连接，退出")
                self.running = False
                return
        
        # 启动心跳
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.heartbeat_task.set_name(f"heartbeat_{self.exchange_name}")



        try:
            # 主循环
            while self.running and self.ws:
                try:
                    # 设置更合理的超时时间，是心跳间隔的1.5倍
                    timeout = max(30, self.heartbeat_interval * 1.5)
                    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
                    await self._handle_raw_message(message)
                    
                except asyncio.TimeoutError:
                    self._log_debug(f"接收消息超时 ({timeout}秒)，检查连接状态")
                    # 检查是否需要重连
                    now = time.time()
                    if now - self.last_message_time > self.heartbeat_interval * 2:  # 两个心跳周期没有消息，尝试重连
                        self._log_warning(f"{int(now - self.last_message_time)}秒未收到消息，尝试重连")
                        if not await self._reconnect():
                            self.running = False
                            
                except websockets.exceptions.ConnectionClosed as e:
                    self._log_warning(f"WebSocket连接关闭: {e}")
                    
                    if not self.running:
                        break
                    
                    # 重连
                    connected = await self._reconnect()
                    if not connected:
                        break
                        
                except Exception as e:
                    self._log_error(f"WebSocket接收消息异常: {e}", exc_info=True)
                    
                    if not self.running:
                        break
                    
                    # 重连
                    connected = await self._reconnect()
                    if not connected:
                        break
            
        finally:
            # 清理
            self.running = False
            
            # 取消心跳任务
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭连接
            if self.ws:
                await self.ws.close()
                self.ws = None
                
            # 最终统计
            self._log_stats()
            self._log_info(f"WebSocket客户端已关闭")
    
    async def close(self):
        """关闭WebSocket客户端"""
        self.running = False
        
        # 取消心跳任务
        if hasattr(self, 'heartbeat_task') and self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 关闭连接
        if self.ws:
            await self.ws.close()
            self.ws = None
            
        self._log_info(f"WebSocket客户端关闭中...")

    async def _check_connection(self):
        """检查WebSocket连接状态"""
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket连接已关闭")
            return False
        
        # 检查最后消息时间
        now = time.time()
        if now - self.last_message_time > 60:  # 60秒没有消息，认为连接可能已失效
            self._log_warning(f"长时间未收到消息 ({int(now - self.last_message_time)}秒)")
            return False
            
        return True
    
    def is_connected(self) -> bool:
        """
        检查WebSocket是否已连接
        
        Returns:
            bool: 连接状态
        """
        try:
            if not self.ws:
                return False
            
            if not hasattr(self.ws, 'open'):
                return False
                
            return self.ws.open and self.running
        except Exception as e:
            self._log_debug(f"检查连接状态时出错: {e}")
            return False
    
    async def start_connection(self):
        """启动WebSocket连接"""
        if self.running:
            self._log_warning("WebSocket已在运行中")
            return True
            
        try:
            self._log_info("启动WebSocket连接...")
            # 创建连接任务但不等待，让它在后台运行
            self.connection_task = asyncio.create_task(self.run())
            
            # 等待更长时间，让连接建立 - 从0.5秒增加到3秒
            max_wait_time = 3.0
            check_interval = 0.1
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                # 检查连接状态
                if self.is_connected():
                    self._log_info(f"WebSocket连接已建立，耗时: {waited_time:.1f}秒")
                    return True
                
                # 如果连接任务已完成但连接失败，提前退出
                if self.connection_task.done():
                    break
            
            # 超时或连接失败
            self._log_warning(f"WebSocket连接建立失败，等待时间: {waited_time:.1f}秒")
            return False
                
        except Exception as e:
            self._log_error(f"启动WebSocket连接失败: {e}")
            return False

