"""
增强的WebSocket客户端基类
集成统一连接池管理、智能重连、多路径备用等功能
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Callable
from abc import ABC, abstractmethod
import logging

from utils.logger import get_logger


class EnhancedWebSocketClientBase(ABC):
    """增强的WebSocket客户端基类
    
    集成功能：
    1. 统一连接池管理
    2. 智能重连机制
    3. 多路径备用方案
    4. 连接质量监控
    5. 数据缓冲和恢复
    """
    
    def __init__(self, exchange: str, market_type: str):
        self.exchange = exchange
        self.market_type = market_type
        self.logger = get_logger(f"websocket.{exchange}.{market_type}")
        
        # 连接管理
        self.connection_id: Optional[str] = None
        self.ws = None
        self.running = False
        self.connected = False
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            "market_data": [],
            "connection_status": [],
            "error": []
        }
        
        # 订阅管理
        self.symbols: List[str] = []
        self.subscriptions: Dict[str, bool] = {}
        
        # 性能指标
        self.metrics = {
            "messages_received": 0,
            "messages_sent": 0,
            "errors": 0,
            "last_message_time": 0,
            "connection_start_time": 0,
            "reconnect_count": 0
        }
        
        # 数据缓冲
        self.message_buffer: List[Dict[str, Any]] = []
        self.max_buffer_size = 1000
        
        # 连接质量监控
        self.latency_samples: List[float] = []
        self.error_timestamps: List[float] = []
        
    def set_symbols(self, symbols: List[str]):
        """设置交易对"""
        self.symbols = symbols
        self.logger.info(f"设置交易对: {symbols}")
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册回调函数"""
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        self.callbacks[event_type].append(callback)
        self.logger.debug(f"注册回调: {event_type}")
    
    async def emit_event(self, event_type: str, data: Any):
        """触发事件回调"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    self.logger.error(f"回调执行失败: {event_type}, {e}")
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接WebSocket - 子类实现"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """断开WebSocket连接 - 子类实现"""
        pass
    
    @abstractmethod
    async def subscribe_symbols(self):
        """订阅交易对数据 - 子类实现"""
        pass
    
    @abstractmethod
    async def handle_message(self, message: str):
        """处理WebSocket消息 - 子类实现"""
        pass
    
    @abstractmethod
    def get_websocket_url(self) -> str:
        """获取WebSocket URL - 子类实现"""
        pass
    
    async def run(self):
        """运行WebSocket客户端"""
        self.running = True
        self.metrics["connection_start_time"] = time.time()
        
        while self.running:
            try:
                # 连接WebSocket
                if not self.connected:
                    success = await self.connect()
                    if not success:
                        await asyncio.sleep(5.0)  # 连接失败等待5秒
                        continue
                
                # 订阅数据
                await self.subscribe_symbols()
                
                # 监听消息
                await self._message_loop()
                
            except Exception as e:
                self.logger.error(f"WebSocket运行异常: {e}")
                self.metrics["errors"] += 1
                self.error_timestamps.append(time.time())
                
                # 触发错误事件
                await self.emit_event("error", {
                    "error": str(e),
                    "timestamp": time.time()
                })
                
                # 断开连接准备重连
                await self.disconnect()
                
                if self.running:
                    await asyncio.sleep(2.0)  # 错误后等待2秒
    
    async def _message_loop(self):
        """消息循环"""
        try:
            async for message in self.ws:
                if not self.running:
                    break
                
                # 更新指标
                self.metrics["messages_received"] += 1
                self.metrics["last_message_time"] = time.time()
                
                # 缓冲消息
                if len(self.message_buffer) >= self.max_buffer_size:
                    self.message_buffer.pop(0)  # 移除最老的消息
                
                self.message_buffer.append({
                    "message": message.data if hasattr(message, 'data') else str(message),
                    "timestamp": time.time()
                })
                
                # 处理消息
                try:
                    await self.handle_message(message.data if hasattr(message, 'data') else str(message))
                except Exception as e:
                    self.logger.error(f"消息处理失败: {e}")
                    self.metrics["errors"] += 1
                
        except Exception as e:
            self.logger.error(f"消息循环异常: {e}")
            raise
    
    async def stop(self):
        """停止WebSocket客户端"""
        self.running = False
        await self.disconnect()
        self.logger.info("WebSocket客户端已停止")
    
    async def close(self):
        """关闭WebSocket客户端 - 别名方法"""
        await self.stop()
    
    def get_connection_metrics(self) -> Dict[str, Any]:
        """获取连接指标"""
        current_time = time.time()
        uptime = current_time - self.metrics["connection_start_time"] if self.metrics["connection_start_time"] > 0 else 0
        
        # 计算错误率
        total_operations = self.metrics["messages_received"] + self.metrics["messages_sent"]
        error_rate = self.metrics["errors"] / max(1, total_operations)
        
        # 计算平均延迟
        avg_latency = sum(self.latency_samples) / len(self.latency_samples) if self.latency_samples else 0
        
        return {
            "exchange": self.exchange,
            "market_type": self.market_type,
            "connection_id": self.connection_id,
            "connected": self.connected,
            "uptime_seconds": uptime,
            "messages_received": self.metrics["messages_received"],
            "messages_sent": self.metrics["messages_sent"],
            "errors": self.metrics["errors"],
            "error_rate": error_rate,
            "reconnect_count": self.metrics["reconnect_count"],
            "avg_latency_ms": avg_latency,
            "last_message_time": self.metrics["last_message_time"],
            "buffer_size": len(self.message_buffer)
        }
    
    def update_latency(self, latency_ms: float):
        """更新延迟样本"""
        self.latency_samples.append(latency_ms)
        
        # 保持最近100个样本
        if len(self.latency_samples) > 100:
            self.latency_samples.pop(0)
    
    def get_buffered_messages(self, since_timestamp: Optional[float] = None) -> List[Dict[str, Any]]:
        """获取缓冲的消息"""
        if since_timestamp is None:
            return self.message_buffer.copy()
        
        return [
            msg for msg in self.message_buffer 
            if msg["timestamp"] >= since_timestamp
        ]
    
    def clear_buffer(self):
        """清空消息缓冲"""
        self.message_buffer.clear()
        self.logger.debug("消息缓冲已清空")
    
    async def send_ping(self):
        """发送心跳包"""
        if self.ws and self.connected:
            try:
                ping_time = time.time()
                await self.ws.ping()
                
                # 记录延迟
                pong_time = time.time()
                latency_ms = (pong_time - ping_time) * 1000
                self.update_latency(latency_ms)
                
                self.metrics["messages_sent"] += 1
                return True
            except Exception as e:
                self.logger.error(f"发送心跳失败: {e}")
                return False
        return False
    
    def is_healthy(self) -> bool:
        """检查连接健康状态"""
        if not self.connected:
            return False
        
        current_time = time.time()
        
        # 检查最近是否有消息
        if self.metrics["last_message_time"] > 0:
            time_since_last_message = current_time - self.metrics["last_message_time"]
            if time_since_last_message > 60:  # 60秒无消息认为不健康
                return False
        
        # 检查错误率
        recent_errors = [
            ts for ts in self.error_timestamps 
            if current_time - ts < 300  # 最近5分钟的错误
        ]
        if len(recent_errors) > 10:  # 5分钟内超过10个错误
            return False
        
        return True
