# -*- coding: utf-8 -*-
"""
🔥 统一订单簿数据格式化器
优化BaseExchange中的_format_orderbook_data方法
在三个WebSocket处理器中统一使用，消除重复的数据格式化逻辑
"""

import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class OrderbookFormatConfig:
    """订单簿格式化配置 - 🔥 修复：移除中间价配置，统一30档深度"""
    max_depth_levels: int = 30  # 🔥 升级：最大深度档位（30档），与UnifiedOrderSpreadCalculator保持一致
    include_spread: bool = True  # 是否包含价差信息
    price_precision: int = 8  # 价格精度
    volume_precision: int = 6  # 数量精度
    sort_asks_asc: bool = True  # asks按价格升序排列
    sort_bids_desc: bool = True  # bids按价格降序排列


class UnifiedOrderbookFormatter:
    """
    🔥 统一订单簿数据格式化器
    替代BaseExchange和三个WebSocket处理器中的重复格式化逻辑
    """
    
    def __init__(self, config: Optional[OrderbookFormatConfig] = None):
        self.config = config or OrderbookFormatConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def format_orderbook_data(
        self,
        asks: List[Any],
        bids: List[Any],
        symbol: str,
        exchange: str,
        market_type: str = "spot",
        timestamp: Optional[int] = None,
        additional_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        🔥 强化：统一订单簿数据格式化接口 - 完全移除ticker依赖

        Args:
            asks: 卖单数据
            bids: 买单数据
            symbol: 交易对
            exchange: 交易所名称
            market_type: 市场类型
            timestamp: 时间戳
            additional_fields: 额外字段

        Returns:
            Dict[str, Any]: 标准格式的订单簿数据
        """
        try:
            # 🔥 强化：严格验证orderbook数据完整性
            if not asks or not bids:
                raise ValueError(f"orderbook数据不完整: {exchange}_{symbol} asks={len(asks) if asks else 0}, bids={len(bids) if bids else 0}")

            # 🔥 强化：验证数据深度，确保有足够的流动性
            if len(asks) < 5 or len(bids) < 5:
                self.logger.warning(f"⚠️ {exchange}_{symbol} orderbook深度不足: asks={len(asks)}, bids={len(bids)}")

            # 🔥 **时间戳处理策略统一**：优先使用传入的时间戳，避免覆盖
            if timestamp is None:
                # 🔥 只有在没有提供时间戳时才生成新的时间戳
                # 优先使用统一时间戳处理器确保一致性
                from websocket.unified_timestamp_processor import get_synced_timestamp
                timestamp = get_synced_timestamp(exchange, None)
            
            # 1. 格式化asks和bids
            formatted_asks = self._format_price_levels(asks, "asks")
            formatted_bids = self._format_price_levels(bids, "bids")
            
            # 2. 排序确保正确性
            if self.config.sort_asks_asc and formatted_asks:
                formatted_asks.sort(key=lambda x: x[0])  # asks按价格升序
            if self.config.sort_bids_desc and formatted_bids:
                formatted_bids.sort(key=lambda x: x[0], reverse=True)  # bids按价格降序
            
            # 3. 🔥 修复：移除中间价计算，直接使用最优价格
            spread = 0.0
            best_bid = 0.0
            best_ask = 0.0

            if formatted_bids:
                best_bid = formatted_bids[0][0]
            if formatted_asks:
                best_ask = formatted_asks[0][0]

            if best_bid > 0 and best_ask > 0:
                spread = best_ask - best_bid

            # 🔥 修复：使用最优买价作为参考价格，避免中间价导致的计算误差
            reference_price = best_bid if best_bid > 0 else (best_ask if best_ask > 0 else 0.0)
            
            # 4. 🔥 强化：构建标准格式数据，确保完全兼容orderbook
            result = {
                "data_type": "orderbook",  # 🔥 强化：明确标识为orderbook数据
                "symbol": symbol,
                "exchange": exchange.lower(),
                "market_type": market_type,
                "asks": formatted_asks,
                "bids": formatted_bids,
                "timestamp": timestamp,
                "asks_depth": len(formatted_asks),
                "bids_depth": len(formatted_bids)
            }

            # 5. 🔥 修复：移除中间价字段，使用参考价格确保兼容性
            result["price"] = reference_price  # 🔥 修复：使用参考价格而非中间价
            result["best_bid"] = best_bid
            result["best_ask"] = best_ask
            result["spread"] = spread

            # 🔥 修复：基于最优买价计算价差百分比和基点
            if best_bid > 0:
                result["spread_percent"] = (spread / best_bid) * 100
                result["spread_bps"] = (spread / best_bid) * 10000  # 基点
            else:
                result["spread_percent"] = 0.0
                result["spread_bps"] = 0.0

            # 6. 🔥 强化：数据质量检查和标记
            result["incomplete_orderbook"] = not formatted_asks or not formatted_bids
            result["low_liquidity"] = len(formatted_asks) < 5 or len(formatted_bids) < 5

            if result["incomplete_orderbook"]:
                result["incomplete_reason"] = f"asks={len(formatted_asks)}, bids={len(formatted_bids)}"

            # 🔥 强化：添加数据质量评分
            quality_score = 100
            if result["incomplete_orderbook"]:
                quality_score -= 50
            if result["low_liquidity"]:
                quality_score -= 30
            if spread <= 0:
                quality_score -= 20

            result["data_quality_score"] = max(0, quality_score)
            
            # 7. 添加额外字段
            if additional_fields:
                result.update(additional_fields)
            
            return result
            
        except Exception as e:
            self.logger.error(f"订单簿格式化失败: {e}")
            return self._create_empty_orderbook(symbol, exchange, market_type, timestamp)
    
    def _format_price_levels(self, levels: List[Any], side: str) -> List[List[float]]:
        """格式化价格档位数据"""
        formatted_levels = []
        
        try:
            for level in levels[:self.config.max_depth_levels]:
                if isinstance(level, (list, tuple)) and len(level) >= 2:
                    try:
                        price = float(level[0])
                        volume = float(level[1])
                        
                        # 只添加有效的价格档位
                        if price > 0 and volume >= 0:  # 允许数量为0的档位
                            # 应用精度设置
                            price = round(price, self.config.price_precision)
                            volume = round(volume, self.config.volume_precision)
                            formatted_levels.append([price, volume])
                            
                    except (ValueError, TypeError) as e:
                        self.logger.debug(f"{side}数据转换失败: {level}, 错误: {e}")
                        continue
                        
                elif isinstance(level, dict):
                    # 处理字典格式的数据
                    try:
                        price = float(level.get("price", level.get("p", 0)))
                        volume = float(level.get("size", level.get("s", level.get("amount", 0))))
                        
                        if price > 0 and volume >= 0:
                            price = round(price, self.config.price_precision)
                            volume = round(volume, self.config.volume_precision)
                            formatted_levels.append([price, volume])
                            
                    except (ValueError, TypeError) as e:
                        self.logger.debug(f"{side}字典数据转换失败: {level}, 错误: {e}")
                        continue
                else:
                    self.logger.debug(f"{side}数据格式不支持: {level}")
                    continue
            
        except Exception as e:
            self.logger.error(f"格式化{side}数据异常: {e}")
        
        return formatted_levels
    
    def _create_empty_orderbook(
        self,
        symbol: str,
        exchange: str,
        market_type: str,
        timestamp: int
    ) -> Dict[str, Any]:
        """创建空的订单簿数据"""
        return {
            "data_type": "orderbook",  # 🔥 修复：添加数据类型标识
            "symbol": symbol,
            "exchange": exchange.lower(),
            "market_type": market_type,
            "price": 0.0,
            "asks": [],
            "bids": [],
            "timestamp": timestamp,
            "asks_depth": 0,
            "bids_depth": 0,
            "spread": 0.0,
            "best_bid": 0.0,
            "best_ask": 0.0,
            "incomplete_orderbook": True,
            "incomplete_reason": "格式化失败"
        }
    
    def normalize_symbol_format(self, symbol: str, target_format: str = "standard") -> str:
        """
        🔥 统一符号格式标准化
        
        Args:
            symbol: 原始交易对
            target_format: 目标格式 ("standard", "gate", "bybit", "okx")
            
        Returns:
            str: 标准化后的交易对
        """
        # 🔥 修复：复用现有的currency_adapter.normalize_symbol，避免造车轮
        from exchanges.currency_adapter import normalize_symbol

        # 先标准化为统一格式 (BTC-USDT)
        standard_symbol = normalize_symbol(symbol)

        if not standard_symbol or standard_symbol == "UNKNOWN-USDT":
            return symbol  # 返回原始符号

        # 根据目标格式转换
        if target_format == "standard":
            return standard_symbol  # BTC-USDT
        elif target_format == "gate":
            return standard_symbol.replace("-", "_")  # BTC_USDT
        elif target_format == "bybit":
            return standard_symbol.replace("-", "")  # BTCUSDT
        elif target_format == "okx":
            return standard_symbol  # BTC-USDT
        else:
            return standard_symbol  # 默认标准格式


# 🌟 全局实例
_global_formatter = None

def get_orderbook_formatter(config: Optional[OrderbookFormatConfig] = None) -> UnifiedOrderbookFormatter:
    """获取全局订单簿格式化器实例"""
    global _global_formatter
    if _global_formatter is None or config is not None:
        _global_formatter = UnifiedOrderbookFormatter(config)
    return _global_formatter


def format_orderbook_data(
    asks: List[Any],
    bids: List[Any],
    symbol: str,
    exchange: str,
    market_type: str = "spot",
    timestamp: Optional[int] = None,
    additional_fields: Optional[Dict[str, Any]] = None,
    config: Optional[OrderbookFormatConfig] = None
) -> Dict[str, Any]:
    """快速格式化订单簿数据"""
    formatter = get_orderbook_formatter(config)
    return formatter.format_orderbook_data(
        asks, bids, symbol, exchange, market_type, timestamp, additional_fields
    )


def normalize_symbol(symbol: str, target_format: str = "standard") -> str:
    """快速标准化交易对格式"""
    formatter = get_orderbook_formatter()
    return formatter.normalize_symbol_format(symbol, target_format)
