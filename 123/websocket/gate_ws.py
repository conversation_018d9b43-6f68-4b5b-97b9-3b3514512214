#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io WebSocket客户端
支持现货和期货的深度数据
"""

import asyncio
import json
import time
from typing import Dict, Any, List
import logging
from datetime import datetime
import sys
import os

# 尝试导入基类和自定义日志系统
try:
    from websocket.ws_client import WebSocketClient
    logger = logging.getLogger("websocket.gate")
except ImportError:
    # 开发环境导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from websocket.ws_client import WebSocketClient
    logger = logging.getLogger("websocket.gate")


class GateWebSocketClient(WebSocketClient):
    """Gate.io WebSocket客户端"""
    
    def __init__(self, market_type: str = "spot", settings=None):
        """
        初始化
        :param market_type: 市场类型 "spot" 或 "futures"
        :param settings: 全局配置，可选
        """
        super().__init__("GATE", settings)
        self.market_type = market_type
        self.symbols = []  # 交易对列表

        # 🔥 时间同步机制 - 已迁移到统一时间戳处理器
        # 保留这些属性以兼容现有代码
        self.time_offset = 0  # 时间偏移（毫秒）
        self.time_synced = False
        self.last_sync_time = 0

        # Gate.io WebSocket URLs
        self.ws_urls = {
            "spot": "wss://api.gateio.ws/ws/v4/",
            "futures": "wss://fx-ws.gateio.ws/v4/ws/usdt"
        }
        
        # 🚀 优化：只订阅必要的数据类型，删除不必要的trades
        self.channels = ["order_book"]  # 只需要深度数据
        
        # 统计收到的各类消息数量
        self.orderbook_count = 0
        self.trade_count = 0
        
        # 🔥 官方文档修复：Gate.io官方支持5秒心跳间隔
        self.connection_timeout = 10  # 🔥 修复：连接超时统一为10秒，与Bybit、OKX保持一致
        self.heartbeat_interval = 5  # 🔥 官方文档修复：Gate.io官方支持5秒心跳间隔
        self.connect_warning_threshold = 3.0  # 连接警告阈值
        
        self._log_debug(f"初始化 {market_type} WebSocket客户端")
        
    def get_ws_url(self) -> str:
        """获取WebSocket URL"""
        url = self.ws_urls.get(self.market_type, self.ws_urls["spot"])
        self._log_debug(f"使用WebSocket URL: {url}")
        return url
    
    def set_symbols(self, symbols: List[str]):
        """设置要监控的交易对 - 智能格式转换与容错"""
        if not symbols:
            self._log_warning("交易对列表为空，将使用默认交易对")
            symbols = ["BTC-USDT", "ETH-USDT"]  # 默认交易对
            
        # 智能转换为Gate.io格式：BTC-USDT -> BTC_USDT
        converted_symbols = []
        supported_symbols = []  # 支持的交易对
        
        for symbol in symbols:
            try:
                # 统一格式转换
                if '-' in symbol:
                    gate_symbol = symbol.replace('-', '_')
                elif '_' in symbol:
                    gate_symbol = symbol  # 已经是Gate格式
                else:
                    # BTCUSDT -> BTC_USDT
                    if symbol.endswith('USDT') and len(symbol) > 4:
                        base = symbol[:-4]
                        gate_symbol = f"{base}_USDT"
                    else:
                        gate_symbol = symbol
                
                # 容错机制：即使交易对不存在也添加，让WebSocket自然筛选
                converted_symbols.append(gate_symbol)
                supported_symbols.append(gate_symbol)
                
            except Exception as e:
                self._log_warning(f"转换交易对 {symbol} 时发生错误: {e}，跳过该交易对")
                continue
                
        # 如果转换后没有任何交易对，使用默认
        if not converted_symbols:
            self._log_warning("没有有效的交易对，使用默认BTC_USDT")
            converted_symbols = ["BTC_USDT"]
            
        self.symbols = converted_symbols
        self._log_info(f"Gate.io {self.market_type} 支持的交易对: {', '.join(supported_symbols)}")
        
        # 容错说明
        if len(supported_symbols) < len(symbols):
            self._log_info(f"注意: 某些交易对可能不被Gate.io支持，系统将自动跳过不支持的交易对")

    async def _sync_time(self):
        """🔥 已删除：使用统一时间戳处理器替代"""
        # 这个方法已被统一时间戳处理器替代，保留空实现以兼容
        pass



    async def run(self):
        """运行WebSocket客户端 - 重写以添加时间同步"""
        # 🔥 关键修复：确保Gate.io时间同步成功，避免时间戳不一致问题
        from websocket.unified_timestamp_processor import get_timestamp_processor

        processor = get_timestamp_processor("gate")
        sync_success = await processor.sync_time(force=True)  # 强制同步

        if sync_success:
            self._log_info(f"✅ Gate.io时间同步成功，偏移量: {processor.time_offset}ms")
        else:
            self._log_warning(f"⚠️ Gate.io时间同步失败，将使用本地时间（可能导致时间戳不一致）")

        # 调用父类的run方法
        await super().run()

    async def subscribe_channels(self):
        """订阅频道 - 🔥 修复：深度订阅必须逐个，不能批量"""
        if not self.symbols:
            self._log_error("没有设置交易对，无法订阅")
            return False
            
        try:
            self._log_info(f"🚀 开始订阅 {len(self.symbols)} 个交易对")
            
            # 🔥 TICKER订阅已完全移除 - 系统只使用OrderBook数据
            self._log_info(f"🚀 只订阅OrderBook数据，ticker订阅已移除")
            
            # 🔥 第二步：逐个订阅深度数据（Gate.io要求单独订阅）
            self._log_info(f"📈 开始逐个订阅深度数据...")
            
            depth_success_count = 0
            for i, symbol in enumerate(self.symbols):
                self._log_info(f"📊 订阅深度 {i+1}/{len(self.symbols)}: {symbol}")
                
                # 🔥 关键修复：使用官方SDK正确的全量快照频道
                if self.market_type == "spot":
                    depth_msg = {
                        "time": int(time.time()),
                        "channel": "spot.order_book",  # 🔥 修复：使用全量快照频道
                        "event": "subscribe",
                        "payload": [symbol, "10", "100ms"]  # 🔥 修复：现货支持频率参数
                    }
                else:
                    # 🔥 关键修复：期货订单簿只需要交易对名称，根据官方文档示例
                    depth_msg = {
                        "time": int(time.time()),
                        "channel": "futures.order_book",
                        "event": "subscribe",
                        "payload": [symbol]  # 🔥 修复：期货只需要[symbol]格式，参考官方文档
                    }
                
                self._log_debug(f"发送{symbol}深度订阅: {json.dumps(depth_msg)}")
                depth_success = await self.send(depth_msg)
                
                if depth_success:
                    depth_success_count += 1
                    self._log_info(f"✅ {symbol} 深度订阅成功")
                else:
                    self._log_error(f"❌ {symbol} 深度订阅失败")

                    # 🔥 新增：记录订阅失败日志
                    from .websocket_logger import log_websocket_subscription_failure
                    log_websocket_subscription_failure("error", f"深度订阅失败",
                                                     exchange="gate", symbol=symbol,
                                                     market_type=self.market_type, channel="order_book")
                
                # 🔥 性能优化：减少订阅间隔，提升启动速度
                await asyncio.sleep(0.02)  # 从100ms减少到20ms，大幅加速订阅
                
            self._log_info(f"🎯 Gate {self.market_type} 深度订阅完成: {depth_success_count}/{len(self.symbols)} 成功")
            
            # 只要Ticker订阅成功就算成功（深度数据不是必须的）
            return True
            
        except Exception as e:
            self._log_error(f"订阅频道时发生异常: {e}", exc_info=True)
            return False
    
    async def handle_message(self, message: Dict[str, Any]):
        """处理消息 - 增强版本，支持所有可能的消息格式"""
        try:
            channel = message.get("channel", "")
            event = message.get("event", "")

            # 🔥 详细记录所有消息用于调试
            self._log_debug(f"🔍 [GATE-{self.market_type.upper()}] 收到消息: channel={channel}, event={event}")

            # 防刷屏：限制订单簿消息处理频率（但不要太严格）
            if "order_book" in channel:
                current_time = time.time()
                if hasattr(self, '_last_orderbook_time'):
                    if current_time - self._last_orderbook_time < 0.1:  # 降低到100ms，避免丢失数据
                        return
                self._last_orderbook_time = current_time

            # 处理订阅响应
            if event == "subscribe":
                result = message.get("result", {})
                status = result.get("status", "")
                if status == "success":
                    self._log_info(f"✅ [GATE-{self.market_type.upper()}] 订阅成功: {channel}")
                else:
                    self._log_error(f"❌ [GATE-{self.market_type.upper()}] 订阅失败: {message}")

                    # 🔥 新增：记录订阅失败日志
                    from .websocket_logger import log_websocket_subscription_failure
                    log_websocket_subscription_failure("error", f"订阅响应失败",
                                                     exchange="gate", channel=channel,
                                                     market_type=self.market_type,
                                                     error_message=str(message))
                return

            # 处理错误消息
            if event == "error":
                self._log_error(f"❌ [GATE-{self.market_type.upper()}] 收到错误消息: {message}")
                return

            # 处理服务器ping请求
            if event == "ping":
                # 回复pong，避免服务器断开连接
                pong_msg = {"event": "pong", "time": int(time.time())}
                await self.send(pong_msg)
                self._log_debug("💓 [GATE] 收到ping，已回复pong")
                return

            # 🔥 增强的数据处理逻辑 - 支持多种消息格式

            # 格式1：标准update事件
            if event == "update":
                result = message.get("result", {})

                if "tickers" in channel:
                    # 🔥 ticker数据已完全移除，跳过处理
                    self._log_debug(f"跳过ticker数据: {channel}")
                elif "order_book" in channel:  # 🔥 修复：处理全量订单簿快照
                    self.orderbook_count += 1
                    await self._handle_orderbook(result)
                    self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理订单簿全量数据")
                elif "trades" in channel:
                    self.trade_count += 1
                    await self._handle_trades(result)
                    self._log_debug(f"💱 [GATE-{self.market_type.upper()}] 处理交易数据")

            # 🔥 ticker数据处理已完全移除
            elif "tickers" in channel and not event:
                self._log_debug(f"跳过ticker数据: {channel}")

            # 格式3：直接的订单簿数据（无event字段）
            elif "order_book" in channel and not event:  # 🔥 修复：处理全量订单簿
                self.orderbook_count += 1
                result = message.get("result", None)
                if result:
                    await self._handle_orderbook(result)
                    self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理直接订单簿数据")
                else:
                    self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] 订单簿消息无result字段")

            # 🔥 格式4：其他可能的订单簿消息格式
            elif any(keyword in channel.lower() for keyword in ["orderbook", "order_book", "depth", "book"]):
                self.orderbook_count += 1
                # 尝试多种可能的数据字段
                result = message.get("result", message.get("data", message))
                await self._handle_orderbook(result)
                self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理其他格式订单簿数据: {channel}")

            # 🔥 格式5：如果消息包含订单簿相关字段，直接处理
            elif any(field in message for field in ["a", "b", "asks", "bids"]):
                self.orderbook_count += 1
                await self._handle_orderbook(message)
                self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理包含订单簿字段的消息")

            # 🔥 未识别的消息格式 - 记录用于调试
            else:
                self._log_debug(f"🤔 [GATE-{self.market_type.upper()}] 未识别消息格式: channel={channel}, event={event}")
                self._log_debug(f"   消息keys: {list(message.keys())}")
                self._log_debug(f"   消息内容: {str(message)[:200]}...")

        except Exception as e:
            self._log_error(f"❌ [GATE-{self.market_type.upper()}] 处理消息错误: {e}", exc_info=True)
            self._log_error(f"   消息内容: {str(message)[:300]}")  # 记录更多消息内容用于调试
    
    # 🔥 TICKER处理方法已完全删除 - 系统只使用OrderBook数据
    
    async def _handle_orderbook(self, data: Dict[str, Any]):
        """处理订单簿数据 - 🔥 增强容错机制"""
        try:
            # 🔥 新增：使用统一订单簿验证器和性能监控
            from websocket.orderbook_validator import get_orderbook_validator
            from websocket.performance_monitor import record_message_latency
            import time

            start_time = time.time()

            # 🔥 详细记录原始数据用于调试
            self._log_info(f"🔍 [GATE-{self.market_type.upper()}] 收到订单簿数据: {str(data)[:200]}...")

            # 🔥 修复：统一处理所有可能的数据格式，不区分现货和期货
            # Gate.io WebSocket订单簿数据可能有多种格式

            # 提取交易对信息
            symbol = ""
            asks = []
            bids = []
            # 🔥 使用统一时间戳处理器
            from websocket.unified_timestamp_processor import get_synced_timestamp
            timestamp = get_synced_timestamp("gate", data)

            # 🔥 格式1：标准格式 {"s": "BTC_USDT", "asks": [...], "bids": [...]}
            if "s" in data:
                symbol = data["s"]
                asks = data.get("asks", [])
                bids = data.get("bids", [])
                self._log_info(f"✅ [GATE-{self.market_type.upper()}] 使用标准格式: {symbol}")

            # 🔥 格式2：合约格式 {"contract": "BTC_USDT", "asks": [...], "bids": [...]}
            elif "contract" in data:
                symbol = data["contract"]
                asks = data.get("asks", data.get("a", []))
                bids = data.get("bids", data.get("b", []))
                self._log_info(f"✅ [GATE-{self.market_type.upper()}] 使用合约格式: {symbol}")

            # 🔥 格式3：直接包含asks/bids的格式
            elif "asks" in data or "bids" in data:
                asks = data.get("asks", [])
                bids = data.get("bids", [])
                # 尝试从其他字段获取交易对
                symbol = data.get("symbol", data.get("currency_pair", ""))
                if not symbol and self.symbols:
                    symbol = self.symbols[0]  # 使用第一个订阅的交易对
                self._log_info(f"✅ [GATE-{self.market_type.upper()}] 使用asks/bids格式: {symbol}")

            # 🔥 格式4：如果都没有，尝试其他可能的字段组合
            else:
                # 尝试所有可能的字段组合
                symbol = data.get("symbol", data.get("currency_pair", data.get("cp", "")))
                asks = data.get("a", data.get("asks", []))
                bids = data.get("b", data.get("bids", []))

                if not symbol and self.symbols:
                    symbol = self.symbols[0]  # 使用第一个订阅的交易对

                self._log_info(f"⚠️ [GATE-{self.market_type.upper()}] 使用兜底格式: {symbol}")

            # 🔥 关键修复：如果仍然没有交易对信息，记录详细错误但不直接返回
            if not symbol:
                self._log_error(f"❌ [GATE-{self.market_type.upper()}] 无法提取交易对信息")
                self._log_error(f"   原始数据keys: {list(data.keys())}")
                self._log_error(f"   原始数据: {str(data)[:300]}")
                # 如果只有一个订阅的交易对，使用它
                if len(self.symbols) == 1:
                    symbol = self.symbols[0]
                    self._log_warning(f"🔧 [GATE-{self.market_type.upper()}] 使用唯一订阅交易对: {symbol}")
                else:
                    return  # 无法确定交易对，跳过

            # 🔥 修复：记录数据接收情况，不因为数据为空就跳过
            self._log_info(f"📊 [GATE-{self.market_type.upper()}] {symbol} 原始数据: asks={len(asks)}, bids={len(bids)}")

            # 🔥 关键修复：即使asks或bids为空也要处理，这可能是增量更新
            # Gate.io的增量更新可能只包含一边的数据

            # 🔥 转换为统一格式 - 支持多种数据格式
            formatted_asks = []
            formatted_bids = []

            # 🔥 关键修复：添加空数据检查
            if not asks or len(asks) == 0:
                self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] {symbol} asks数据为空")

            # 🔥 处理asks数据 - 支持多种格式，升级为30档深度
            for ask in asks[:30]:  # 🔥 修复：升级为30档深度
                try:
                    if isinstance(ask, list) and len(ask) >= 2:
                        # 格式1：数组格式 [price, size] - 🔥 使用高精度Decimal处理
                        from decimal import Decimal
                        price, size = Decimal(str(ask[0])), Decimal(str(ask[1]))
                        # 🔥 关键修复：添加数量验证，确保价格和数量都大于0
                        if price > 0 and size > 0:  # 价格和数量都必须大于0
                            formatted_asks.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                        else:
                            self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效ask数据: price={price}, size={size}")
                    elif isinstance(ask, dict):
                        # 格式2：对象格式 {'p': price, 's': size} 或 {'price': price, 'size': size} - 🔥 使用高精度Decimal处理
                        from decimal import Decimal
                        price = Decimal(str(ask.get("p", ask.get("price", 0))))
                        size = Decimal(str(ask.get("s", ask.get("size", ask.get("amount", 0)))))
                        # 🔥 关键修复：添加数量验证
                        if price > 0 and size > 0:  # 价格和数量都必须大于0
                            formatted_asks.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                        else:
                            self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效ask数据: price={price}, size={size}")
                    elif isinstance(ask, str):
                        # 格式3：字符串格式 "price:size" - 🔥 使用高精度Decimal处理
                        parts = ask.split(":")
                        if len(parts) >= 2:
                            from decimal import Decimal
                            price, size = Decimal(str(parts[0])), Decimal(str(parts[1]))
                            # 🔥 关键修复：添加数量验证
                            if price > 0 and size > 0:  # 价格和数量都必须大于0
                                formatted_asks.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                            else:
                                self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效ask数据: price={price}, size={size}")
                except (ValueError, TypeError, IndexError) as e:
                    self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] 解析ask数据失败: {ask} - {e}")
                    continue

            # 🔥 关键修复：添加空数据检查
            if not bids or len(bids) == 0:
                self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] {symbol} bids数据为空")

            # 🔥 处理bids数据 - 支持多种格式，升级为30档深度
            for bid in bids[:30]:  # 🔥 修复：升级为30档深度
                try:
                    if isinstance(bid, list) and len(bid) >= 2:
                        # 格式1：数组格式 [price, size] - 🔥 使用高精度Decimal处理
                        from decimal import Decimal
                        price, size = Decimal(str(bid[0])), Decimal(str(bid[1]))
                        # 🔥 关键修复：添加数量验证，确保价格和数量都大于0
                        if price > 0 and size > 0:  # 价格和数量都必须大于0
                            formatted_bids.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                        else:
                            self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效bid数据: price={price}, size={size}")
                    elif isinstance(bid, dict):
                        # 格式2：对象格式 {'p': price, 's': size} 或 {'price': price, 'size': size} - 🔥 使用高精度Decimal处理
                        from decimal import Decimal
                        price = Decimal(str(bid.get("p", bid.get("price", 0))))
                        size = Decimal(str(bid.get("s", bid.get("size", bid.get("amount", 0)))))
                        # 🔥 关键修复：添加数量验证
                        if price > 0 and size > 0:  # 价格和数量都必须大于0
                            formatted_bids.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                        else:
                            self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效bid数据: price={price}, size={size}")
                    elif isinstance(bid, str):
                        # 格式3：字符串格式 "price:size" - 🔥 使用高精度Decimal处理
                        parts = bid.split(":")
                        if len(parts) >= 2:
                            from decimal import Decimal
                            price, size = Decimal(str(parts[0])), Decimal(str(parts[1]))
                            # 🔥 关键修复：添加数量验证
                            if price > 0 and size > 0:  # 价格和数量都必须大于0
                                formatted_bids.append([float(price), float(size)])  # 🔥 转换为float保持兼容性
                            else:
                                self._log_debug(f"⚠️ [GATE-{self.market_type.upper()}] 无效bid数据: price={price}, size={size}")
                except (ValueError, TypeError, IndexError) as e:
                    self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] 解析bid数据失败: {bid} - {e}")
                    continue

            # 🔥 标准合规：添加异步锁防止并发冲突
            import asyncio
            if not hasattr(self, 'orderbook_locks'):
                self.orderbook_locks = {}
            if symbol not in self.orderbook_locks:
                self.orderbook_locks[symbol] = asyncio.Lock()

            async with self.orderbook_locks[symbol]:
                # 🔥 关键修复：添加正确的排序逻辑，确保最优价格提取正确
                # asks必须按价格升序排列（最低卖价在前）
                if formatted_asks:
                    formatted_asks = sorted(formatted_asks, key=lambda x: x[0])[:30]  # 升序，取前30档

                # bids必须按价格降序排列（最高买价在前）
                if formatted_bids:
                    formatted_bids = sorted(formatted_bids, key=lambda x: x[0], reverse=True)[:30]  # 降序，取前30档

                # 🔥 记录格式化结果
                self._log_info(f"📊 [GATE-{self.market_type.upper()}] {symbol} 格式化结果: asks={len(formatted_asks)}, bids={len(formatted_bids)}")

                # 🔥 新增：使用统一订单簿验证器
                validator = get_orderbook_validator()
                orderbook_data = {
                    'asks': formatted_asks,
                    'bids': formatted_bids
                }
                validation_result = validator.validate_orderbook_data(
                    orderbook_data,
                    exchange="gate",
                    symbol=symbol,
                    market_type=self.market_type
                )

                if not validation_result.is_valid:
                    self._log_warning(f"⚠️ Gate订单簿验证失败: {validation_result.error_message}")
                    # 🔥 容错策略：异步重新订阅该交易对（不阻塞当前处理）
                    asyncio.create_task(self._resubscribe_symbol(symbol))
                    return

                # 🔥 统一Symbol格式 - 使用currency_adapter标准化
                from exchanges.currency_adapter import normalize_symbol
                standard_symbol = normalize_symbol(symbol)  # BTC_USDT → BTC-USDT

                # 🔥 使用统一格式化器创建订单簿数据
                from websocket.unified_data_formatter import get_orderbook_formatter

                formatter = get_orderbook_formatter()
                orderbook_data = formatter.format_orderbook_data(
                    asks=formatted_asks,
                    bids=formatted_bids,
                    symbol=standard_symbol,
                    exchange="gate",
                    market_type=self.market_type,
                    timestamp=timestamp
                )

                # 🔥 记录性能指标
                record_message_latency(start_time)

                # 🔥 详细日志记录
                if formatted_asks and formatted_bids:
                    self._log_info(f"✅ [GATE-{self.market_type.upper()}] {symbol} 完整订单簿: 卖一={formatted_asks[0][0]:.4f}, 买一={formatted_bids[0][0]:.4f}, 价差={(formatted_asks[0][0]-formatted_bids[0][0]):.4f}")
                else:
                    self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] {symbol} 不完整订单簿: asks={len(formatted_asks)}, bids={len(formatted_bids)}")

                # 🔥 统一数据流：发送market_data事件
                self.emit("market_data", orderbook_data)

        except Exception as e:
            self._log_error(f"❌ [GATE-{self.market_type.upper()}] 处理订单簿错误: {e}", exc_info=True)
            self._log_error(f"   原始数据: {str(data)[:500]}")  # 记录更多原始数据用于调试

    async def _resubscribe_symbol(self, symbol: str):
        """
        🔥 重新订阅单个交易对的订单簿数据 - 容错机制
        当检测到订单簿数据为空时，尝试重新订阅
        """
        try:
            self._log_info(f"🔄 [GATE-{self.market_type.upper()}] 重新订阅 {symbol} 订单簿数据")

            # 等待一小段时间避免频繁重订阅
            await asyncio.sleep(1.0)

            # 重新订阅该交易对的订单簿（前10档）
            if self.market_type == "spot":
                depth_msg = {
                    "time": int(time.time()),
                    "channel": "spot.order_book",
                    "event": "subscribe",
                    "payload": [symbol, "10", "100ms"]  # 🔥 修复：现货支持频率参数
                }
            else:
                # 🔥 关键修复：期货订单簿只需要交易对名称，根据官方文档示例
                depth_msg = {
                    "time": int(time.time()),
                    "channel": "futures.order_book",
                    "event": "subscribe",
                    "payload": [symbol]  # 🔥 修复：期货只需要[symbol]格式，参考官方文档
                }

            success = await self.send(depth_msg)
            if success:
                self._log_info(f"✅ [GATE-{self.market_type.upper()}] {symbol} 重新订阅成功")
            else:
                self._log_error(f"❌ [GATE-{self.market_type.upper()}] {symbol} 重新订阅失败")

        except Exception as e:
            self._log_error(f"❌ [GATE-{self.market_type.upper()}] 重新订阅 {symbol} 时发生错误: {e}")
    
    async def _handle_trades(self, data: List[Dict[str, Any]]):
        """处理成交数据"""
        try:
            for trade in data:
                # 🔥 使用高精度Decimal处理交易数据
                from decimal import Decimal
                trade_data = {
                    "symbol": trade.get("currency_pair", trade.get("contract", "")),
                    "price": float(Decimal(str(trade.get("price", 0)))),  # 🔥 转换为float保持兼容性
                    "amount": float(Decimal(str(trade.get("amount", trade.get("size", 0))))),  # 🔥 转换为float保持兼容性
                    "side": trade.get("side", ""),
                    # 🔥 时间戳修复：优先使用服务器时间戳
                    "timestamp": trade.get("create_time_ms", trade.get("timestamp", trade.get("t", int(time.time() * 1000)))),
                    "exchange": "GATE",  # 使用统一的交易所名称
                    "market_type": self.market_type
                }
                
                self._log_debug(f"处理成交: {trade_data['symbol']} {trade_data['side']} {trade_data['amount']} @ {trade_data['price']}")
                self.emit("trade", trade_data)
                
        except Exception as e:
            self._log_error(f"处理成交数据错误: {e}", exc_info=True)
    
    async def send_heartbeat(self):
        """发送心跳"""
        ping_msg = {
            "time": int(time.time()),
            "channel": "spot.ping" if self.market_type == "spot" else "futures.ping"
        }
        self._log_debug(f"发送心跳: {json.dumps(ping_msg)}")
        success = await self.send(ping_msg)
        if success:
            self.last_message_time = time.time()  # 更新最后消息时间
        return success
    
    def get_status(self):
        """获取WebSocket状态"""
        connected = self.ws is not None and self.ws.open if hasattr(self, 'ws') else False
        return {
            "connected": connected,
            "reconnect_count": self.reconnect_count,
            "message_count": self.orderbook_count + self.trade_count,
            "last_message_time": self.last_message_time
        }


# 测试代码
if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 配置简单的日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # ticker处理已移除
    
    async def main():
        # 测试现货WebSocket
        spot_client = GateWebSocketClient("spot")
        spot_client.set_symbols(["BTC_USDT", "ETH_USDT"])
        # ticker回调已移除
        # spot_client.register_callback("orderbook", on_orderbook)  # 已移除
        
        # 测试期货WebSocket
        futures_client = GateWebSocketClient("futures")
        futures_client.set_symbols(["BTC_USDT", "ETH_USDT"])
        # ticker回调已移除
        # futures_client.register_callback("orderbook", on_orderbook)  # 已移除
        
        # 运行30秒后退出
        tasks = [
            asyncio.create_task(spot_client.run()),
            asyncio.create_task(futures_client.run())
        ]
        
        await asyncio.sleep(30)
        
        spot_client.running = False
        futures_client.running = False
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    asyncio.run(main())
