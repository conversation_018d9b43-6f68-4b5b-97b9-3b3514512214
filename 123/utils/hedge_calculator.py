#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 对冲比例预计算器
在开仓前计算对冲比例，过滤低质量机会
🔥 优化版：删除重复逻辑，统一使用TradingRulesPreloader
"""

import logging
from typing import Dict, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class HedgeCalculation:
    """对冲计算结果"""
    spot_amount: float
    futures_amount: float
    spot_formatted: str
    futures_formatted: str
    hedge_ratio: float  # 对冲比例 (%)
    amount_diff: float  # 数量差异
    percentage_diff: float  # 百分比差异
    is_acceptable: bool  # 是否可接受
    spot_loss: float  # 现货损失
    futures_loss: float  # 期货损失
    total_loss: float  # 总损失

class HedgeCalculator:
    """🔥 优化版对冲比例计算器 - 删除重复逻辑，统一使用TradingRulesPreloader"""
    
    def __init__(self, min_hedge_ratio: float = 98.0):
        """初始化对冲计算器"""
        self.min_hedge_ratio = min_hedge_ratio

        # 🔥 统一获取TradingRulesPreloader实例，避免重复调用
        from core.trading_rules_preloader import get_trading_rules_preloader
        self.rules_preloader = get_trading_rules_preloader()

        logger.info("✅ 对冲计算器初始化完成 - 🔥 优化版：统一使用TradingRulesPreloader")
        logger.info(f"   对冲比例阈值: {min_hedge_ratio}%")

    async def calculate_hedge_amounts(self,
                                    base_amount: float,
                                    spot_exchange: str,
                                    futures_exchange: str,
                                    exchanges_dict: Dict = None,
                                    symbol: str = "BTC-USDT") -> HedgeCalculation:
        """计算对冲数量 - 兼容测试脚本接口"""
        return await self.calculate_hedge_ratio(
            base_amount=base_amount,
            spot_exchange=spot_exchange,
            futures_exchange=futures_exchange,
            spot_exchange_instance=exchanges_dict.get(spot_exchange) if exchanges_dict else None,
            futures_exchange_instance=exchanges_dict.get(futures_exchange) if exchanges_dict else None,
            symbol=symbol
        )

    async def calculate_hedge_ratio(self,
                                  base_amount: float,
                                  spot_exchange: str,
                                  futures_exchange: str,
                                  spot_exchange_instance=None,
                                  futures_exchange_instance=None,
                                  symbol: str = "BTC-USDT") -> HedgeCalculation:
        """
        🔥 优化版计算对冲比例 - 完全使用TradingRulesPreloader统一系统
        删除所有重复逻辑，避免重复计算
        """
        try:
            # 🔥 使用实例变量，避免重复调用
            # 🔥 统一使用预加载器的精度处理
            spot_amount = self.rules_preloader.truncate_to_step_size(base_amount, spot_exchange, symbol, "spot")
            futures_amount = self.rules_preloader.truncate_to_step_size(base_amount, futures_exchange, symbol, "futures")
            
            spot_formatted = self.rules_preloader.format_amount_unified(spot_amount, spot_exchange, symbol, "spot")
            futures_formatted = self.rules_preloader.format_amount_unified(futures_amount, futures_exchange, symbol, "futures")

            logger.info(f"🎯 统一精度处理: 基础={base_amount:.6f} → 现货={spot_amount:.6f}('{spot_formatted}'), 期货={futures_amount:.6f}('{futures_formatted}')")

            # 🔥 核心优化：使用虚拟价格进行对冲质量检查
            # 由于测试场景不提供真实价格，使用1.0作为标准化价格
            virtual_price = 1.0
            
            # 🔥 完全使用TradingRulesPreloader的对冲质量缓存系统（包含完美对冲逻辑）
            hedge_quality_data = self.rules_preloader.get_hedge_quality_cached(
                spot_exchange, futures_exchange, symbol,
                spot_amount, futures_amount, virtual_price, virtual_price
            )

            if hedge_quality_data:
                hedge_ratio = hedge_quality_data.get("hedge_ratio", 0.0) * 100  # 转换为百分比
                is_good_hedge = hedge_quality_data.get("is_good_hedge", False)

                # 🔥 检查是否使用了完美对冲逻辑
                perfect_hedge_applied = hedge_quality_data.get("perfect_hedge_applied", False)
                if perfect_hedge_applied:
                    logger.info(f"🎯 完美对冲逻辑已应用: 比例={hedge_ratio:.2f}%, 质量={'通过' if is_good_hedge else '不通过'}")
                else:
                    logger.info(f"✅ 使用TradingRulesPreloader对冲质量缓存: 比例={hedge_ratio:.2f}%, 质量={'通过' if is_good_hedge else '不通过'}")
            else:
                # 降级计算（不应该发生）
                logger.warning("⚠️ 对冲质量缓存获取失败，使用降级计算")
                if futures_amount > 0 and spot_amount > 0:
                    hedge_ratio = min(spot_amount, futures_amount) / max(spot_amount, futures_amount) * 100
                    is_good_hedge = hedge_ratio >= self.min_hedge_ratio
                else:
                    hedge_ratio = 0
                    is_good_hedge = False

            # 计算差异和损失
            amount_diff = abs(spot_amount - futures_amount)
            reference_amount = max(spot_amount, futures_amount)
            percentage_diff = (amount_diff / reference_amount) * 100 if reference_amount > 0 else 100
            
            spot_loss = max(0, base_amount - spot_amount)
            futures_loss = max(0, base_amount - futures_amount)
            total_loss = spot_loss + futures_loss

            # 创建结果
            result = HedgeCalculation(
                spot_amount=spot_amount,
                futures_amount=futures_amount,
                spot_formatted=spot_formatted,
                futures_formatted=futures_formatted,
                hedge_ratio=hedge_ratio,
                amount_diff=amount_diff,
                percentage_diff=percentage_diff,
                is_acceptable=is_good_hedge,
                spot_loss=spot_loss,
                futures_loss=futures_loss,
                total_loss=total_loss
            )

            logger.info(f"🎯 对冲计算完成: 比例={hedge_ratio:.2f}%, 差异={amount_diff:.6f}, 可接受={is_good_hedge}")
            return result

        except Exception as e:
            logger.error(f"❌ 对冲比例计算异常: {e}")
            # 返回失败结果
            return HedgeCalculation(
                spot_amount=0, futures_amount=0,
                spot_formatted="0", futures_formatted="0",
                hedge_ratio=0, amount_diff=0, percentage_diff=100,
                is_acceptable=False, spot_loss=base_amount, 
                futures_loss=base_amount, total_loss=base_amount * 2
            )

    # 🔥 删除重复方法：format_amount_by_exchange
    # 统一使用TradingRulesPreloader.format_amount_unified()

    # 🔥 删除所有重复方法！统一使用TradingRulesPreloader.get_hedge_quality_cached()
    # 为兼容性保留极简接口，但内部不再重复计算

    # 🔥 删除重复方法：find_best_hedge_combination, should_execute_opportunity
    # 这些功能已由ExecutionEngine和TradingRulesPreloader统一处理

    def calculate_optimal_amounts(self, available_balance: float, spot_price: float,
                                futures_price: float, target_ratio: float = 0.98) -> Tuple[float, float]:
        """计算最优对冲数量 - 保留的工具方法"""
        try:
            # 基于价格比例计算
            price_ratio = futures_price / spot_price if spot_price > 0 else 1.0
            
            # 简化计算
            if price_ratio >= 1:
                spot_amount = available_balance * 0.5 / spot_price
                futures_amount = spot_amount * target_ratio
            else:
                futures_amount = available_balance * 0.5 / futures_price
                spot_amount = futures_amount * target_ratio
            
            return spot_amount, futures_amount
            
        except Exception as e:
            logger.error(f"最优数量计算失败: {e}")
            return 0.0, 0.0

    def calculate_arbitrage_profit(self, spot_amount: float, futures_amount: float,
                                 entry_spot_price: float, entry_futures_price: float,
                                 exit_spot_price: float, exit_futures_price: float) -> float:
        """计算套利利润 - 保留的工具方法"""
        try:
            # 现货利润：卖出价格 - 买入价格
            spot_profit = spot_amount * (exit_spot_price - entry_spot_price)
            
            # 期货利润：开仓价格 - 平仓价格（做空）
            futures_profit = futures_amount * (entry_futures_price - exit_futures_price)
            
            total_profit = spot_profit + futures_profit
            return total_profit
            
        except Exception as e:
            logger.error(f"套利利润计算失败: {e}")
            return 0.0

# 🔥 保留的全局接口函数
def get_hedge_calculator(min_hedge_ratio: float = 98.0) -> HedgeCalculator:
    """获取对冲计算器实例"""
    return HedgeCalculator(min_hedge_ratio)

async def check_hedge_quality(base_amount: float,
                            spot_exchange: str,
                            futures_exchange: str,
                            min_hedge_ratio: float = 98.0,
                            exchanges_dict: Dict = None,
                            symbol: str = "BTC-USDT") -> Tuple[bool, Dict]:
    """🔥 优化版对冲质量检查 - 使用统一系统"""
    try:
        calculator = HedgeCalculator(min_hedge_ratio)
        result = await calculator.calculate_hedge_ratio(
            base_amount, spot_exchange, futures_exchange,
            exchanges_dict.get(spot_exchange) if exchanges_dict else None,
            exchanges_dict.get(futures_exchange) if exchanges_dict else None,
            symbol
        )
        
        return result.is_acceptable, {
            "hedge_ratio": result.hedge_ratio,
            "spot_amount": result.spot_amount,
            "futures_amount": result.futures_amount,
            "spot_formatted": result.spot_formatted,
            "futures_formatted": result.futures_formatted,
            "amount_diff": result.amount_diff,
            "percentage_diff": result.percentage_diff
        }
        
    except Exception as e:
        logger.error(f"对冲质量检查失败: {e}")
        return False, {}
