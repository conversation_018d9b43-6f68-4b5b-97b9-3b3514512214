#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用保证金计算器 - API驱动，支持所有交易所和交易对
"""

import os
import time
import asyncio
from typing import Dict, Optional, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class MarginCalculator:
    """
    🔥 通用保证金计算器 - 按照全流程工作流.md设计

    设计原则：
    1. API驱动：所有保证金计算都从API获取
    2. 缓存机制：合约信息缓存，5分钟TTL，避免重复API调用
    3. 动态检查：实时检查保证金，不依赖预估
    4. 统一接口：所有交易所使用相同的接口规范
    5. 零延迟：运行时使用缓存，零API调用
    """

    def __init__(self):
        self.contract_cache = {}  # 合约信息缓存 - 按照全流程工作流.md
        self.account_config_cache = {}  # 🔥 新增：账户配置缓存 - 使用现有缓存系统架构
        self.cache_ttl = int(os.getenv("MARGIN_CACHE_TTL", "300"))  # 缓存5分钟TTL，支持.env配置
        self.logger = logging.getLogger(__name__)
        
        # 🔥 使用现有的统一缓存监控系统 - 不造轮子
        from utils.cache_monitor import get_cache_monitor
        self.cache_monitor = get_cache_monitor()
        
    async def calculate_required_margin(self, 
                                      exchange_name: str,
                                      symbol: str,
                                      amount: float,
                                      price: float,
                                      exchange_client=None) -> Tuple[float, Dict]:
        """
        计算所需保证金
        
        Args:
            exchange_name: 交易所名称
            symbol: 交易对
            amount: 交易数量（币）
            price: 价格
            exchange_client: 交易所客户端
            
        Returns:
            (所需保证金, 详细信息)
        """
        try:
            # 1. 获取合约信息（带缓存）
            contract_info = await self._get_contract_info(exchange_name, symbol, exchange_client)
            
            if not contract_info:
                # 🔥 关键修复：合约信息获取失败是保证金问题的根源！
                logger.error(f"🚨 {exchange_name} {symbol} 合约信息获取失败 - 这是保证金计算错误的根本原因！")
                logger.error(f"💡 解决方案：")
                logger.error(f"   1. 检查交易所API连接")
                logger.error(f"   2. 验证交易对符号格式")
                logger.error(f"   3. 确认交易所支持该合约")

                # 🔥 修复：使用极保守的保证金率，确保不会因为保证金不足而失败
                ultra_conservative_rate = 0.50  # 50%极保守保证金率
                estimated_value = amount * price
                ultra_conservative_margin = estimated_value * ultra_conservative_rate

                details = {
                    "method": "ultra_conservative_no_contract",
                    "estimated_value": estimated_value,
                    "margin_rate": ultra_conservative_rate,
                    "required_margin": ultra_conservative_margin,
                    "error": "合约信息获取失败",
                    "warning": f"使用50%极保守保证金率，避免交易失败",
                    "symbol": symbol,
                    "exchange": exchange_name
                }

                logger.warning(f"⚠️ {exchange_name} {symbol} 合约信息缺失，使用50%极保守保证金: ${ultra_conservative_margin:.2f}")
                return ultra_conservative_margin, details
            
            # 2. 根据交易所类型计算保证金
            if exchange_name.lower() == "gate":
                return await self._calculate_gate_margin(contract_info, amount, price)
            elif exchange_name.lower() == "bybit":
                return await self._calculate_bybit_margin(contract_info, amount, price)
            elif exchange_name.lower() == "okx":
                return await self._calculate_okx_margin(contract_info, amount, price, exchange_client)
            else:
                # 通用计算
                return await self._calculate_generic_margin(contract_info, amount, price)
                
        except Exception as e:
            logger.error(f"保证金计算失败 {exchange_name} {symbol}: {e}")
            # 🔥 修复：返回保守估算，降低保证金率到10%
            estimated_value = amount * price
            conservative_margin = estimated_value * 0.1  # 10%保证金率
            details = {
                "method": "error_fallback",
                "error": str(e),
                "margin_rate": 0.1,
                "required_margin": conservative_margin
            }
            return conservative_margin, details
    
    async def _get_contract_info(self, exchange_name: str, symbol: str, exchange_client) -> Optional[Dict]:
        """获取合约信息（带缓存）"""
        try:
            cache_key = f"{exchange_name}_{symbol}"
            current_time = time.time()
            
            # 检查缓存
            if cache_key in self.contract_cache:
                cached_data, cache_time = self.contract_cache[cache_key]
                if current_time - cache_time < self.cache_ttl:
                    logger.info(f"✅ [缓存命中] 保证金缓存: {exchange_name}_{symbol} | 合约信息从缓存获取，零延迟")
                    return cached_data
            
            # 从API获取
            if not exchange_client:
                logger.warning(f"交易所客户端未提供: {exchange_name}")
                return None
                
            if not hasattr(exchange_client, 'get_contract_info'):
                logger.warning(f"交易所不支持get_contract_info: {exchange_name}")
                return None
            
            # 🔥 关键修复：增强重试机制和错误处理，确保合约信息获取成功
            max_retries = 3
            last_error = None

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔍 获取合约信息: {exchange_name} {symbol} (尝试 {attempt + 1}/{max_retries})")
                    contract_info = await exchange_client.get_contract_info(symbol)

                    if contract_info and self._validate_contract_info(contract_info, exchange_name, symbol):
                        # 更新缓存
                        self.contract_cache[cache_key] = (contract_info, current_time)
                        logger.info(f"✅ [API调用] 保证金接口: {exchange_name}_{symbol} | 合约信息获取成功并缓存")
                        return contract_info
                    else:
                        logger.warning(f"⚠️ 合约信息无效: {exchange_name} {symbol} (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # 短暂等待后重试

                except Exception as api_error:
                    last_error = api_error
                    error_str = str(api_error).lower()

                    # 🔥 增强错误分类处理
                    if any(keyword in error_str for keyword in ["not found", "invalid", "不存在", "does not exist"]):
                        logger.warning(f"🚫 {exchange_name} {symbol} 合约不存在，停止重试")
                        break  # 合约不存在，无需重试
                    elif any(keyword in error_str for keyword in ["rate limit", "too many", "频率"]):
                        logger.warning(f"⚠️ {exchange_name} API频率限制，等待后重试 (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(2.0)  # 频率限制等待更长时间
                    elif any(keyword in error_str for keyword in ["timeout", "connection", "network"]):
                        logger.warning(f"⚠️ {exchange_name} 网络问题，重试 (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(1.0)  # 网络问题等待1秒
                    else:
                        logger.error(f"❌ {exchange_name} API调用失败: {api_error} (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # 其他错误短暂等待

            # 🔥 增强回退机制：所有重试都失败时的智能回退
            if last_error:
                logger.error(f"❌ [API调用] 保证金接口: {exchange_name}_{symbol} | 获取合约信息失败，最后错误: {last_error}")
            else:
                logger.error(f"❌ [API调用] 保证金接口: {exchange_name}_{symbol} | 获取合约信息失败，所有重试都失败")

            # 🔥 智能回退：尝试使用默认合约信息
            default_contract_info = self._get_default_contract_info(exchange_name, symbol)
            if default_contract_info:
                logger.warning(f"🔧 使用默认合约信息作为回退: {exchange_name} {symbol}")
                # 缓存默认信息，但设置较短TTL
                self.contract_cache[cache_key] = (default_contract_info, current_time - self.cache_ttl + 60)  # 1分钟TTL
                return default_contract_info

            return None
                
        except Exception as e:
            logger.error(f"获取合约信息失败: {exchange_name} {symbol} - {e}")
            return None

    def _get_default_contract_info(self, exchange_name: str, symbol: str) -> Optional[Dict]:
        """
        🔥 新增：获取默认合约信息作为API失败时的回退
        """
        try:
            exchange_lower = exchange_name.lower()

            # 基础默认值
            default_info = {
                "symbol": symbol,
                "leverage_max": "100",
                "maintenance_rate": "0.005",
                "order_size_min": "0.001",
                "order_size_max": "1000000",
                "price_precision": "4",
                "quantity_precision": "6"
            }

            # 交易所特定默认值
            if exchange_lower == "gate":
                default_info.update({
                    "quanto_multiplier": "0.0001",  # Gate.io常见值
                    "type": "direct",
                    "maintenance_rate": "0.005"
                })
            elif exchange_lower == "bybit":
                default_info.update({
                    "maintenance_rate": "0.005",
                    "leverage_max": "100"
                })
            elif exchange_lower == "okx":
                default_info.update({
                    "account_level": "1",  # 统一账户模式
                    "maintenance_rate": "0.004",
                    "leverage_max": "125"
                })

            logger.info(f"✅ 生成默认合约信息: {exchange_name} {symbol}")
            return default_info

        except Exception as e:
            logger.error(f"生成默认合约信息失败: {e}")
            return None

    def _validate_contract_info(self, contract_info: Dict, exchange_name: str, symbol: str) -> bool:
        """
        验证合约信息完整性 - 🔥 修复：根据交易所特性进行差异化验证

        关键修复：不强制要求所有交易所都有quanto_multiplier字段
        - Gate.io: 需要quanto_multiplier进行合约转换
        - Bybit: 线性合约，不需要quanto_multiplier
        - OKX: 使用ctVal和ctMult，不是quanto_multiplier
        """
        if not contract_info:
            logger.warning(f"⚠️ {exchange_name} {symbol} 合约信息为空")
            return False

        # 🔥 关键修复：根据交易所特性定义必需字段
        exchange_lower = exchange_name.lower()

        if exchange_lower == 'gate':
            # Gate.io需要quanto_multiplier进行合约转换
            required_fields = ['maintenance_rate', 'leverage_max', 'quanto_multiplier']
        elif exchange_lower == 'bybit':
            # Bybit线性合约，不需要quanto_multiplier
            required_fields = ['maintenance_rate', 'leverage_max']
        elif exchange_lower == 'okx':
            # OKX使用ctVal和ctMult，不是quanto_multiplier
            required_fields = ['maintenance_rate', 'leverage_max']
        else:
            # 其他交易所的通用要求
            required_fields = ['maintenance_rate', 'leverage_max']

        missing_fields = []
        for field in required_fields:
            if field not in contract_info or contract_info[field] is None:
                missing_fields.append(field)

        if missing_fields:
            logger.warning(f"⚠️ {exchange_name} {symbol} 合约信息缺失关键字段: {missing_fields}")

            # 🔥 关键修复：只为真正需要的字段提供默认值
            if 'quanto_multiplier' in missing_fields and exchange_lower == 'gate':
                # 只有Gate.io才需要quanto_multiplier
                contract_info['quanto_multiplier'] = '0.0001'  # Gate.io常见值
                logger.warning(f"🔧 {exchange_name} {symbol} 使用默认quanto_multiplier: {contract_info['quanto_multiplier']}")

            if 'maintenance_rate' in missing_fields:
                # 所有交易所都需要维持保证金率
                default_rates = {'gate': '0.005', 'bybit': '0.005', 'okx': '0.004'}
                contract_info['maintenance_rate'] = default_rates.get(exchange_lower, '0.005')
                logger.warning(f"🔧 {exchange_name} {symbol} 使用默认maintenance_rate: {contract_info['maintenance_rate']}")

            if 'leverage_max' in missing_fields:
                # 所有交易所都需要最大杠杆
                contract_info['leverage_max'] = '100'
                logger.warning(f"🔧 {exchange_name} {symbol} 使用默认leverage_max: {contract_info['leverage_max']}")

        # 🔥 修复：只对Gate.io验证quanto_multiplier
        if exchange_lower == 'gate' and 'quanto_multiplier' in contract_info:
            try:
                quanto_multiplier = float(contract_info.get('quanto_multiplier', 0))
                if quanto_multiplier <= 0:
                    logger.error(f"❌ {exchange_name} {symbol} quanto_multiplier无效: {quanto_multiplier}")
                    return False
                elif quanto_multiplier > 1:
                    logger.warning(f"⚠️ {exchange_name} {symbol} quanto_multiplier异常大: {quanto_multiplier}")
                    # 不返回False，因为某些合约可能确实有大的multiplier
            except (ValueError, TypeError):
                logger.error(f"❌ {exchange_name} {symbol} quanto_multiplier格式错误: {contract_info.get('quanto_multiplier')}")
                return False

        logger.info(f"✅ {exchange_name} {symbol} 合约信息验证通过")
        return True
    
    async def _calculate_gate_margin(self, contract_info: Dict, amount: float, price: float) -> Tuple[float, Dict]:
        """
        Gate.io保证金计算 - 🔥 修复合约张数转换问题

        关键修复：
        1. amount参数是币数量，需要转换为合约张数
        2. Gate.io期货以"张"为单位，每张合约价值 = quanto_multiplier × 标记价格
        3. 保证金计算必须基于正确的张数

        Args:
            contract_info: 合约信息，包含quanto_multiplier等字段
            amount: 币数量（需要转换为张数）
            price: 当前价格（USDT）

        Returns:
            (所需保证金, 详细信息)
        """
        try:
            # 🔥 关键修复：获取合约参数并验证（通用于所有代币）
            quanto_multiplier = float(contract_info.get('quanto_multiplier', '0.0001'))
            maintenance_rate = float(contract_info.get('maintenance_rate', '0.005'))
            leverage_max = float(contract_info.get('leverage_max', '100'))
            contract_type = contract_info.get('type', 'direct')

            # 🔥 关键修复：验证quanto_multiplier合理性（防止API返回错误数据）
            if quanto_multiplier <= 0:
                logger.error(f"❌ Gate.io quanto_multiplier无效: {quanto_multiplier}, 使用默认值0.0001")
                quanto_multiplier = 0.0001
            elif quanto_multiplier > 1:
                logger.warning(f"⚠️ Gate.io quanto_multiplier异常大: {quanto_multiplier}, 可能导致计算错误")
                # 不强制修改，但记录警告

            logger.info(f"🔍 Gate.io合约参数: quanto_multiplier={quanto_multiplier}, leverage_max={leverage_max}")

            # 🔥 修复：使用最优杠杆实现15%保证金率
            target_margin_rate = 0.15  # 15%目标保证金率
            optimal_leverage = 1.0 / target_margin_rate  # 6.67x杠杆
            leverage = min(optimal_leverage, leverage_max)

            # 🔥 关键修复：币数量转换为合约张数
            # 币数量 ÷ 每张合约面值 = 张数
            # 例如：23.34 DOT ÷ 0.0001 DOT/张 = 233,400张
            contract_size = amount / quanto_multiplier  # 转换为张数

            logger.info(f"🔧 Gate.io合约转换: {amount:.6f}币 ÷ {quanto_multiplier} = {contract_size:.0f}张")

            if contract_type == 'direct':
                # 🔥 Gate.io官方SDK保证金计算公式（基于张数）
                # margin = order_size(张) × last_price × quanto_multiplier ÷ leverage
                margin_base = contract_size * price * quanto_multiplier / leverage
                # 🔥 修复：直接使用基础保证金，无额外安全边际，确保精确15%
                required_margin = margin_base

                # 持仓价值验证
                position_value = contract_size * price * quanto_multiplier  # 持仓价值(USDT)
                margin_rate = (required_margin / position_value) * 100 if position_value > 0 else 0

                # 🔥 关键修复：保证金率合理性检查
                if margin_rate < 10.0 or margin_rate > 80.0:
                    logger.warning(f"Gate.io保证金率异常: {margin_rate:.2f}%, 使用保守保证金率35%")
                    required_margin = position_value * 0.35  # 使用35%保守保证金率
                    margin_rate = 35.0

            else:
                # Inverse类型：基础货币结算合约
                logger.warning(f"Gate.io inverse类型合约，使用保守估算")
                position_value = amount * price  # 直接使用币价值
                required_margin = position_value * 0.35  # 35%保证金率
                margin_rate = 35.0

            details = {
                "method": "gate_contract_conversion_fixed",
                "contract_type": contract_type,
                "coin_amount": amount,
                "contract_size": contract_size,
                "price": price,
                "quanto_multiplier": quanto_multiplier,
                "leverage": leverage,
                "position_value": position_value,
                "margin_rate": f"{margin_rate:.2f}%",
                "safety_factor": 1.1,
                "required_margin": required_margin,
                "maintenance_rate": maintenance_rate,
                "leverage_max": leverage_max
            }

            logger.info(f"✅ Gate.io保证金计算修复: {amount:.6f}币 → {contract_size:.0f}张 → ${required_margin:.2f}保证金 (保证金率{margin_rate:.2f}%)")

            return required_margin, details

        except Exception as e:
            logger.error(f"Gate.io保证金计算失败: {e}")
            # 🔥 错误回退：使用保守估算（基于币价值）
            estimated_value = amount * price
            conservative_margin = estimated_value * 0.40  # 40%保守保证金率
            details = {
                "method": "gate_error_fallback_fixed",
                "error": str(e),
                "coin_amount": amount,
                "estimated_value": estimated_value,
                "margin_rate": 0.40,
                "required_margin": conservative_margin
            }
            logger.warning(f"⚠️ Gate.io保证金计算回退: {amount:.6f}币 → ${conservative_margin:.2f}保证金 (40%保守率)")
            return conservative_margin, details
    
    async def _calculate_unified_standard_margin(self, contract_info: Dict, amount: float, price: float, exchange_name: str) -> Tuple[float, Dict]:
        """
        🔥 统一的标准保证金计算逻辑 - 修复33%保证金率问题
        适用于Gate和Bybit等使用标准保证金模式的交易所
        """
        try:
            estimated_value = amount * price

            # 🔥 统一参数提取
            maintenance_margin = float(contract_info.get('maintenance_rate', 0.005))  # 维持保证金率
            max_leverage = float(contract_info.get('leverage_max', 100))  # 最大杠杆

            # 🔥 关键修复：使用正确的杠杆设置 - 修复33%保证金率问题
            # 原问题：使用3倍杠杆导致33%保证金率 (1/3 = 0.33)
            # 修复：使用6.67倍杠杆实现15%保证金率 (1/6.67 ≈ 0.15)
            target_margin_rate = 0.15  # 目标15%保证金率
            optimal_leverage = 1.0 / target_margin_rate  # 6.67倍杠杆
            leverage = min(optimal_leverage, max_leverage)  # 使用最优杠杆或最大杠杆中的较小值

            # 🔥 修复：正确的保证金计算公式
            initial_margin_rate = 1.0 / leverage
            # 开仓只需要初始保证金，不需要加上维持保证金
            required_margin = estimated_value * initial_margin_rate

            details = {
                "method": f"{exchange_name}_unified_standard",
                "estimated_value": estimated_value,
                "maintenance_margin": maintenance_margin,
                "leverage": leverage,
                "max_leverage": max_leverage,
                "initial_margin_rate": initial_margin_rate,
                "required_margin": required_margin
            }

            logger.info(f"✅ {exchange_name.title()}保证金计算: 价值${estimated_value:.2f}, 杠杆{leverage}x, "
                       f"维持保证金率={maintenance_margin*100:.3f}%, 初始保证金率={initial_margin_rate*100:.2f}%, "
                       f"需要保证金=${required_margin:.2f}")

            return required_margin, details

        except Exception as e:
            logger.error(f"{exchange_name.title()}保证金计算失败: {e}")
            # 🔥 统一的错误回退
            estimated_value = amount * price
            conservative_margin = estimated_value * 0.15  # 15%保证金率
            details = {
                "method": f"{exchange_name}_error_fallback",
                "error": str(e),
                "margin_rate": 0.15,
                "required_margin": conservative_margin
            }
            return conservative_margin, details

    async def _calculate_bybit_margin(self, contract_info: Dict, amount: float, price: float) -> Tuple[float, Dict]:
        """
        Bybit保证金计算 - 🔥 使用统一的保证金计算逻辑
        """
        return await self._calculate_unified_standard_margin(contract_info, amount, price, "bybit")
    
    async def _calculate_okx_margin(self, contract_info: Dict, amount: float, price: float, exchange_client) -> Tuple[float, Dict]:
        """
        OKX保证金计算 - 🔥 修复：使用缓存的账户配置，避免重复API调用

        根据OKX API文档：
        - leverage_max: 最大杠杆
        - 账户模式影响保证金计算
        """
        try:
            estimated_value = amount * price

            # 🔥 修复：基于官方API文档的正确参数
            leverage_max = float(contract_info.get('leverage_max', 100))  # 最大杠杆

            # 🔥 关键修复：使用现有缓存系统获取OKX账户配置，避免重复API调用
            account_level = "1"  # 默认统一账户
            if exchange_client and hasattr(exchange_client, 'get_account_config'):
                try:
                    # 🔥 使用现有的保证金缓存系统缓存账户配置
                    config = await self._get_cached_account_config(exchange_client, "okx")
                    account_level = config.get("acctLv", "1")
                except Exception:
                    pass

            # 🔥 关键修复：使用正确的杠杆设置 - 修复33%保证金率问题
            target_margin_rate = 0.15  # 目标15%保证金率
            optimal_leverage = 1.0 / target_margin_rate  # 6.67倍杠杆
            leverage = min(optimal_leverage, leverage_max)  # 使用最优杠杆或最大杠杆中的较小值

            # 🔥 修复：基于OKX官方文档的正确保证金计算公式
            if account_level == "2":  # 单币种保证金模式
                # 🔥 修复：使用目标保证金率，无需额外安全边际
                initial_margin_rate = 1.0 / leverage
                safety_factor = 1.0  # 无额外安全边际，直接使用15%保证金率
                total_margin_rate = initial_margin_rate * safety_factor
                method = "okx_single_currency_fixed"
            else:  # 统一账户模式 (account_level == "1" or "3")
                # 🔥 修复：使用目标保证金率，无需额外安全边际
                initial_margin_rate = 1.0 / leverage
                safety_factor = 1.0  # 无额外安全边际，直接使用15%保证金率
                total_margin_rate = initial_margin_rate * safety_factor
                method = "okx_unified_account_fixed"

            # 计算所需保证金
            required_margin = estimated_value * total_margin_rate

            details = {
                "method": method,
                "estimated_value": estimated_value,
                "account_level": account_level,
                "leverage": leverage,
                "leverage_max": leverage_max,
                "initial_margin_rate": initial_margin_rate,
                "safety_factor": safety_factor,
                "total_margin_rate": total_margin_rate,
                "required_margin": required_margin
            }

            logger.info(f"✅ OKX保证金计算: 价值${estimated_value:.2f}, 账户模式={account_level}, "
                       f"杠杆{leverage}x, 总保证金率={total_margin_rate*100:.2f}%, 需要保证金=${required_margin:.2f}")

            return required_margin, details

        except Exception as e:
            logger.error(f"OKX保证金计算失败: {e}")
            # 🔥 修复：降低保证金率
            estimated_value = amount * price
            conservative_margin = estimated_value * 0.15  # 15%保证金率
            details = {
                "method": "okx_error_fallback",
                "error": str(e),
                "margin_rate": 0.15,
                "required_margin": conservative_margin
            }
            return conservative_margin, details
    
    async def _calculate_generic_margin(self, contract_info: Dict, amount: float, price: float) -> Tuple[float, Dict]:
        """通用保证金计算 - 🔥 修复：确保15%保证金率"""
        try:
            estimated_value = amount * price

            # 🔥 修复确认：通用计算使用15%保证金率
            margin_rate = 0.15  # 15%保证金率
            required_margin = estimated_value * margin_rate

            details = {
                "method": "generic_15_percent_fixed",
                "estimated_value": estimated_value,
                "margin_rate": margin_rate,
                "required_margin": required_margin
            }

            logger.info(f"✅ 通用保证金计算修复: 价值${estimated_value:.2f}, 保证金率15%, 需要保证金=${required_margin:.2f}")

            return required_margin, details

        except Exception as e:
            logger.error(f"通用保证金计算失败: {e}")
            # 🔥 修复确认：错误回退也使用15%保证金率
            estimated_value = amount * price
            conservative_margin = estimated_value * 0.15  # 15%保证金率
            details = {
                "method": "generic_error_fallback_15_percent",
                "error": str(e),
                "margin_rate": 0.15,
                "required_margin": conservative_margin
            }
            return conservative_margin, details
    
    async def check_available_margin(self,
                                   exchange_name: str,
                                   required_margin: float,
                                   exchange_client=None) -> Tuple[bool, float, Dict]:
        """
        检查可用保证金 - 🔥 修复余额缓存同步问题

        Args:
            exchange_name: 交易所名称
            required_margin: 所需保证金
            exchange_client: 交易所客户端实例

        Returns:
            (是否充足, 可用余额, 详细信息)
        """
        try:
            if not exchange_client:
                logger.warning(f"交易所客户端未提供: {exchange_name}")
                return False, 0.0, {"error": "交易所客户端未提供"}

            # 🔥 关键修复：强制刷新余额缓存，确保数据准确性
            logger.info(f"🔍 {exchange_name}保证金检查: 强制刷新余额缓存")

            # 🔥 修复：使用UnifiedBalanceManager强制刷新余额
            try:
                from core.unified_balance_manager import get_unified_balance_manager
                balance_manager = get_unified_balance_manager({exchange_name: exchange_client})

                # 强制刷新余额缓存
                unified_balances = await balance_manager.get_all_balances(force_refresh=True)

                # 获取对应交易所的余额
                account_key = None
                if hasattr(exchange_client, 'is_unified_account') and exchange_client.is_unified_account():
                    account_key = f"{exchange_name}_unified_usdt"
                else:
                    account_key = f"{exchange_name}_futures_usdt"

                available_usdt = unified_balances.get(account_key, 0.0)
                logger.info(f"🔍 {exchange_name}统一余额管理器: {account_key} = ${available_usdt:.2f}")

            except Exception as balance_error:
                logger.warning(f"⚠️ {exchange_name}统一余额管理器失败，使用直接API: {balance_error}")

                # 备用方案：直接调用交易所API
                if hasattr(exchange_client, 'is_unified_account') and exchange_client.is_unified_account():
                    from exchanges.exchanges_base import AccountType
                    balance = await exchange_client.get_balance(AccountType.UNIFIED)
                else:
                    from exchanges.exchanges_base import AccountType
                    balance = await exchange_client.get_balance(AccountType.FUTURES)

                usdt_info = balance.get("USDT", {})
                if isinstance(usdt_info, dict):
                    available_usdt = float(usdt_info.get("available", 0))
                else:
                    available_usdt = float(usdt_info) if usdt_info else 0

            # 🔥 关键修复：增加安全边际，避免边界情况
            safety_margin = max(required_margin * 0.05, 5.0)  # 5%安全边际，最少5 USDT
            effective_required = required_margin + safety_margin

            is_sufficient = available_usdt >= effective_required

            details = {
                "available_usdt": available_usdt,
                "required_margin": required_margin,
                "safety_margin": safety_margin,
                "effective_required": effective_required,
                "is_sufficient": is_sufficient,
                "margin_ratio": available_usdt / effective_required if effective_required > 0 else 0
            }

            if is_sufficient:
                logger.info(f"✅ {exchange_name}保证金充足: ${available_usdt:.2f} >= ${effective_required:.2f} (含安全边际${safety_margin:.2f})")
            else:
                logger.error(f"❌ {exchange_name}保证金不足: 需要${effective_required:.2f} (含安全边际), 可用${available_usdt:.2f}")

            return is_sufficient, available_usdt, details

        except Exception as e:
            logger.error(f"保证金检查失败 {exchange_name}: {e}")
            return False, 0.0, {"error": str(e)}

    # 🔥 新增：统一接口方法 - 按照三个交易所统一模块清单
    async def get_available_margin(self, exchange: str, account_type: str = "futures") -> float:
        """🔥 获取可用保证金 - 统一接口"""
        try:
            # 这里需要传入exchange实例，暂时返回0
            # 实际使用时应该从ArbitrageEngine的余额缓存获取
            self.logger.warning(f"获取可用保证金需要exchange实例: {exchange}")
            return 0.0
        except Exception as e:
            self.logger.error(f"获取可用保证金失败: {e}")
            return 0.0

    async def calculate_position_margin(self, symbol: str, exchange: str) -> Dict[str, float]:
        """🔥 计算持仓保证金 - 统一接口"""
        try:
            # 这里需要传入exchange实例，暂时返回空字典
            # 实际使用时应该从exchange获取持仓信息
            self.logger.warning(f"计算持仓保证金需要exchange实例: {exchange} {symbol}")
            return {}
        except Exception as e:
            self.logger.error(f"计算持仓保证金失败: {e}")
            return {}

    def calculate_liquidation_price(self, entry_price: float, leverage: int,
                                  side: str, margin_ratio: float = 0.05) -> float:
        """🔥 计算强平价格 - 统一接口"""
        try:
            if side.lower() == "long":
                # 多头强平价格
                liquidation_price = entry_price * (1 - margin_ratio)
            else:
                # 空头强平价格
                liquidation_price = entry_price * (1 + margin_ratio)

            return liquidation_price
        except Exception as e:
            self.logger.error(f"计算强平价格失败: {e}")
            return 0.0

    async def check_margin_sufficiency(self, symbol: str, amount: float,
                                     leverage: int, exchange: str) -> bool:
        """🔥 检查保证金是否充足 - 统一接口"""
        try:
            # 这里需要传入exchange实例，暂时返回True
            # 实际使用时应该调用calculate_required_margin和get_available_margin
            self.logger.warning(f"检查保证金充足性需要exchange实例: {exchange} {symbol}")
            return True
        except Exception as e:
            self.logger.error(f"检查保证金充足性失败: {e}")
            return False

    # 🔥 删除造轮子的缓存方法 - 直接使用OKX交易所现有的get_account_config方法和内置缓存

    async def _get_cached_account_config(self, exchange_client, exchange_name: str) -> Dict[str, Any]:
        """
        🔥 获取缓存的账户配置 - 使用现有保证金缓存系统架构，避免重复API调用
        
        基于现有的_get_contract_info方法模式实现，确保架构一致性
        """
        try:
            cache_key = f"{exchange_name}_account_config"
            current_time = time.time()
            
            # 检查缓存
            if cache_key in self.account_config_cache:
                cached_data, cache_time = self.account_config_cache[cache_key]
                if current_time - cache_time < self.cache_ttl:
                    # 🔥 使用现有的统一缓存监控系统记录命中
                    self.cache_monitor.log_margin_cache_hit(exchange_name, "account_config", cached_data)
                    logger.info(f"✅ [缓存命中] 保证金缓存: {exchange_name}_account_config | 账户配置从缓存获取，零延迟")
                    return cached_data
            
            # 🔥 使用现有的统一缓存监控系统记录未命中
            self.cache_monitor.log_margin_cache_miss(exchange_name, "account_config")
            
            # 从API获取
            if not exchange_client or not hasattr(exchange_client, 'get_account_config'):
                logger.warning(f"交易所客户端不支持get_account_config: {exchange_name}")
                return {"acctLv": "1"}  # 返回默认配置
            
            logger.info(f"🔍 获取账户配置: {exchange_name}")
            config = await exchange_client.get_account_config()
            
            if config:
                # 更新缓存
                self.account_config_cache[cache_key] = (config, current_time)
                logger.info(f"✅ [API调用] 保证金接口: {exchange_name}_account_config | 账户配置获取成功并缓存")
                return config
            else:
                logger.warning(f"⚠️ {exchange_name} 账户配置为空，使用默认配置")
                return {"acctLv": "1"}
                
        except Exception as e:
            logger.error(f"获取账户配置失败: {exchange_name} - {e}")
            # 返回默认配置
            return {"acctLv": "1"}

    # 🔥 新增：保证金问题诊断方法 - 解决反复出现的保证金问题
    async def diagnose_margin_issues(self,
                                   exchange_name: str,
                                   symbol: str,
                                   amount: float,
                                   price: float,
                                   exchange_client=None) -> Dict[str, Any]:
        """
        诊断保证金问题 - 🔥 找出保证金计算反复出错的根本原因

        这个方法会深度分析：
        1. 合约信息获取是否成功
        2. quanto_multiplier是否正确
        3. 保证金计算是否合理
        4. 余额缓存是否同步
        """
        diagnosis = {
            "exchange": exchange_name,
            "symbol": symbol,
            "amount": amount,
            "price": price,
            "issues": [],
            "recommendations": [],
            "contract_info_status": "unknown",
            "margin_calculation_status": "unknown",
            "balance_sync_status": "unknown"
        }

        try:
            logger.info(f"🔍 开始诊断保证金问题: {exchange_name} {symbol}")

            # 1. 检查合约信息获取
            contract_info = await self._get_contract_info(exchange_name, symbol, exchange_client)
            if not contract_info:
                diagnosis["contract_info_status"] = "failed"
                diagnosis["issues"].append("合约信息获取失败 - 这是保证金问题的主要原因")
                diagnosis["recommendations"].append("检查交易所API连接和交易对符号格式")
            else:
                diagnosis["contract_info_status"] = "success"
                diagnosis["contract_info"] = contract_info

                # 🔥 修复：根据交易所特性验证关键字段
                exchange_lower = exchange_name.lower()

                if exchange_lower == 'gate':
                    # 只有Gate.io需要验证quanto_multiplier
                    quanto_multiplier = contract_info.get('quanto_multiplier')
                    if not quanto_multiplier:
                        diagnosis["issues"].append("Gate.io quanto_multiplier缺失")
                        diagnosis["recommendations"].append("使用默认quanto_multiplier值")
                    else:
                        try:
                            multiplier_value = float(quanto_multiplier)
                            if multiplier_value <= 0:
                                diagnosis["issues"].append(f"Gate.io quanto_multiplier无效: {multiplier_value}")
                            elif multiplier_value > 1:
                                diagnosis["issues"].append(f"Gate.io quanto_multiplier异常大: {multiplier_value}")
                        except ValueError:
                            diagnosis["issues"].append(f"Gate.io quanto_multiplier格式错误: {quanto_multiplier}")
                elif exchange_lower == 'bybit':
                    # Bybit验证线性合约特有字段
                    if 'contract_type' in contract_info:
                        contract_type = contract_info.get('contract_type', '')
                        if 'Linear' not in contract_type:
                            diagnosis["issues"].append(f"Bybit合约类型异常: {contract_type}")
                elif exchange_lower == 'okx':
                    # OKX验证合约面值和乘数
                    contract_val = contract_info.get('contract_val')
                    contract_mult = contract_info.get('contract_mult')
                    if not contract_val:
                        diagnosis["issues"].append("OKX contract_val缺失")
                    if not contract_mult:
                        diagnosis["issues"].append("OKX contract_mult缺失")

            # 2. 测试保证金计算
            try:
                required_margin, calc_details = await self.calculate_required_margin(
                    exchange_name, symbol, amount, price, exchange_client
                )
                diagnosis["margin_calculation_status"] = "success"
                diagnosis["calculated_margin"] = required_margin
                diagnosis["calculation_details"] = calc_details

                # 检查保证金率合理性
                estimated_value = amount * price
                margin_rate = (required_margin / estimated_value) * 100 if estimated_value > 0 else 0

                if margin_rate < 5:
                    diagnosis["issues"].append(f"保证金率过低: {margin_rate:.2f}%")
                elif margin_rate > 80:
                    diagnosis["issues"].append(f"保证金率过高: {margin_rate:.2f}%")

            except Exception as calc_error:
                diagnosis["margin_calculation_status"] = "failed"
                diagnosis["issues"].append(f"保证金计算失败: {calc_error}")

            # 3. 检查余额同步
            if exchange_client:
                try:
                    is_sufficient, available_balance, balance_details = await self.check_available_margin(
                        exchange_name, required_margin, exchange_client
                    )
                    diagnosis["balance_sync_status"] = "success"
                    diagnosis["available_balance"] = available_balance
                    diagnosis["balance_sufficient"] = is_sufficient

                    if not is_sufficient:
                        diagnosis["issues"].append(f"余额不足: 需要${required_margin:.2f}, 可用${available_balance:.2f}")
                        diagnosis["recommendations"].append("增加保证金或减少交易量")

                except Exception as balance_error:
                    diagnosis["balance_sync_status"] = "failed"
                    diagnosis["issues"].append(f"余额检查失败: {balance_error}")

            # 4. 生成总结和建议
            if not diagnosis["issues"]:
                diagnosis["summary"] = "保证金系统正常，无发现问题"
            else:
                diagnosis["summary"] = f"发现{len(diagnosis['issues'])}个问题"
                diagnosis["recommendations"].extend([
                    "使用强制余额缓存刷新",
                    "验证交易对符号格式",
                    "检查网络连接稳定性",
                    "考虑增加保证金安全边际"
                ])

            logger.info(f"🔍 保证金诊断完成: {diagnosis['summary']}")
            return diagnosis

        except Exception as e:
            diagnosis["issues"].append(f"诊断过程异常: {e}")
            diagnosis["summary"] = "诊断失败"
            logger.error(f"保证金诊断异常: {e}")
            return diagnosis


# 全局实例
_margin_calculator = None

def get_margin_calculator() -> MarginCalculator:
    """获取保证金计算器实例"""
    global _margin_calculator
    if _margin_calculator is None:
        _margin_calculator = MarginCalculator()
    return _margin_calculator
