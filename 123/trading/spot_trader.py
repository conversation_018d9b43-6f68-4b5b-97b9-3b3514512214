"""
现货交易模块
负责执行现货交易订单，包括市价单、限价单
深度分析由统一深度分析器(UnifiedDepthAnalyzer)处理
"""

import asyncio
import time
import logging
import os
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal, ROUND_DOWN
from dataclasses import dataclass
from enum import Enum

from config.debug_config import DEBUG
from utils.logger import get_logger
from exchanges.exchanges_base import BaseExchange, OrderSide, OrderType, OrderStatus
# 🔥 删除硬编码精度导入：不再使用format_amount_unified

logger = get_logger(__name__)

# 🔥 删除OrderDepthInfo：深度分析由统一深度分析器处理
# 所有深度相关的数据结构都在core/unified_depth_analyzer.py中定义

@dataclass
class SpotOrderResult:
    """现货订单执行结果"""
    success: bool
    order_id: str
    symbol: str
    side: str
    quantity: float
    executed_quantity: float
    executed_price: float
    executed_amount: float
    fee: float
    timestamp: int
    error_message: str = ""
    # 🔥 删除depth_info：深度分析由统一深度分析器处理

class SpotTrader:
    """现货交易器"""
    
    def __init__(self, exchange_client):
        self.exchange = exchange_client
        # 🔥 修复：安全获取交易所名称，避免__class__.__name__导致的问题
        try:
            self.exchange_name = exchange_client.__class__.__name__.lower().replace('exchange', '')
            # 如果交易所名称为空或异常，使用默认名称
            if not self.exchange_name or self.exchange_name == '':
                self.exchange_name = 'unknown'
        except Exception:
            self.exchange_name = 'unknown'

        # 🔥 修复：使用安全的logger名称
        self.logger = get_logger(f"SpotTrader.{self.exchange_name}")
        
        # 🔥 重构修复：完全移除滑点保护变量
        # 根据重构方案文档："移除所有的滑点保护"
        # 交易参数配置 - 从环境变量读取
        # 🔥 限价单已彻底删除 - 纯市价单系统
        self.max_retries = int(os.getenv('SPOT_RETRY_TIMES', '3'))  # 🔥 统一参数名
        self.retry_delay = float(os.getenv('SPOT_RETRY_DELAY', '0.01'))  # 🔥 高性能修复：重试延迟减少到10ms
        self.order_timeout = int(os.getenv('SPOT_ORDER_TIMEOUT', '1'))  # 🔥 高性能修复：订单超时1秒
        # 🔥 删除拆单相关配置 - 遵循"简单交易逻辑：深度足够就执行，不足就跳过"原则
        # self.split_threshold = float(os.getenv('SPOT_SPLIT_THRESHOLD', '0.8'))  # 已删除
        # self.volume_check_ratio = float(os.getenv('SPOT_VOLUME_CHECK_RATIO', '0.5'))  # 已删除
        # self.min_execution_ratio = float(os.getenv('SPOT_MIN_EXECUTION_RATIO', '0.95'))  # 已删除
        
        # 性能监控
        self.execution_times = []
        self.success_count = 0
        self.failure_count = 0
        
        if DEBUG:
            self.logger.info(f"SpotTrader initialized for {self.exchange_name}")
    
    # 🔥 删除重复实现：深度分析应该使用统一深度分析器
    # SpotTrader不应该有自己的深度分析逻辑，违反MD文档01设计
    # 所有深度分析统一由UnifiedDepthAnalyzer处理
    

    
    def _get_pure_execution_price(self, side: OrderSide, orderbook: Dict, target_amount_usd: float = 100.0) -> float:
        """
        🔥 重构修复：获取30档算法的纯净执行价格

        根据重构方案文档："所有的差价计算不允许有滑点保护"
        "30档算法返回的加权平均价格就是纯净的真实执行价格"

        使用现有的UnifiedOrderSpreadCalculator的30档累积表算法计算单市场执行价格

        Args:
            side: 买卖方向
            orderbook: 订单簿数据
            target_amount_usd: 目标交易金额（用于30档计算）

        Returns:
            float: 30档算法计算的纯净执行价格
        """
        try:
            # 🔥 使用现有的30档累积表算法计算纯净执行价格
            from core.unified_order_spread_calculator import get_order_spread_calculator
            calculator = get_order_spread_calculator()

            # 确定交易方向对应的订单簿侧
            if side == OrderSide.BUY:
                # 买入需要检查卖单深度(asks)
                depth_side = "asks"
                depth_data = orderbook.get('asks', [])
            else:
                # 卖出需要检查买单深度(bids)
                depth_side = "bids"
                depth_data = orderbook.get('bids', [])

            if not depth_data:
                raise ValueError(f"无法获取{depth_side}价格数据")

            # 🔥 使用30档累积表算法计算加权平均执行价格
            cum_table = calculator.build_cumulative_table_30_levels(depth_data, side=depth_side)

            if len(cum_table) == 0:
                # 兜底：使用最优价格（无滑点保护）
                best_price = float(depth_data[0][0])
                self.logger.warning(f"⚠️ 30档累积表构建失败，使用最优价格: {best_price:.8f}")
                return best_price

            # 🔥 使用二分查找找到最优执行档位
            execution_level = calculator.find_optimal_execution_level(cum_table, target_amount_usd)

            if execution_level and execution_level.weighted_avg_price > 0:
                self.logger.debug(f"✅ 30档算法执行价格: {execution_level.weighted_avg_price:.8f} (使用{execution_level.execution_levels}档)")
                return execution_level.weighted_avg_price
            else:
                # 兜底：使用最优价格（无滑点保护）
                best_price = float(depth_data[0][0])
                self.logger.warning(f"⚠️ 30档算法执行档位查找失败，使用最优价格: {best_price:.8f}")
                return best_price

        except Exception as e:
            self.logger.error(f"❌ 获取纯净执行价格失败: {e}")
            # 最终兜底：使用最优价格（无滑点保护）
            depth_side = "asks" if side == OrderSide.BUY else "bids"
            depth_data = orderbook.get(depth_side, [])
            if depth_data:
                return float(depth_data[0][0])
            else:
                raise ValueError(f"无法获取{depth_side}价格数据")
    
    async def _execute_single_order(self, symbol: str, side: OrderSide,
                                   quantity: float, order_type: OrderType,
                                   price: Optional[float] = None,
                                   orderbook: Optional[Dict] = None) -> SpotOrderResult:
        """
        执行单个订单

        Args:
            symbol: 交易对
            side: 买卖方向
            quantity: 数量
            order_type: 订单类型
            price: 价格（限价单使用）
            orderbook: 订单簿数据（可选）

        Returns:
            SpotOrderResult: 执行结果
        """
        start_time = time.time()

        try:
            # 🔥 精度处理已由trading_rules_preloader统一处理，此处不再重复
            self.logger.debug(f"现货交易器: {symbol} {quantity}")

            # 🔥 准备params参数，包含orderbook数据
            params = {}
            if orderbook is not None:
                params['orderbook'] = orderbook

            # 🔥 直接调用交易所API，精度已由统一开仓管理器处理
            if hasattr(self.exchange, 'create_spot_order'):
                order_result = await self.exchange.create_spot_order(
                    symbol=symbol,
                    side=side,
                    order_type=order_type,
                    amount=quantity,  # 数量已由trading_rules_preloader格式化
                    price=price,
                    params=params
                )
            else:
                raise ValueError(f"交易所 {type(self.exchange).__name__} 不支持现货交易")
            
            execution_time = (time.time() - start_time) * 1000
            self.execution_times.append(execution_time)
            
            # 🔥 修复：不仅检查order_id，还要检查订单状态
            if order_result and order_result.get('order_id'):
                # 🔥 关键修复：检查订单状态
                order_status = order_result.get('status', '')
                
                # 🔥 修复：增加明确的状态分类，确保100%覆盖
                immediate_success_statuses = ['filled', 'closed', 'partially_filled']
                pending_statuses = ['open', 'pending', 'submitted']
                failed_statuses = ['rejected', 'canceled', 'cancelled', 'failed', 'error']  # 🔥 新增：明确的失败状态
                
                if order_status in immediate_success_statuses:
                    # 立即成功的订单
                    self.success_count += 1
                    
                    # 安全的字段解析，防止NoneType错误
                    def safe_float(value, default=0.0):
                        """安全转换为浮点数"""
                        if value is None or value == '':
                            return default
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return default
                    
                    # 🔥 统一修复：删除重复的交易所特殊处理，使用统一接口
                    # 所有交易所都应该在各自的exchange实现中处理API差异
                    # SpotTrader只使用统一的返回字段
                    
                    # 🔥 强化字段选择：优先级 + 合理性验证
                    selected_field = None
                    executed_qty = None
                    
                    # 按优先级尝试字段
                    for field_name in ['filled', 'executed_quantity', 'actual_amount', 'amount']:
                        field_value = order_result.get(field_name)
                        if field_value is not None and field_value > 0:
                            test_qty = safe_float(field_value, 0)
                            
                            # 🔥 合理性检查：确保数量不会异常大（避免选择USDT金额）
                            if test_qty <= quantity * 2:  # 允许2倍误差范围
                                executed_qty = test_qty
                                selected_field = field_name
                                break
                            else:
                                self.logger.warning(f"⚠️ 字段{field_name}数量异常: {test_qty} > {quantity * 2} (可能是金额字段)")
                    
                    # 🔥 兜底保护：如果所有字段都不合理，使用请求数量
                    if executed_qty is None:
                        executed_qty = quantity
                        selected_field = "fallback"
                        self.logger.error(f"❌ 所有数量字段都不合理，使用请求数量: {quantity}")
                    
                    self.logger.info(f"🔧 数量字段选择: {selected_field}={executed_qty:.8f} ({type(self.exchange).__name__})")
                    
                    # 🔥 修复：删除调试代码
                    


                    # 🔥 核心修复：完全移除滑点保护污染，只使用实际成交价格
                    executed_price_raw = None

                    # 优先获取实际成交价格字段
                    for price_field in ['executed_price', 'average', 'average_price', 'price']:
                        field_value = order_result.get(price_field)
                        if field_value is not None and field_value > 0:
                            executed_price_raw = field_value
                            self.logger.info(f"✅ 现货获取实际成交价格: {price_field}={executed_price_raw:.8f}")
                            break

                    # 🔥 核心修复：完全移除滑点保护拒绝逻辑，使用兜底机制确保交易继续
                    if executed_price_raw is None:
                        # 🔥 兜底机制：使用传入的price参数（30档算法计算的纯净执行价格）
                        executed_price_raw = price
                        self.logger.warning(f"⚠️ 无法获取{self.exchange_name}现货实际成交价格，使用30档算法价格: {executed_price_raw:.8f}")
                        self.logger.warning(f"⚠️ API响应: {order_result}")
                        self.logger.info(f"✅ 使用兜底机制继续执行，确保交易不中断")

                    executed_price = safe_float(executed_price_raw, 0)
                    
                    # 计算执行金额
                    executed_amount = executed_qty * executed_price
                    
                    result = SpotOrderResult(
                        success=True,
                        order_id=order_result.get('order_id', ''),
                        symbol=symbol,
                        side=side.value,
                        quantity=quantity,
                        executed_quantity=executed_qty,
                        executed_price=executed_price,
                        executed_amount=executed_amount,
                        fee=safe_float(order_result.get('fee'), 0.0),
                        timestamp=int(time.time() * 1000)
                    )
                    
                    # 🔥 修复：删除调试代码
                    self.logger.debug(f"Order executed: {symbol} {side.value} {result.executed_quantity}")
                    return result
                
                elif order_status in pending_statuses:
                    # 🔥 新增：处理pending状态的订单，等待成交
                    order_id = order_result.get('order_id', '')
                    self.logger.info(f"订单{order_id}状态为{order_status}，等待成交...")
                    
                    # 🔥 高性能修复：等待订单成交，最多等待100ms
                    wait_timeout = 0.1
                    wait_start = time.time()
                    
                    while (time.time() - wait_start) < wait_timeout:
                        try:
                            # 查询订单状态
                            updated_order = await self.exchange.get_order(order_id, symbol, market_type="spot")
                            if updated_order:
                                updated_status = updated_order.get('status', '')
                                self.logger.debug(f"订单{order_id}状态更新: {updated_status}")
                                
                                if updated_status in immediate_success_statuses:
                                    # 订单成功成交
                                    self.success_count += 1
                                    
                                    def safe_float(value, default=0.0):
                                        if value is None or value == '':
                                            return default
                                        try:
                                            return float(value)
                                        except (ValueError, TypeError):
                                            return default
                                    
                                    executed_qty = safe_float(updated_order.get('amount') or updated_order.get('executed_quantity') or updated_order.get('filled'), quantity)
                                    # 🔥 核心修复：完全移除滑点保护污染，只使用实际成交价格
                                    executed_price_raw = None

                                    # 优先获取实际成交价格字段
                                    for price_field in ['executed_price', 'average', 'average_price', 'price']:
                                        field_value = updated_order.get(price_field)
                                        if field_value is not None and field_value > 0:
                                            executed_price_raw = field_value
                                            self.logger.info(f"✅ 现货更新获取实际成交价格: {price_field}={executed_price_raw:.8f}")
                                            break

                                    # 🔥 核心修复：完全移除滑点保护拒绝逻辑，使用兜底机制确保交易继续
                                    if executed_price_raw is None:
                                        # 🔥 兜底机制：使用传入的price参数（30档算法计算的纯净执行价格）
                                        executed_price_raw = price
                                        self.logger.warning(f"⚠️ 无法获取{self.exchange_name}现货更新成交价格，使用30档算法价格: {executed_price_raw:.8f}")
                                        self.logger.info(f"✅ 使用兜底机制继续执行，确保交易不中断")

                                    executed_price = safe_float(executed_price_raw, 0)
                                    executed_amount = executed_qty * executed_price
                                    
                                    wait_time = (time.time() - wait_start) * 1000
                                    self.logger.info(f"✅ 订单{order_id}等待成交成功: {wait_time:.1f}ms")
                                    
                                    return SpotOrderResult(
                                        success=True,
                                        order_id=order_id,
                                        symbol=symbol,
                                        side=side.value,
                                        quantity=quantity,
                                        executed_quantity=executed_qty,
                                        executed_price=executed_price,
                                        executed_amount=executed_amount,
                                        fee=safe_float(updated_order.get('fee'), 0.0),
                                        timestamp=int(time.time() * 1000)
                                    )
                                
                                elif updated_status in failed_statuses:
                                    # 订单被取消或失败
                                    self.failure_count += 1
                                    error_detail = f"Order failed with status: {updated_status}"
                                    self.logger.error(f"订单{order_id}最终失败: {updated_status}")
                                    
                                    return SpotOrderResult(
                                        success=False,
                                        order_id=order_id,
                                        symbol=symbol,
                                        side=side.value,
                                        quantity=quantity,
                                        executed_quantity=0,
                                        executed_price=0,
                                        executed_amount=0,
                                        fee=0,
                                        timestamp=int(time.time() * 1000),
                                        error_message=error_detail
                                    )
                                
                            # � 高性能修复：减少查询等待时间到5ms
                            await asyncio.sleep(0.005)  # 5ms最小等待
                            
                        except Exception as query_error:
                            self.logger.warning(f"查询订单{order_id}状态失败: {query_error}")
                            # 🚀 手动极限优化：移除重试延迟，最大化速度
                            continue
                    
                    # 🔥 等待超时：按部分成功处理
                    wait_time = (time.time() - wait_start) * 1000
                    self.logger.warning(f"⚠️ 订单{order_id}等待超时({wait_time:.1f}ms)，按部分成功处理")
                    self.success_count += 1
                    
                    # 🔥 关键修复：对于超时的订单，使用订单请求的数量作为预估成交量
                    # 这避免了因为无法确认实际成交量而导致的执行失败
                    def safe_float(value, default=0.0):
                        if value is None or value == '':
                            return default
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return default
                    
                    estimated_price = price or 0
                    if estimated_price == 0:
                        # 尝试从原始订单结果获取价格
                        estimated_price = safe_float(order_result.get('price'), 0)
                    
                    return SpotOrderResult(
                        success=True,  # 🔥 修复：按成功处理
                        order_id=order_id,
                        symbol=symbol,
                        side=side.value,
                        quantity=quantity,
                        executed_quantity=quantity,  # 🔥 使用请求数量作为预估
                        executed_price=estimated_price,
                        executed_amount=quantity * estimated_price,
                        fee=quantity * estimated_price * 0.001,  # 预估手续费
                        timestamp=int(time.time() * 1000),
                        error_message=f"Order timeout but treated as success (wait_time: {wait_time:.1f}ms)"
                    )
                
                elif order_status in failed_statuses:
                    # 🔥 新增：明确处理失败状态
                    self.failure_count += 1
                    error_detail = f"Order failed with status: {order_status}"
                    if 'error' in order_result:
                        error_detail += f" - {order_result['error']}"
                    
                    self.logger.error(f"订单明确失败: {order_status}, 详情: {error_detail}")
                    
                    return SpotOrderResult(
                        success=False,
                        order_id=order_result.get('order_id', ''),
                        symbol=symbol,
                        side=side.value,
                        quantity=quantity,
                        executed_quantity=0,
                        executed_price=0,
                        executed_amount=0,
                        fee=0,
                        timestamp=int(time.time() * 1000),
                        error_message=error_detail
                    )
                
                else:
                    # 🔥 真正的未知状态（应该很少见）
                    self.failure_count += 1
                    error_detail = f"Order status not recognized: {order_status} (unknown status)"
                    self.logger.error(f"订单状态无法识别: {order_result}")
                    
                    return SpotOrderResult(
                        success=False,
                        order_id=order_result.get('order_id', ''),
                        symbol=symbol,
                        side=side.value,
                        quantity=quantity,
                        executed_quantity=0,
                        executed_price=0,
                        executed_amount=0,
                        fee=0,
                        timestamp=int(time.time() * 1000),
                        error_message=error_detail
                    )
            else:
                # 🔥 没有order_id的情况
                self.failure_count += 1
                error_detail = f"Order failed - order_id missing or empty"
                if order_result:
                    error_detail += f", response: {order_result}"
                else:
                    error_detail += ", no response received"
                    
                return SpotOrderResult(
                    success=False,
                    order_id="",
                    symbol=symbol,
                    side=side.value,
                    quantity=quantity,
                    executed_quantity=0,
                    executed_price=0,
                    executed_amount=0,
                    fee=0,
                    timestamp=int(time.time() * 1000),
                    error_message=error_detail
                )
                
        except Exception as e:
            self.failure_count += 1
            execution_time = (time.time() - start_time) * 1000
            self.execution_times.append(execution_time)
            
            # 详细的错误日志
            self.logger.error(f"Error executing spot order: {e}")
            self.logger.error(f"Order parameters: symbol={symbol}, side={side.value}, quantity={quantity}, price={price}")
            
            return SpotOrderResult(
                success=False,
                order_id="",
                symbol=symbol,
                side=side.value,
                quantity=quantity,
                executed_quantity=0,
                executed_price=0,
                executed_amount=0,
                fee=0,
                timestamp=int(time.time() * 1000),
                error_message=str(e)
            )
    
    async def execute_market_order(self, symbol: str, side: OrderSide, 
                                  quantity: float, orderbook: Dict) -> SpotOrderResult:
        """
        执行市价单（优先速度）
        
        Args:
            symbol: 交易对
            side: 买卖方向
            quantity: 数量
            orderbook: 订单簿数据
            
        Returns:
            SpotOrderResult: 执行结果
        """
        start_time = time.time()
        
        try:
            # 🔥 删除重复深度分析：深度检查应该在调用此方法之前完成
            # SpotTrader只负责执行交易，不负责深度分析

            # 🔥 重构修复：使用30档算法的纯净执行价格
            # 根据重构方案文档："扫描与执行一致：都使用相同的纯净价格"
            try:
                market_price = self._get_pure_execution_price(side, orderbook, 100.0)
                self.logger.info(f"✅ 使用30档纯净执行价格: {market_price:.8f}")
            except Exception as e:
                self.logger.error(f"❌ 获取30档执行价格失败: {e}")
                return SpotOrderResult(
                    success=False,
                    error_message=f"无法获取执行价格: {e}"
                )

            # 直接执行单笔订单
            result = await self._execute_single_order(
                symbol, side, quantity, OrderType.MARKET, market_price, orderbook
            )
            return result
                
        except Exception as e:
            self.logger.error(f"Error executing market order: {e}")
            return SpotOrderResult(
                success=False,
                order_id="",
                symbol=symbol,
                side=side.value,
                quantity=quantity,
                executed_quantity=0,
                executed_price=0,
                executed_amount=0,
                fee=0,
                timestamp=int(time.time() * 1000),
                error_message=str(e)
            )
    

    
    # 🔥 修复：删除拆单方法，遵循"删除拆单策略：强制单笔执行"设计
    # async def _execute_split_orders(...) - 已删除，强制单笔执行
    
    async def execute_with_retry(self, symbol: str, side: OrderSide, 
                                quantity: float, orderbook: Dict) -> SpotOrderResult:
        """
        带重试机制的订单执行
        
        Args:
            symbol: 交易对
            side: 买卖方向
            quantity: 数量
            orderbook: 订单簿数据
            
        Returns:
            SpotOrderResult: 执行结果
        """
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                # 🔥 纯市价单策略：所有尝试都使用市价单
                result = await self.execute_market_order(symbol, side, quantity, orderbook)
                
                if result.success:
                    if attempt > 0:
                        self.logger.info(f"Order succeeded on attempt {attempt + 1}")
                    return result
                else:
                    last_error = result.error_message
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
            
            # 等待后重试
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)
        
        # 所有重试都失败
        self.logger.error(f"All {self.max_retries} attempts failed for {symbol} {side.value}")
        return SpotOrderResult(
            success=False,
            order_id="",
            symbol=symbol,
            side=side.value,
            quantity=quantity,
            executed_quantity=0,
            executed_price=0,
            executed_amount=0,
            fee=0,
            timestamp=int(time.time() * 1000),
            error_message=f"All retries failed. Last error: {last_error}"
        )
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total_orders = self.success_count + self.failure_count
        success_rate = (self.success_count / total_orders * 100) if total_orders > 0 else 0
        avg_execution_time = sum(self.execution_times) / len(self.execution_times) if self.execution_times else 0
        
        return {
            "exchange": self.exchange_name,
            "total_orders": total_orders,
            "successful_orders": self.success_count,
            "failed_orders": self.failure_count,
            "success_rate": round(success_rate, 2),
            "average_execution_time_ms": round(avg_execution_time, 2),
            "min_execution_time_ms": min(self.execution_times) if self.execution_times else 0,
            "max_execution_time_ms": max(self.execution_times) if self.execution_times else 0
        }
    
    def reset_stats(self):
        """重置性能统计"""
        self.execution_times = []
        self.success_count = 0
        self.failure_count = 0
        self.logger.info("Performance stats reset")
    
    async def market_buy(self, symbol: str, amount: float, slippage: float = None, orderbook: Dict = None, disable_split: bool = True) -> Dict:
        """
        执行市价买单 - ExecutionEngine期望的接口
        
        Args:
            symbol: 交易对
            amount: 交易数量
            slippage: 保留参数(兼容性，已不使用)
            orderbook: 订单簿数据(可选，如果不传入则重新获取)
            disable_split: 是否禁用拆单(ExecutionEngine调用时设为True)
        
        Returns:
            Dict: 订单结果
        """
        try:
            # 🔥 修复：删除调试代码，遵循简化设计
            self.logger.debug(f"market_buy调用: {symbol}, amount={amount:.6f}")

            # 🔥 关键修复：优先使用传入的orderbook数据，避免重复获取
            if orderbook is not None:
                # 使用传入的orderbook数据
                self.logger.info(f"使用传入的订单簿数据: {symbol}, asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                
                # 验证传入的orderbook数据有效性
                if not orderbook.get('asks') or not orderbook.get('bids'):
                    self.logger.error(f"传入的订单簿数据无效: {orderbook}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'Invalid orderbook data provided'
                    }
            else:
                # 🔥 删除重复的深度数据获取 - 应该由ExecutionEngine预获取并传入
                error_msg = f"❌ 缺少预获取的订单簿数据，请确保ExecutionEngine正确传入orderbook参数"
                self.logger.error(error_msg)
                return {
                    'id': '',
                    'status': 'failed',
                    'error': error_msg
                }
            
            # 🔥 关键修复：当disable_split=True时，直接调用单笔执行
            if disable_split:
                self.logger.info(f"🎯 ExecutionEngine模式: 禁用拆单，直接执行单笔订单")
                
                # 🔥 重构修复：使用30档算法的纯净执行价格
                # 根据重构方案文档："移除所有的滑点保护"
                try:
                    market_price = self._get_pure_execution_price(OrderSide.BUY, orderbook, 100.0)
                    self.logger.info(f"✅ 买入使用30档纯净执行价格: {market_price:.8f}")
                except Exception as e:
                    self.logger.error(f"❌ 获取30档执行价格失败: {e}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'无法获取执行价格: {e}'
                    }
                
                # 直接调用_execute_single_order，完全跳过拆单逻辑
                result = await self._execute_single_order(
                    symbol=symbol,
                    side=OrderSide.BUY,
                    quantity=amount,
                    order_type=OrderType.MARKET,
                    price=market_price,
                    orderbook=orderbook  # 🔥 修复：传递orderbook参数
                )
                
                if result.success:
                    return {
                        'id': result.order_id,
                        'symbol': result.symbol,
                        'side': result.side,
                        'amount': result.executed_quantity,
                        'price': result.executed_price,
                        'status': 'filled',
                        'filled': result.executed_quantity,
                        'average': result.executed_price,
                        'timestamp': result.timestamp
                    }
                else:
                    return {
                        'id': result.order_id,
                        'status': 'failed',
                        'error': result.error_message
                    }
            
            # 🔧 使用统一的执行逻辑：amount统一表示币数量
            # 各交易所的特殊处理在其exchange实现中完成
            try:
                # 🔥 修复：使用统一深度分析器进行深度验证
                from core.unified_depth_analyzer import get_depth_analyzer
                depth_analyzer = get_depth_analyzer()

                depth_result = depth_analyzer.analyze_orderbook_depth(
                    orderbook, amount, "buy", 0.0, "spot", symbol
                )

                if not depth_result.is_sufficient:
                    # 深度不足，返回错误结果
                    self.logger.warning(f"{symbol} 深度不足: {depth_result.error_message}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': depth_result.error_message
                    }

                # 深度充足，执行市价买单
                result = await self.execute_market_order(
                    symbol=symbol,
                    side=OrderSide.BUY,
                    quantity=amount,
                    orderbook=orderbook
                )
                
            except Exception as e:
                self.logger.error(f"Error executing market order: {e}")
                return {
                    'id': '',
                    'status': 'failed',
                    'error': f"Error executing market order: {str(e)}"
                }
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'side': result.side,
                    'amount': result.executed_quantity,  # 实际执行的币数量
                    'price': result.executed_price,
                    'status': 'filled' if result.success else 'failed',
                    'filled': result.executed_quantity,
                    'average': result.executed_price,
                    'timestamp': result.timestamp
                }
            else:
                return {
                    'id': result.order_id,
                    'status': 'failed',
                    'error': result.error_message
                }
                
        except Exception as e:
            self.logger.error(f"market_buy error: {e}")
            return {
                'id': '',
                'status': 'failed', 
                'error': str(e)
            }
    
    async def market_sell(self, symbol: str, amount: float, slippage: float = None, orderbook: Dict = None, disable_split: bool = True) -> Dict:
        """
        执行市价卖单 - ExecutionEngine期望的接口
        
        Args:
            symbol: 交易对
            amount: 交易数量
            slippage: 保留参数(兼容性，已不使用)
            orderbook: 订单簿数据(可选，如果不传入则重新获取)
            disable_split: 是否禁用拆单(ExecutionEngine调用时设为True)
        
        Returns:
            Dict: 订单结果
        """
        try:
            # 🔥 修复：删除调试代码，遵循简化设计
            self.logger.debug(f"market_sell调用: {symbol}, amount={amount:.6f}")
            
            # 🔥 关键修复：优先使用传入的orderbook数据，避免重复获取
            if orderbook is not None:
                # 使用传入的orderbook数据
                self.logger.info(f"使用传入的订单簿数据: {symbol}, asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                
                # 验证传入的orderbook数据有效性
                if not orderbook.get('asks') or not orderbook.get('bids'):
                    self.logger.error(f"传入的订单簿数据无效: {orderbook}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'Invalid orderbook data provided'
                    }
            else:
                # 🔥 删除重复的深度数据获取 - 应该由ExecutionEngine预获取并传入
                error_msg = f"❌ 缺少预获取的订单簿数据，请确保ExecutionEngine正确传入orderbook参数"
                self.logger.error(error_msg)
                return {
                    'id': '',
                    'status': 'failed',
                    'error': error_msg
                }
            
            # 🔥 关键修复：当disable_split=True时，直接调用单笔执行
            if disable_split:
                self.logger.info(f"🎯 ExecutionEngine模式: 禁用拆单，直接执行单笔订单")
                
                # 🔥 重构修复：使用30档算法的纯净执行价格
                # 根据重构方案文档："移除所有的滑点保护"
                try:
                    market_price = self._get_pure_execution_price(OrderSide.SELL, orderbook, 100.0)
                    self.logger.info(f"✅ 卖出使用30档纯净执行价格: {market_price:.8f}")
                except Exception as e:
                    self.logger.error(f"❌ 获取30档执行价格失败: {e}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'无法获取执行价格: {e}'
                    }
                
                # 直接调用_execute_single_order，完全跳过拆单逻辑
                result = await self._execute_single_order(
                    symbol=symbol,
                    side=OrderSide.SELL,
                    quantity=amount,
                    order_type=OrderType.MARKET,
                    price=market_price,
                    orderbook=orderbook  # 🔥 修复：传递orderbook参数
                )
                
                if result.success:
                    return {
                        'id': result.order_id,
                        'symbol': result.symbol,
                        'side': result.side,
                        'amount': result.executed_quantity,
                        'price': result.executed_price,
                        'status': 'filled',
                        'filled': result.executed_quantity,
                        'average': result.executed_price,
                        'timestamp': result.timestamp
                    }
                else:
                    return {
                        'id': result.order_id,
                        'status': 'failed',
                        'error': result.error_message
                    }
            
            # 🔧 使用统一的执行逻辑：amount统一表示币数量
            # 各交易所的特殊处理在其exchange实现中完成
            try:
                # 🔥 修复：使用统一深度分析器进行深度验证
                from core.unified_depth_analyzer import get_depth_analyzer
                depth_analyzer = get_depth_analyzer()

                depth_result = depth_analyzer.analyze_orderbook_depth(
                    orderbook, amount, "sell", 0.0, "spot", symbol
                )

                if not depth_result.is_sufficient:
                    # 深度不足，返回错误结果
                    self.logger.warning(f"{symbol} 深度不足: {depth_result.error_message}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': depth_result.error_message
                    }

                # 深度充足，执行市价卖单
                result = await self.execute_market_order(
                    symbol=symbol,
                    side=OrderSide.SELL,
                    quantity=amount,
                    orderbook=orderbook
                )
                
            except Exception as e:
                self.logger.error(f"Error executing market order: {e}")
                return {
                    'id': '',
                    'status': 'failed',
                    'error': f"Error executing market order: {str(e)}"
                }
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'side': result.side,
                    'amount': result.executed_quantity,  # 实际执行的币数量
                    'price': result.executed_price,
                    'status': 'filled' if result.success else 'failed',
                    'filled': result.executed_quantity,
                    'average': result.executed_price,
                    'timestamp': result.timestamp
                }
            else:
                return {
                    'id': result.order_id,
                    'status': 'failed',
                    'error': result.error_message
                }
                
        except Exception as e:
            self.logger.error(f"market_sell error: {e}")
            return {
                'id': '',
                'status': 'failed', 
                'error': str(e)
            }

    async def get_balance(self, currency: str = "USDT") -> Dict:
        """
        获取现货余额 - 使用统一余额管理器
        
        Args:
            currency: 货币符号，默认USDT
            
        Returns:
            Dict: 余额信息
        """
        try:
            # 🔥 修复：使用统一余额管理器，符合18个核心统一模块规范
            from core.unified_balance_manager import get_unified_balance_manager
            balance_manager = get_unified_balance_manager()
            
            if hasattr(self.exchange, 'is_unified_account') and self.exchange.is_unified_account():
                # 统一账户
                from exchanges.exchanges_base import AccountType
                balance = await balance_manager.get_balance_unified(self.exchange, AccountType.UNIFIED)
            else:
                # 现货账户
                from exchanges.exchanges_base import AccountType
                balance = await balance_manager.get_balance_unified(self.exchange, AccountType.SPOT)
            
            # 统一返回格式
            if currency in balance:
                currency_balance = balance[currency]
                if isinstance(currency_balance, dict):
                    return {
                        'available': currency_balance.get('available', 0),
                        'locked': currency_balance.get('locked', 0),
                        'total': currency_balance.get('available', 0) + currency_balance.get('locked', 0)
                    }
                else:
                    # 简单数值格式
                    return {
                        'available': float(currency_balance) if currency_balance else 0,
                        'locked': 0,
                        'total': float(currency_balance) if currency_balance else 0
                    }
            else:
                return {
                    'available': 0,
                    'locked': 0,
                    'total': 0
                }
                
        except Exception as e:
            self.logger.error(f"Error getting balance: {e}")
            return {
                'available': 0,
                'locked': 0,
                'total': 0
            }

# 测试和调试功能
async def test_spot_trader():
    """测试现货交易器"""
    logger.info("Testing SpotTrader...")
    
    # 这里需要实际的交易所客户端
    # 示例：trader = SpotTrader(gate_exchange_client)
    
    # 测试深度分析
    mock_orderbook = {
        "asks": [
            ["50000.0", "1.0"],
            ["50001.0", "2.0"],
            ["50002.0", "1.5"],
            ["50003.0", "0.8"],
            ["50004.0", "1.2"]
        ],
        "bids": [
            ["49999.0", "1.1"],
            ["49998.0", "2.1"],
            ["49997.0", "1.6"],
            ["49996.0", "0.9"],
            ["49995.0", "1.3"]
        ]
    }
    
    # 创建模拟交易器进行测试
    class MockExchange:
        async def create_spot_order(self, **kwargs):
            return {
                'success': True,
                'order_id': 'test_order_123',
                'executed_quantity': kwargs['quantity'],
                'executed_price': 50000.0,
                'executed_amount': kwargs['quantity'] * 50000.0,
                'fee': kwargs['quantity'] * 50000.0 * 0.001
            }
    
    mock_exchange = MockExchange()
    trader = SpotTrader(mock_exchange)
    
    # 🔥 修复：测试统一深度分析器
    from core.unified_depth_analyzer import get_depth_analyzer
    depth_analyzer = get_depth_analyzer()

    depth_result = depth_analyzer.analyze_orderbook_depth(
        mock_orderbook, 0.5, "buy", 0.0, "spot", "BTCUSDT"
    )

    logger.info(f"Depth analysis result: is_sufficient={depth_result.is_sufficient}, "
               f"total_volume={depth_result.total_volume}, effective_volume={depth_result.effective_volume}")

    assert depth_result.total_volume > 0, "Total volume should be greater than 0"
    assert depth_result.is_sufficient, "Depth should be sufficient for test amount"
    
    logger.info("✅ SpotTrader test completed successfully")

if __name__ == '__main__':
    asyncio.run(test_spot_trader())
