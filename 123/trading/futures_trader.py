"""
期货交易模块
负责执行期货交易订单，实现完美对冲逻辑
"""

import asyncio
import time
import logging
import os
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal, ROUND_DOWN
from dataclasses import dataclass
from enum import Enum

from config.debug_config import DEBUG
from utils.logger import get_logger

logger = get_logger(__name__)

class FuturesOrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class FuturesOrderType(Enum):
    MARKET = "market"
    # 🔥 限价单已删除 - 纯市价单策略

class PositionSide(Enum):
    LONG = "long"
    SHORT = "short"

@dataclass
class FuturesOrderResult:
    """期货订单执行结果"""
    success: bool
    order_id: str
    symbol: str
    side: str
    position_side: str
    quantity: float
    executed_quantity: float
    executed_price: float
    executed_amount: float
    fee: float
    timestamp: int
    match_spot_quantity: float = 0  # 匹配的现货数量
    match_spot_amount: float = 0    # 匹配的现货金额
    error_message: str = ""

@dataclass
class HedgeMatchInfo:
    """对冲匹配信息"""
    spot_quantity: float
    spot_price: float
    spot_amount: float
    target_futures_quantity: float
    futures_side: FuturesOrderSide
    position_side: PositionSide
    perfect_match: bool = False

class FuturesTrader:
    """期货交易器"""
    
    def __init__(self, exchange_client):
        self.exchange = exchange_client
        # 🔥 修复：安全获取交易所名称，避免__class__.__name__导致的问题
        try:
            self.exchange_name = exchange_client.__class__.__name__.lower().replace('exchange', '')
            # 如果交易所名称为空或异常，使用默认名称
            if not self.exchange_name or self.exchange_name == '':
                self.exchange_name = 'unknown'
        except Exception:
            self.exchange_name = 'unknown'

        # 🔥 修复：使用安全的logger名称
        self.logger = get_logger(f"FuturesTrader.{self.exchange_name}")
        
        # 🔥 重构修复：完全移除滑点保护变量
        # 根据重构方案文档："移除所有的滑点保护"
        # 交易参数 - 🔥 纯市价单策略
        self.retry_times = int(os.getenv('FUTURES_RETRY_TIMES', '3'))
        self.retry_delay = float(os.getenv('FUTURES_RETRY_DELAY', '0.01'))  # 🔥 高性能修复：重试延迟减少到10ms
        self.execution_timeout = int(os.getenv('FUTURES_EXECUTION_TIMEOUT', '1'))  # 🔥 高性能修复：执行超时1秒
        
        # 完美对冲参数
        self.quantity_tolerance = float(os.getenv('QUANTITY_TOLERANCE', '0.00001'))  # 数量容忍度
        self.amount_tolerance = float(os.getenv('AMOUNT_TOLERANCE', '0.01'))  # 金额容忍度
        
        # 性能监控
        self.execution_times = []
        self.hedge_delays = []  # 对冲延迟记录
        self.success_count = 0
        self.failure_count = 0
        self.perfect_hedge_count = 0
        
        if DEBUG:
            self.logger.info(f"FuturesTrader initialized for {self.exchange_name}")
    
    def _calculate_hedge_info(self, spot_result: Dict) -> HedgeMatchInfo:
        """
        根据现货成交结果计算期货对冲信息
        
        Args:
            spot_result: 现货成交结果
            
        Returns:
            HedgeMatchInfo: 对冲匹配信息
        """
        try:
            spot_side = spot_result.get('side', '').lower()
            spot_quantity = float(spot_result.get('executed_quantity', 0))
            spot_price = float(spot_result.get('executed_price', 0))
            spot_amount = float(spot_result.get('executed_amount', 0))
            
            # 现货买入 -> 期货做空对冲
            # 现货卖出 -> 期货做多对冲
            if spot_side == 'buy':
                futures_side = FuturesOrderSide.SELL
                position_side = PositionSide.SHORT
            else:
                futures_side = FuturesOrderSide.BUY
                position_side = PositionSide.LONG
            
            return HedgeMatchInfo(
                spot_quantity=spot_quantity,
                spot_price=spot_price,
                spot_amount=spot_amount,
                target_futures_quantity=spot_quantity,  # 完美对冲，数量一致
                futures_side=futures_side,
                position_side=position_side
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating hedge info: {e}")
            raise
    

    
    def _get_pure_execution_price(self, side: FuturesOrderSide, orderbook: Dict, target_amount_usd: float = 100.0) -> float:
        """
        🔥 重构修复：获取30档算法的纯净执行价格

        根据重构方案文档："所有的差价计算不允许有滑点保护"
        "30档算法返回的加权平均价格就是纯净的真实执行价格"

        使用现有的UnifiedOrderSpreadCalculator的30档累积表算法计算单市场执行价格

        Args:
            side: 期货订单方向
            orderbook: 订单簿数据
            target_amount_usd: 目标交易金额（用于30档计算）

        Returns:
            float: 30档算法计算的纯净执行价格
        """
        try:
            # 🔥 使用现有的30档累积表算法计算纯净执行价格
            from core.unified_order_spread_calculator import get_order_spread_calculator
            calculator = get_order_spread_calculator()

            # 确定交易方向对应的订单簿侧
            if side == FuturesOrderSide.BUY:
                # 买入需要检查卖单深度(asks)
                depth_side = "asks"
                depth_data = orderbook.get('asks', [])
            else:
                # 卖出需要检查买单深度(bids)
                depth_side = "bids"
                depth_data = orderbook.get('bids', [])

            if not depth_data:
                raise ValueError(f"无法获取{depth_side}价格数据")

            # 🔥 使用30档累积表算法计算加权平均执行价格
            cum_table = calculator.build_cumulative_table_30_levels(depth_data, side=depth_side)

            if len(cum_table) == 0:
                # 兜底：使用最优价格（无滑点保护）
                best_price = float(depth_data[0][0])
                self.logger.warning(f"⚠️ 30档累积表构建失败，使用最优价格: {best_price:.8f}")
                return best_price

            # 🔥 使用二分查找找到最优执行档位
            execution_level = calculator.find_optimal_execution_level(cum_table, target_amount_usd)

            if execution_level and execution_level.weighted_avg_price > 0:
                self.logger.debug(f"✅ 30档算法执行价格: {execution_level.weighted_avg_price:.8f} (使用{execution_level.execution_levels}档)")
                return execution_level.weighted_avg_price
            else:
                # 兜底：使用最优价格（无滑点保护）
                best_price = float(depth_data[0][0])
                self.logger.warning(f"⚠️ 30档算法执行档位查找失败，使用最优价格: {best_price:.8f}")
                return best_price

        except Exception as e:
            self.logger.error(f"❌ 获取纯净执行价格失败: {e}")
            # 最终兜底：使用最优价格（无滑点保护）
            depth_side = "asks" if side == FuturesOrderSide.BUY else "bids"
            depth_data = orderbook.get(depth_side, [])
            if depth_data:
                return float(depth_data[0][0])
            else:
                raise ValueError(f"无法获取{depth_side}价格数据")
    
    async def _execute_single_futures_order(self, symbol: str, side: FuturesOrderSide,
                                           position_side: PositionSide, quantity: float,
                                           order_type: FuturesOrderType,
                                           price: Optional[float] = None,
                                           params: Dict = None,
                                           orderbook: Optional[Dict] = None) -> FuturesOrderResult:
        """
        执行单个期货订单
        
        Args:
            symbol: 交易对
            side: 订单方向
            position_side: 仓位方向
            quantity: 数量
            order_type: 订单类型
            price: 价格（限价单使用）
            params: 额外参数(可选)，包括reduce_only等参数
            
        Returns:
            FuturesOrderResult: 执行结果
        """
        start_time = time.time()
        
        try:
            # 🔥 删除重复精度处理：统一开仓管理器已处理
            formatted_quantity = quantity
            
            # 转换为BaseExchange期望的参数格式
            from exchanges.exchanges_base import OrderSide as BaseOrderSide, OrderType as BaseOrderType
            
            # 转换订单方向
            base_side = BaseOrderSide.BUY if side == FuturesOrderSide.BUY else BaseOrderSide.SELL
            
            # 转换订单类型 - 🔥 纯市价单策略
            if order_type != FuturesOrderType.MARKET:
                raise ValueError("🚫 期货交易只支持市价单")
            base_order_type = BaseOrderType.MARKET
            
            # 准备额外参数（期货特有）
            if params:
                # 🔥 关键修复：避免重复设置reduce_only参数
                # Bybit交易所会自动处理reduceOnly参数，不需要在这里重复设置
                reduce_only_value = params.get("reduce_only", False)
                # 移除: if "reduce_only" not in params: params["reduce_only"] = False
                # 🔥 修复：不要在params中重复设置reduce_only，让Bybit交易所层面处理
                self.logger.info(f"🎯 期货订单参数处理: reduce_only={reduce_only_value} (避免参数重复)")
            else:
                params = {
                    # 移除: "reduce_only": False  # 🔥 不在这里设置，让交易所层面处理
                }
                # 🔥 如果没有params，创建空字典即可，交易所会设置默认的reduceOnly值

            # 🔥 关键修复：添加position_side信息到params中，供OKX使用
            params['position_side'] = position_side.value
            self.logger.info(f"🔧 传递position_side参数给期货交易所: {position_side.value}")

            # 🔥 修复：添加orderbook参数到params中
            if orderbook is not None:
                params['orderbook'] = orderbook
                self.logger.debug(f"🔍 传递orderbook参数给期货交易所: {len(orderbook.get('asks', []))} asks, {len(orderbook.get('bids', []))} bids")
            
            # 🔥 关键修复：直接调用交易所的底层API，避免循环调用统一开仓管理器
            # 不能调用exchange.place_order，因为那会再次调用统一开仓管理器，形成无限循环
            if hasattr(self.exchange, 'create_futures_order'):
                # 使用专门的期货下单方法
                order_result = await self.exchange.create_futures_order(
                    symbol=symbol,
                    side=base_side,
                    order_type=base_order_type,
                    amount=formatted_quantity,
                    price=price,
                    params=params
                )
            else:
                # 如果没有专门的期货下单方法，抛出错误
                raise ValueError(f"交易所 {type(self.exchange).__name__} 不支持期货交易")
            
            execution_time = (time.time() - start_time) * 1000
            self.execution_times.append(execution_time)
            
            if order_result and order_result.get('order_id'):
                self.success_count += 1
                
                # 安全的字段解析，防止NoneType错误
                def safe_float(value, default=0.0):
                    """安全转换为浮点数"""
                    if value is None or value == '':
                        return default
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return default
                
                # 统一字段名映射 - 处理不同交易所的字段差异
                executed_qty = safe_float(order_result.get('amount') or order_result.get('executed_quantity') or order_result.get('filled'), quantity)

                # 🔥 核心修复：完全移除滑点保护污染，只使用实际成交价格
                # 根据用户要求：确保整个项目完全移除滑点保护！
                executed_price_raw = None

                # 1. 优先获取实际成交价格字段（Bybit和OKX已修复，会返回这些字段）
                for price_field in ['executed_price', 'average', 'average_price', 'fill_price', 'avgPx']:
                    field_value = order_result.get(price_field)
                    if field_value is not None and field_value > 0:
                        executed_price_raw = field_value
                        self.logger.info(f"✅ 获取实际成交价格: {price_field}={executed_price_raw:.8f}")
                        break

                # 2. 如果没有实际成交价格，尝试从price字段获取（但排除0值）
                if executed_price_raw is None:
                    api_price = order_result.get('price')
                    if api_price is not None and api_price > 0:
                        executed_price_raw = api_price
                        self.logger.info(f"✅ 使用API价格字段: price={executed_price_raw:.8f}")

                # 3. 🔥 核心修复：完全移除滑点保护拒绝逻辑，使用兜底机制确保交易继续
                if executed_price_raw is None:
                    # 🔥 兜底机制：使用传入的price参数（30档算法计算的纯净执行价格）
                    executed_price_raw = price
                    self.logger.warning(f"⚠️ 无法获取{self.exchange_name}期货实际成交价格，使用30档算法价格: {executed_price_raw:.8f}")
                    self.logger.warning(f"⚠️ API响应: {order_result}")
                    self.logger.info(f"✅ 使用兜底机制继续执行，确保交易不中断")

                executed_price = safe_float(executed_price_raw, price or 0)
                
                # 计算执行金额
                executed_amount = executed_qty * executed_price
                
                result = FuturesOrderResult(
                    success=True,
                    order_id=order_result.get('order_id', ''),
                    symbol=symbol,
                    side=side.value,
                    position_side=position_side.value,
                    quantity=quantity,
                    executed_quantity=executed_qty,
                    executed_price=executed_price,
                    executed_amount=executed_amount,
                    fee=safe_float(order_result.get('fee'), 0.0),
                    timestamp=int(time.time() * 1000)
                )
                
                if DEBUG:
                    self.logger.info(f"Futures order executed: {symbol} {side.value}/{position_side.value} "
                                   f"{result.executed_quantity} @ {result.executed_price} "
                                   f"(execution time: {execution_time:.2f}ms)")
                    self.logger.debug(f"原始期货API响应: {order_result}")
                return result
            else:
                self.failure_count += 1
                # 改进错误信息记录
                error_detail = f"Futures order failed - order_id missing or empty"
                if order_result:
                    error_detail += f", response: {order_result}"
                else:
                    error_detail += ", no response received"
                    
                return FuturesOrderResult(
                    success=False,
                    order_id="",
                    symbol=symbol,
                    side=side.value,
                    position_side=position_side.value,
                    quantity=quantity,
                    executed_quantity=0,
                    executed_price=0,
                    executed_amount=0,
                    fee=0,
                    timestamp=int(time.time() * 1000),
                    error_message=error_detail
                )
                
        except Exception as e:
            self.failure_count += 1
            execution_time = (time.time() - start_time) * 1000
            self.execution_times.append(execution_time)
            
            # 详细的错误日志
            self.logger.error(f"Error executing futures order: {e}")
            self.logger.error(f"Order parameters: symbol={symbol}, side={side.value}, position_side={position_side.value}, quantity={quantity}, price={price}")
            
            return FuturesOrderResult(
                success=False,
                order_id="",
                symbol=symbol,
                side=side.value,
                position_side=position_side.value,
                quantity=quantity,
                executed_quantity=0,
                executed_price=0,
                executed_amount=0,
                fee=0,
                timestamp=int(time.time() * 1000),
                error_message=str(e)
            )
    
    async def execute_hedge_order(self, spot_result: Dict, orderbook: Dict, 
                                 symbol: str, hedge_start_time: float) -> FuturesOrderResult:
        """
        执行对冲订单（现货成交后<80ms内发出期货订单）
        
        Args:
            spot_result: 现货成交结果
            orderbook: 期货订单簿数据
            symbol: 期货交易对
            hedge_start_time: 对冲开始时间
            
        Returns:
            FuturesOrderResult: 期货执行结果
        """
        try:
            # 计算对冲信息
            hedge_info = self._calculate_hedge_info(spot_result)
            
            # 记录对冲延迟
            hedge_delay = (time.time() - hedge_start_time) * 1000
            self.hedge_delays.append(hedge_delay)
            
            if hedge_delay > self.execution_timeout:
                self.logger.warning(f"Execution timeout exceeded: {hedge_delay:.2f}ms")
            
            # 获取最优价格
            if hedge_info.futures_side == FuturesOrderSide.BUY:
                best_price = float(orderbook.get('asks', [[0, 0]])[0][0])
            else:
                best_price = float(orderbook.get('bids', [[0, 0]])[0][0])
            
            if best_price <= 0:
                raise ValueError("Invalid orderbook data")
            
            # 🔥 重构修复：使用30档算法的纯净执行价格
            # 根据重构方案文档："扫描与执行一致：都使用相同的纯净价格"
            market_price = self._get_pure_execution_price(hedge_info.futures_side, orderbook, 100.0)
            self.logger.info(f"✅ 使用30档纯净执行价格: {market_price:.8f}")
            market_result = await self._execute_single_futures_order(
                symbol, hedge_info.futures_side, hedge_info.position_side,
                hedge_info.target_futures_quantity, FuturesOrderType.MARKET, market_price,
                params=None, orderbook=orderbook  # 🔥 修复：传递orderbook参数
            )
            
            if market_result.success:
                # 验证完美对冲
                is_perfect_hedge = self._verify_perfect_hedge(spot_result, market_result)
                if is_perfect_hedge:
                    self.perfect_hedge_count += 1
                
                market_result.match_spot_quantity = hedge_info.spot_quantity
                market_result.match_spot_amount = hedge_info.spot_amount
                
                if DEBUG:
                    self.logger.info(f"Hedge completed via market order")
                
                return market_result
            else:
                self.logger.error(f"市价单对冲失败")
                return market_result
            
        except Exception as e:
            self.logger.error(f"Error executing hedge order: {e}")
            return FuturesOrderResult(
                success=False,
                order_id="",
                symbol=symbol,
                side="",
                position_side="",
                quantity=0,
                executed_quantity=0,
                executed_price=0,
                executed_amount=0,
                fee=0,
                timestamp=int(time.time() * 1000),
                error_message=str(e)
            )
    
    def _verify_perfect_hedge(self, spot_result: Dict, futures_result: FuturesOrderResult) -> bool:
        """
        🔥 简化对冲验证 - 重复验证已删除

        Args:
            spot_result: 现货成交结果
            futures_result: 期货成交结果

        Returns:
            bool: 是否执行成功（对冲质量已由hedge_calculator统一验证）
        """
        try:
            # 🔥 删除重复验证：对冲质量检查已在ExecutionEngine._pre_check_hedge_quality()中统一完成
            # 此处只检查基本的执行成功状态
            self.logger.info(f"✅ 对冲质量检查已由统一系统完成，此处仅验证执行状态")

            return (spot_result and futures_result and
                   spot_result.get("executed_quantity", 0) > 0 and
                   futures_result.executed_quantity > 0)

        except Exception as e:
            self.logger.error(f"Error verifying hedge: {e}")
            return False
    
    async def execute_with_retry(self, spot_result: Dict, orderbook: Dict, 
                                symbol: str, hedge_start_time: float) -> FuturesOrderResult:
        """
        带重试机制的对冲订单执行
        
        Args:
            spot_result: 现货成交结果
            orderbook: 期货订单簿数据
            symbol: 期货交易对
            hedge_start_time: 对冲开始时间
            
        Returns:
            FuturesOrderResult: 执行结果
        """
        last_error = None
        
        for attempt in range(self.retry_times):
            try:
                result = await self.execute_hedge_order(
                    spot_result, orderbook, symbol, hedge_start_time
                )
                
                if result.success:
                    if attempt > 0:
                        self.logger.info(f"Hedge order succeeded on attempt {attempt + 1}")
                    return result
                else:
                    last_error = result.error_message
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"Hedge attempt {attempt + 1} failed: {e}")
            
            # 等待后重试
            if attempt < self.retry_times - 1:
                await asyncio.sleep(self.retry_delay)
        
        # 所有重试都失败
        self.logger.error(f"All {self.retry_times} hedge attempts failed")
        return FuturesOrderResult(
            success=False,
            order_id="",
            symbol=symbol,
            side="",
            position_side="",
            quantity=0,
            executed_quantity=0,
            executed_price=0,
            executed_amount=0,
            fee=0,
            timestamp=int(time.time() * 1000),
            error_message=f"All hedge retries failed. Last error: {last_error}"
        )
    
    async def close_position(self, symbol: str, position_info: Dict, 
                           orderbook: Dict) -> FuturesOrderResult:
        """
        平仓操作
        
        Args:
            symbol: 交易对
            position_info: 仓位信息
            orderbook: 订单簿数据
            
        Returns:
            FuturesOrderResult: 平仓结果
        """
        try:
            position_side = position_info.get('position_side', '').lower()
            
            # 🔥 修复：安全处理position size，防止None值
            size_value = position_info.get('size', 0)
            try:
                position_size = abs(float(size_value)) if size_value is not None else 0.0
            except (ValueError, TypeError):
                self.logger.warning(f"Invalid position size in close_position: {size_value}, treating as 0")
                position_size = 0.0
            
            if position_size <= 0:
                self.logger.info(f"No position to close: size={position_size}")
                return FuturesOrderResult(
                    success=True,
                    order_id="NO_POSITION",
                    symbol=symbol,
                    side="",
                    position_side="",
                    quantity=0,
                    executed_quantity=0,
                    executed_price=0,
                    executed_amount=0,
                    fee=0,
                    timestamp=int(time.time() * 1000)
                )
            
            # 确定平仓方向
            if position_side == 'long':
                close_side = FuturesOrderSide.SELL
                close_position_side = PositionSide.LONG
            else:
                close_side = FuturesOrderSide.BUY
                close_position_side = PositionSide.SHORT
            
            # 获取最优价格
            if close_side == FuturesOrderSide.BUY:
                best_price = float(orderbook.get('asks', [[0, 0]])[0][0])
            else:
                best_price = float(orderbook.get('bids', [[0, 0]])[0][0])
            
            # 🔥 重构修复：平仓也使用30档算法的纯净执行价格
            # 根据重构方案文档："移除所有的滑点保护"
            market_price = self._get_pure_execution_price(close_side, orderbook, 100.0)
            self.logger.info(f"✅ 平仓使用30档纯净执行价格: {market_price:.8f}")
            
            # 转换为BaseExchange期望的参数格式
            from exchanges.exchanges_base import OrderSide as BaseOrderSide, OrderType as BaseOrderType
            
            # 转换订单方向
            base_side = BaseOrderSide.BUY if close_side == FuturesOrderSide.BUY else BaseOrderSide.SELL
            
            # 准备平仓参数
            params = {
                # 移除: "reduce_only": True  # 🔥 不在这里设置，让Bybit交易所层面处理
                "is_closing": True  # 🔥 使用明确的标识，让交易所层面转换为reduceOnly
            }
            
            # 调用交易所的place_order方法进行平仓
            order_result = await self.exchange.place_order(
                symbol=symbol,
                side=base_side,
                order_type=BaseOrderType.MARKET,
                amount=position_size,
                price=market_price,
                market_type="futures",
                params=params
            )
            
            if order_result and order_result.get('order_id'):
                result = FuturesOrderResult(
                    success=True,
                    order_id=order_result.get('order_id', ''),
                    symbol=symbol,
                    side=close_side.value,
                    position_side=close_position_side.value,
                    quantity=position_size,
                    executed_quantity=float(order_result.get('amount', position_size)),
                    executed_price=float(order_result.get('price', market_price or 0)),
                    executed_amount=float(order_result.get('amount', position_size)) * float(order_result.get('price', market_price or 0)),
                    fee=0.0,
                    timestamp=int(time.time() * 1000)
                )
                
                if DEBUG:
                    self.logger.info(f"Position closed: {symbol} {position_side} "
                                   f"{result.executed_quantity} @ {result.executed_price}")
                
                return result
            else:
                return FuturesOrderResult(
                    success=False,
                    order_id="",
                    symbol=symbol,
                    side=close_side.value,
                    position_side=close_position_side.value,
                    quantity=position_size,
                    executed_quantity=0,
                    executed_price=0,
                    executed_amount=0,
                    fee=0,
                    timestamp=int(time.time() * 1000),
                    error_message=f"Close position failed: {order_result}"
                )
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return FuturesOrderResult(
                success=False,
                order_id="",
                symbol=symbol,
                side="",
                position_side="",
                quantity=0,
                executed_quantity=0,
                executed_price=0,
                executed_amount=0,
                fee=0,
                timestamp=int(time.time() * 1000),
                error_message=str(e)
            )
    
    async def force_close_all_positions(self, symbol: str) -> List[FuturesOrderResult]:
        """
        强制平仓所有仓位
        
        Args:
            symbol: 交易对
            
        Returns:
            List[FuturesOrderResult]: 平仓结果列表
        """
        try:
            # 获取当前仓位
            if hasattr(self.exchange, 'get_position'):
                positions = await self.exchange.get_position(symbol)
            else:
                self.logger.error("Exchange doesn't support get_position")
                return []
            
            if not positions:
                self.logger.info("No positions to close")
                return []
            
            # 🔥 修复：使用传入的WebSocket订单簿数据，禁止REST API调用
            if orderbook is None:
                self.logger.error("❌ 期货平仓缺少WebSocket订单簿数据，无法执行")
                return []

            if not orderbook.get('asks') or not orderbook.get('bids'):
                self.logger.error("❌ WebSocket订单簿数据无效，无法执行期货平仓")
                return []
            
            # 并发平仓所有仓位
            close_tasks = []
            for position in positions:
                # 🔥 修复：安全处理position size，防止None值
                size_value = position.get('size', 0)
                try:
                    position_size = float(size_value) if size_value is not None else 0.0
                except (ValueError, TypeError):
                    self.logger.warning(f"Invalid position size in close_all_positions: {size_value}, skipping")
                    continue
                
                if position_size != 0:
                    task = self.close_position(symbol, position, orderbook)
                    close_tasks.append(task)
            
            if close_tasks:
                results = await asyncio.gather(*close_tasks, return_exceptions=True)
                
                # 过滤异常结果
                valid_results = []
                for result in results:
                    if isinstance(result, FuturesOrderResult):
                        valid_results.append(result)
                    else:
                        self.logger.error(f"Force close exception: {result}")
                
                self.logger.info(f"Force closed {len(valid_results)} positions")
                return valid_results
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"Error in force close all positions: {e}")
            return []
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total_orders = self.success_count + self.failure_count
        success_rate = (self.success_count / total_orders * 100) if total_orders > 0 else 0
        avg_execution_time = sum(self.execution_times) / len(self.execution_times) if self.execution_times else 0
        avg_hedge_delay = sum(self.hedge_delays) / len(self.hedge_delays) if self.hedge_delays else 0
        perfect_hedge_rate = (self.perfect_hedge_count / self.success_count * 100) if self.success_count > 0 else 0
        
        return {
            "exchange": self.exchange_name,
            "total_orders": total_orders,
            "successful_orders": self.success_count,
            "failed_orders": self.failure_count,
            "success_rate": round(success_rate, 2),
            "perfect_hedge_count": self.perfect_hedge_count,
            "perfect_hedge_rate": round(perfect_hedge_rate, 2),
            "average_execution_time_ms": round(avg_execution_time, 2),
            "average_hedge_delay_ms": round(avg_hedge_delay, 2),
            "max_hedge_delay_ms": max(self.hedge_delays) if self.hedge_delays else 0,
            "hedge_delay_violations": len([d for d in self.hedge_delays if d > self.execution_timeout])
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.execution_times.clear()
        self.hedge_delays.clear()
        self.success_count = 0
        self.failure_count = 0
        self.perfect_hedge_count = 0
    

    
    async def market_sell(self, symbol: str, amount: float, slippage: float = None, orderbook: Dict = None, disable_split: bool = False, params: Dict = None) -> Dict:
        """
        执行市价卖单 - ExecutionEngine期望的接口
        
        Args:
            symbol: 交易对
            amount: 交易数量
            slippage: 保留参数(兼容性，已不使用)
            orderbook: 订单簿数据(可选，如果不传入则重新获取)
            disable_split: 是否禁用拆单(ExecutionEngine调用时设为True)
            params: 额外参数(可选)，包括reduce_only等参数
        
        Returns:
            Dict: 订单结果
        """
        try:
            self.logger.info(f"🔍 [DEBUG] 期货market_sell调用: symbol={symbol}, amount={amount:.6f}, slippage={slippage}, disable_split={disable_split}")
            
            # 🔥 关键修复：记录params参数用于后续传递
            if params:
                self.logger.info(f"🎯 期货市价卖单接收到参数: {params}")
            
            # 🔥 关键修复：优先使用传入的orderbook数据，避免重复获取
            if orderbook is not None:
                # 使用传入的orderbook数据
                self.logger.info(f"期货市价卖单使用传入的订单簿数据: {symbol}, asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                
                # 验证传入的orderbook数据有效性
                if not orderbook.get('asks') or not orderbook.get('bids'):
                    self.logger.error(f"传入的期货订单簿数据无效: {orderbook}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'Invalid futures orderbook data provided'
                    }
                
                best_bid = float(orderbook.get('bids', [[0, 0]])[0][0]) if orderbook.get('bids') else 0
                
            else:
                # 🔥 修复：使用传入的WebSocket订单簿数据，禁止REST API调用
                if orderbook and orderbook.get('bids'):
                    best_bid = float(orderbook.get('bids', [[0, 0]])[0][0]) if orderbook.get('bids') else 0
                else:
                    self.logger.error(f"❌ 缺少WebSocket订单簿数据，无法获取最优买价")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': 'Exchange does not support get_orderbook'
                    }
            
            # 🔥 精度处理已由trading_rules_preloader统一处理，此处不再重复
            # 🔥 期货交易器直接使用已格式化的数量
            self.logger.info(f"🎯 期货卖出: {symbol} {amount} (精度已由统一系统处理)")

            # 🔥 重构修复：使用30档算法的纯净执行价格
            market_price = self._get_pure_execution_price(FuturesOrderSide.SELL, orderbook, 100.0)
            self.logger.info(f"✅ 期货卖出使用30档纯净执行价格: {market_price:.8f}")

            result = await self._execute_single_futures_order(
                symbol=symbol,
                side=FuturesOrderSide.SELL,
                position_side=PositionSide.SHORT,
                quantity=amount,  # 🔥 直接使用已格式化的数量
                order_type=FuturesOrderType.MARKET,
                price=market_price,
                params=params
            )
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'side': result.side,
                    'amount': result.executed_quantity,
                    'price': result.executed_price,
                    'status': 'filled' if result.success else 'failed',
                    'filled': result.executed_quantity,
                    'average': result.executed_price,
                    'timestamp': result.timestamp
                }
            else:
                return {
                    'id': '',
                    'status': 'failed',
                    'error': result.error_message
                }
                
        except Exception as e:
            self.logger.error(f"futures market_sell error: {e}")
            return {
                'id': '',
                'status': 'failed',
                'error': str(e)
            }
    
    async def market_buy(self, symbol: str, amount: float, slippage: float = None, orderbook: Dict = None, disable_split: bool = False, params: Dict = None) -> Dict:
        """
        执行市价买单 - ExecutionEngine期望的接口
        
        Args:
            symbol: 交易对
            amount: 交易数量
            slippage: 保留参数(兼容性，已不使用)
            orderbook: 订单簿数据(可选，如果不传入则重新获取)
            disable_split: 是否禁用拆单(ExecutionEngine调用时设为True)
            params: 额外参数(可选)，包括reduce_only等参数
        
        Returns:
            Dict: 订单结果
        """
        try:
            self.logger.info(f"🔍 [DEBUG] 期货market_buy调用: symbol={symbol}, amount={amount:.6f}, slippage={slippage}, disable_split={disable_split}")
            
            # 🔥 关键修复：记录params参数用于后续传递
            if params:
                self.logger.info(f"🎯 期货市价买单接收到参数: {params}")
            
            # 🔥 关键修复：优先使用传入的orderbook数据，避免重复获取
            if orderbook is not None:
                # 使用传入的orderbook数据
                self.logger.info(f"期货市价买单使用传入的订单簿数据: {symbol}, asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                
                # 验证传入的orderbook数据有效性
                if not orderbook.get('asks') or not orderbook.get('bids'):
                    self.logger.error(f"传入的期货订单簿数据无效: {orderbook}")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': f'Invalid futures orderbook data provided'
                    }
                
                best_ask = float(orderbook.get('asks', [[0, 0]])[0][0]) if orderbook.get('asks') else 0
                
            else:
                # 🔥 修复：使用传入的WebSocket订单簿数据，禁止REST API调用
                if orderbook and orderbook.get('asks'):
                    best_ask = float(orderbook.get('asks', [[0, 0]])[0][0]) if orderbook.get('asks') else 0
                else:
                    self.logger.error(f"❌ 缺少WebSocket订单簿数据，无法获取最优卖价")
                    return {
                        'id': '',
                        'status': 'failed',
                        'error': 'Exchange does not support get_orderbook'
                    }
            
            # 🔥 精度处理已由trading_rules_preloader统一处理，此处不再重复
            self.logger.info(f"🎯 期货买入: {symbol} {amount} (精度已由统一系统处理)")

            # 🔥 重构修复：使用30档算法的纯净执行价格
            market_price = self._get_pure_execution_price(FuturesOrderSide.BUY, orderbook, 100.0)
            self.logger.info(f"✅ 期货买入使用30档纯净执行价格: {market_price:.8f}")

            result = await self._execute_single_futures_order(
                symbol=symbol,
                side=FuturesOrderSide.BUY,
                position_side=PositionSide.LONG,
                quantity=amount,  # 🔥 直接使用已格式化的数量
                order_type=FuturesOrderType.MARKET,
                price=market_price,
                params=params
            )
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'side': result.side,
                    'amount': result.executed_quantity,
                    'price': result.executed_price,
                    'status': 'filled' if result.success else 'failed',
                    'filled': result.executed_quantity,
                    'average': result.executed_price,
                    'timestamp': result.timestamp
                }
            else:
                return {
                    'id': '',
                    'status': 'failed',
                    'error': result.error_message
                }
                
        except Exception as e:
            self.logger.error(f"futures market_buy error: {e}")
            return {
                'id': '',
                'status': 'failed',
                'error': str(e)
            }
    
    async def get_order(self, order_id: str, symbol: str) -> Dict:
        """
        查询订单状态 - ExecutionEngine期望的接口
        
        Args:
            order_id: 订单ID
            symbol: 交易对
        
        Returns:
            Dict: 订单信息
        """
        try:
            if hasattr(self.exchange, 'get_order'):
                order = await self.exchange.get_order(order_id, symbol, market_type="futures")
                return order
            else:
                self.logger.error("Exchange doesn't support get_order")
                return {}
                
        except Exception as e:
            self.logger.error(f"get_order error: {e}")
            return {}
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """
        取消订单 - ExecutionEngine期望的接口
        
        Args:
            order_id: 订单ID
            symbol: 交易对
        
        Returns:
            bool: 取消成功
        """
        try:
            if hasattr(self.exchange, 'cancel_order'):
                result = await self.exchange.cancel_order(order_id, symbol, market_type="futures")
                return result
            else:
                self.logger.error("Exchange doesn't support cancel_order")
                return False
                
        except Exception as e:
            self.logger.error(f"cancel_order error: {e}")
            return False

    async def get_positions(self, symbol: str) -> List[Dict]:
        """
        获取期货持仓 - ExecutionEngine期望的接口
        
        Args:
            symbol: 交易对
        
        Returns:
            List[Dict]: 持仓信息列表
        """
        try:
            if hasattr(self.exchange, 'get_position'):
                positions = await self.exchange.get_position(symbol)
                return positions if positions else []
            else:
                self.logger.error("Exchange doesn't support get_position")
                return []
                
        except Exception as e:
            self.logger.error(f"get_positions error: {e}")
            return []

# 测试和调试功能
async def test_futures_trader():
    """测试期货交易器"""
    logger.info("Testing FuturesTrader...")
    
    # 创建模拟交易所
    class MockExchange:
        async def create_futures_order(self, **kwargs):
            return {
                'success': True,
                'order_id': 'futures_test_123',
                'executed_quantity': kwargs['quantity'],
                'executed_price': 50000.0,
                'executed_amount': kwargs['quantity'] * 50000.0,
                'fee': kwargs['quantity'] * 50000.0 * 0.0004
            }
        
        async def get_positions(self, symbol):
            return [
                {
                    'symbol': symbol,
                    'position_side': 'long',
                    'size': '0.1',
                    'unrealized_pnl': '10.5'
                }
            ]
        
        async def get_orderbook(self, symbol):
            return {
                "asks": [["50000.0", "1.0"]],
                "bids": [["49999.0", "1.1"]]
            }
    
    mock_exchange = MockExchange()
    trader = FuturesTrader(mock_exchange)
    
    # 测试对冲信息计算
    mock_spot_result = {
        'side': 'buy',
        'executed_quantity': 0.1,
        'executed_price': 49995.0,
        'executed_amount': 4999.5
    }
    
    hedge_info = trader._calculate_hedge_info(mock_spot_result)
    
    assert hedge_info.futures_side == FuturesOrderSide.SELL, "Wrong futures side for buy hedge"
    assert hedge_info.position_side == PositionSide.SHORT, "Wrong position side for buy hedge"
    assert hedge_info.target_futures_quantity == 0.1, "Wrong target quantity"
    
    # 测试对冲执行
    mock_orderbook = {
        "asks": [["50000.0", "1.0"]],
        "bids": [["49999.0", "1.1"]]
    }
    
    hedge_start_time = time.time()
    result = await trader.execute_hedge_order(
        mock_spot_result, mock_orderbook, "BTCUSDT", hedge_start_time
    )
    
    assert result.success, "Hedge order should succeed"
    assert result.match_spot_quantity == 0.1, "Should match spot quantity"
    
    logger.info("✅ FuturesTrader test completed successfully")

if __name__ == '__main__':
    asyncio.run(test_futures_trader())