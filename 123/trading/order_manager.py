"""
订单管理模块
统一管理现货和期货订单的生命周期，实现完美对冲逻辑
"""

import asyncio
import time
import logging
import os
import uuid
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
from decimal import Decimal

from config.debug_config import DEBUG
from utils.logger import get_logger
from trading.spot_trader import SpotTrader, SpotOrderResult, OrderSide as SpotOrderSide
from trading.futures_trader import FuturesTrader, FuturesOrderResult, FuturesOrderSide
from exchanges.exchanges_base import OrderStatus  # 🔥 使用统一的OrderStatus定义

logger = get_logger(__name__)

class ArbitrageType(Enum):
    SPOT_FUTURES = "spot_futures"  # 现货-期货套利

# 🔥 删除重复的OrderStatus类定义，使用exchanges_base.py中的统一定义
# 为了兼容性，添加额外的状态映射
class ArbitrageOrderStatus(Enum):
    """套利订单特有状态（扩展基础OrderStatus）"""
    SPOT_EXECUTING = "spot_executing"    # 现货执行中
    FUTURES_EXECUTING = "futures_executing"  # 期货执行中
    HEDGED = "hedged"           # 已对冲
    WAITING_CONVERGENCE = "waiting_convergence"  # 等待价差收敛
    FORCE_CLOSING = "force_closing"  # 强制平仓中

@dataclass
class ArbitrageOrder:
    """套利订单信息"""
    id: str
    arbitrage_type: ArbitrageType
    status: OrderStatus  # 使用基础OrderStatus，扩展状态通过字符串值处理
    
    # 交易对信息
    spot_symbol: str
    futures_symbol: str
    spot_exchange: str
    futures_exchange: str
    
    # 订单参数
    target_quantity: float
    target_spread: float
    
    # 执行结果
    spot_result: Optional[SpotOrderResult] = None
    futures_result: Optional[FuturesOrderResult] = None
    
    # 时间记录
    created_time: float = field(default_factory=time.time)
    spot_start_time: Optional[float] = None
    spot_end_time: Optional[float] = None
    futures_start_time: Optional[float] = None
    futures_end_time: Optional[float] = None
    hedge_delay_ms: Optional[float] = None
    
    # 完美对冲验证
    is_perfect_hedge: bool = False
    quantity_diff: float = 0.0
    amount_diff: float = 0.0
    
    # 错误信息
    error_messages: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": self.id,
            "arbitrage_type": self.arbitrage_type.value,
            "status": self.status.value,
            "spot_symbol": self.spot_symbol,
            "futures_symbol": self.futures_symbol,
            "spot_exchange": self.spot_exchange,
            "futures_exchange": self.futures_exchange,
            "target_quantity": self.target_quantity,
            "target_spread": self.target_spread,
            "spot_result": self.spot_result.__dict__ if self.spot_result else None,
            "futures_result": self.futures_result.__dict__ if self.futures_result else None,
            "created_time": self.created_time,
            "spot_start_time": self.spot_start_time,
            "spot_end_time": self.spot_end_time,
            "futures_start_time": self.futures_start_time,
            "futures_end_time": self.futures_end_time,
            "hedge_delay_ms": self.hedge_delay_ms,
            "is_perfect_hedge": self.is_perfect_hedge,
            "quantity_diff": self.quantity_diff,
            "amount_diff": self.amount_diff,
            "error_messages": self.error_messages
        }

# 🔥 状态转换辅助函数
def get_arbitrage_status(status_name: str) -> OrderStatus:
    """将套利特有状态转换为基础OrderStatus"""
    status_mapping = {
        "spot_executing": OrderStatus.PENDING,
        "futures_executing": OrderStatus.PENDING,
        "hedged": OrderStatus.FILLED,
        "waiting_convergence": OrderStatus.FILLED,
        "closing": OrderStatus.PENDING,
        "force_closing": OrderStatus.PENDING
    }
    return status_mapping.get(status_name, OrderStatus.PENDING)

class OrderManager:
    """订单管理器"""

    def __init__(self, spot_traders: Dict[str, SpotTrader],
                 futures_traders: Dict[str, FuturesTrader]):
        self.spot_traders = spot_traders  # {"gate": SpotTrader, "bybit": SpotTrader}
        self.futures_traders = futures_traders  # {"gate": FuturesTrader, "bybit": FuturesTrader}
        self.logger = get_logger("OrderManager")
        
        # 订单存储
        self.active_orders: Dict[str, ArbitrageOrder] = {}
        self.completed_orders: Dict[str, ArbitrageOrder] = {}
        
        # 执行参数
        self.hedge_timeout_ms = int(os.getenv('HEDGE_TIMEOUT_MS', '50'))  # 🔥 高性能修复：对冲超时50ms
        self.convergence_timeout_minutes = int(os.getenv('CONVERGENCE_TIMEOUT_MINUTES', '30'))  # 价差收敛超时时间
        self.max_retry_attempts = 3
        
        # 完美对冲参数
        self.quantity_tolerance = float(os.getenv('QUANTITY_TOLERANCE', '0.00001'))  # 数量容忍度
        self.amount_tolerance = float(os.getenv('AMOUNT_TOLERANCE', '0.01'))  # 金额容忍度
        self.convergence_check_interval = float(os.getenv('CONVERGENCE_CHECK_INTERVAL', '0.5'))  # 收敛检查间隔
        
        # 统计信息
        self.total_orders = 0
        self.successful_orders = 0
        self.perfect_hedge_orders = 0
        self.forced_close_orders = 0
        
        if DEBUG:
            self.logger.info("OrderManager initialized")
    
    async def initialize(self):
        """初始化OrderManager"""
        self.logger.info("OrderManager initialization completed")
    
    def generate_order_id(self) -> str:
        """生成唯一订单ID"""
        timestamp = int(time.time() * 1000)
        unique_id = str(uuid.uuid4())[:8]
        return f"ARB_{timestamp}_{unique_id}"
    
    async def create_arbitrage_order(self, spot_symbol: str, futures_symbol: str,
                                   spot_exchange: str, futures_exchange: str,
                                   quantity: float, spread: float) -> ArbitrageOrder:
        """
        创建套利订单
        
        Args:
            spot_symbol: 现货交易对
            futures_symbol: 期货交易对
            spot_exchange: 现货交易所
            futures_exchange: 期货交易所
            quantity: 交易数量
            spread: 目标价差
            
        Returns:
            ArbitrageOrder: 套利订单
        """
        order_id = self.generate_order_id()
        
        order = ArbitrageOrder(
            id=order_id,
            arbitrage_type=ArbitrageType.SPOT_FUTURES,
            status=OrderStatus.PENDING,
            spot_symbol=spot_symbol,
            futures_symbol=futures_symbol,
            spot_exchange=spot_exchange,
            futures_exchange=futures_exchange,
            target_quantity=quantity,
            target_spread=spread
        )
        
        self.active_orders[order_id] = order
        self.total_orders += 1
        
        if DEBUG:
            self.logger.info(f"Created arbitrage order {order_id}: "
                           f"{spot_exchange}:{spot_symbol} -> {futures_exchange}:{futures_symbol}")
        
        return order
    
    async def execute_spot_first_arbitrage(self, order: ArbitrageOrder, 
                                         spot_orderbook: Dict,
                                         futures_orderbook: Dict) -> bool:
        """
        执行现货优先套利（先现货后期货对冲）
        
        Args:
            order: 套利订单
            spot_orderbook: 现货订书
            futures_orderbook: 期货订单簿
            
        Returns:
            bool: 是否成功执行
        """
        try:
            # 🔥 使用字符串状态值，避免枚举冲突
            order.status = get_arbitrage_status("spot_executing")

            # 1. 执行现货订单
            spot_trader = self.spot_traders.get(order.spot_exchange)
            if not spot_trader:
                order.error_messages.append(f"Spot trader not found for {order.spot_exchange}")
                order.status = OrderStatus.FAILED
                return False
            
            # 确定现货订单方向（根据价差方向）
            spot_side = SpotOrderSide.BUY if order.target_spread > 0 else SpotOrderSide.SELL
            
            order.spot_start_time = time.time()
            spot_result = await spot_trader.execute_with_retry(
                order.spot_symbol, spot_side, order.target_quantity, spot_orderbook
            )
            order.spot_end_time = time.time()
            order.spot_result = spot_result
            
            if not spot_result.success:
                order.error_messages.append(f"Spot order failed: {spot_result.error_message}")
                order.status = OrderStatus.FAILED
                return False
            
            if DEBUG:
                self.logger.info(f"Spot order executed: {order.spot_symbol} {spot_side.value} "
                               f"{spot_result.executed_quantity} @ {spot_result.executed_price}")
            
            # 2. 立即执行期货对冲
            order.status = get_arbitrage_status("futures_executing")
            order.futures_start_time = time.time()

            futures_trader = self.futures_traders.get(order.futures_exchange)
            if not futures_trader:
                order.error_messages.append(f"Futures trader not found for {order.futures_exchange}")
                order.status = OrderStatus.FAILED
                # 需要平掉现货仓位
                await self._emergency_close_spot_position(order)
                return False
            
            # 执行期货对冲
            futures_result = await futures_trader.execute_with_retry(
                spot_result.__dict__, futures_orderbook, order.futures_symbol, order.futures_start_time
            )
            order.futures_end_time = time.time()
            order.futures_result = futures_result
            
            if not futures_result.success:
                order.error_messages.append(f"Futures order failed: {futures_result.error_message}")
                order.status = OrderStatus.FAILED
                return False
            
            # 3. 验证完美对冲
            hedge_delay = (order.futures_start_time - order.spot_end_time) * 1000
            order.hedge_delay_ms = hedge_delay
            
            is_perfect = self._verify_perfect_hedge(order)
            if is_perfect:
                self.perfect_hedge_orders += 1
                order.status = get_arbitrage_status("hedged")
                if DEBUG:
                    self.logger.info(f"Perfect hedge achieved for order {order.id}")
            else:
                order.status = get_arbitrage_status("hedged")  # 即使不完美也标记为已对冲
                if DEBUG:
                    self.logger.warning(f"Imperfect hedge for order {order.id}: "
                                      f"quantity_diff={order.quantity_diff}, amount_diff={order.amount_diff}")
            
            self.successful_orders += 1
            return True
            
        except Exception as e:
            order.error_messages.append(f"Execution error: {str(e)}")
            order.status = OrderStatus.FAILED
            self.logger.error(f"Error executing arbitrage order {order.id}: {e}")
            return False
    
    def _verify_perfect_hedge(self, order: ArbitrageOrder) -> bool:
        """
        🔥 简化对冲验证 - 重复验证已删除

        Args:
            order: 套利订单

        Returns:
            bool: 是否执行成功（对冲质量已由hedge_calculator统一验证）
        """
        if not order.spot_result or not order.futures_result:
            return False

        try:
            # 🔥 删除重复验证：对冲质量检查已在ExecutionEngine._pre_check_hedge_quality()中统一完成
            # 此处只检查基本的执行成功状态
            self.logger.info(f"✅ 对冲质量检查已由统一系统完成，此处仅验证执行状态")

            # 记录基本信息用于统计
            spot_quantity = order.spot_result.executed_quantity
            futures_quantity = order.futures_result.executed_quantity
            order.quantity_diff = abs(spot_quantity - futures_quantity)
            order.amount_diff = abs(spot_quantity * order.spot_result.executed_price -
                                  futures_quantity * order.futures_result.executed_price)

            return (order.spot_result.success and order.futures_result.success)

        except Exception as e:
            self.logger.error(f"Error verifying hedge for order {order.id}: {e}")
            return False
    
    async def wait_for_convergence(self, order: ArbitrageOrder, 
                                  min_spread: float, max_spread: float,
                                  price_monitor_callback) -> bool:
        """
        等待价差收敛到平仓区间 - 🔥 使用统一趋同监控器
        
        Args:
            order: 套利订单
            min_spread: 最小平仓价差
            max_spread: 最大平仓价差
            price_monitor_callback: 价格监控回调函数
            
        Returns:
            bool: 是否成功收敛
        """
        if order.status != get_arbitrage_status("hedged"):
            return False

        order.status = get_arbitrage_status("waiting_convergence")
        
        if DEBUG:
            self.logger.info(f"Waiting for convergence: order {order.id}, "
                           f"target spread: {min_spread:.4f} - {max_spread:.4f}")
        
        try:
            # 🔥 修复：使用统一的ConvergenceMonitor，避免重复逻辑
            from core.convergence_monitor import get_convergence_monitor
            convergence_monitor = get_convergence_monitor()
            
            if convergence_monitor:
                # 使用统一的趋同监控
                result = await convergence_monitor.monitor_price_convergence(
                    order.spot_symbol,
                    order.spot_exchange, 
                    order.futures_exchange,
                    max_spread,  # 使用max_spread作为目标价差
                    self.convergence_timeout_minutes * 60  # 转换为秒
                )
                
                if result.get('converged', False):
                    if DEBUG:
                        self.logger.info(f"Convergence achieved for order {order.id}: "
                                       f"spread={result.get('final_spread', 0):.4f}")
                    return True
                else:
                    self.logger.warning(f"Convergence timeout for order {order.id}")
                    return False
            else:
                # 🔥 备用方案：如果ConvergenceMonitor不可用，返回失败
                self.logger.error("ConvergenceMonitor not available, cannot wait for convergence")
                return False
            
        except Exception as e:
            self.logger.error(f"Error waiting for convergence: {e}")
            return False
    
    async def close_arbitrage_position(self, order: ArbitrageOrder,
                                     spot_orderbook: Dict, 
                                     futures_orderbook: Dict) -> bool:
        """
        平仓套利仓位（先期货后现货）
        
        Args:
            order: 套利订单
            spot_orderbook: 现货订单簿
            futures_orderbook: 期货订单簿
            
        Returns:
            bool: 是否成功平仓
        """
        try:
            order.status = get_arbitrage_status("closing")
            
            # 1. 先平期货仓位
            futures_trader = self.futures_traders.get(order.futures_exchange)
            if not futures_trader:
                order.error_messages.append(f"Futures trader not found for closing")
                return False
            
            # 获取期货仓位信息
            if hasattr(futures_trader.exchange, 'get_positions'):
                positions = await futures_trader.exchange.get_positions(order.futures_symbol)
                
                close_results = []
                for position in positions:
                    if float(position.get('size', 0)) != 0:
                        close_result = await futures_trader.close_position(
                            order.futures_symbol, position, futures_orderbook
                        )
                        close_results.append(close_result)
                
                # 检查期货平仓结果
                futures_closed = all(result.success for result in close_results)
                if not futures_closed:
                    order.error_messages.append("Futures closing failed")
                    return False
            
            # 2. 平现货仓位
            spot_trader = self.spot_traders.get(order.spot_exchange)
            if not spot_trader:
                order.error_messages.append(f"Spot trader not found for closing")
                return False
            
            # 确定现货平仓方向（与开仓相反）
            if order.spot_result:
                original_side = order.spot_result.side
                close_side = SpotOrderSide.SELL if original_side == 'buy' else SpotOrderSide.BUY
                close_quantity = order.spot_result.executed_quantity
                
                spot_close_result = await spot_trader.execute_with_retry(
                    order.spot_symbol, close_side, close_quantity, spot_orderbook
                )
                
                if not spot_close_result.success:
                    order.error_messages.append(f"Spot closing failed: {spot_close_result.error_message}")
                    return False
            
            # 3. 验证仓位清零
            is_clean = await self._verify_position_clean(order)
            
            if is_clean:
                order.status = OrderStatus.FILLED  # 使用基础状态FILLED表示完成
                self.successful_orders += 1
                
                # 移动到已完成订单
                self.completed_orders[order.id] = order
                del self.active_orders[order.id]
                
                if DEBUG:
                    self.logger.info(f"Arbitrage order {order.id} completed successfully")
                
                return True
            else:
                order.error_messages.append("Position not completely closed")
                return False
            
        except Exception as e:
            order.error_messages.append(f"Closing error: {str(e)}")
            self.logger.error(f"Error closing arbitrage order {order.id}: {e}")
            return False
    
    async def _verify_position_clean(self, order: ArbitrageOrder) -> bool:
        """
        验证仓位是否完全清零
        
        Args:
            order: 套利订单
            
        Returns:
            bool: 仓位是否清零
        """
        try:
            # 检查期货仓位
            futures_trader = self.futures_traders.get(order.futures_exchange)
            if futures_trader and hasattr(futures_trader.exchange, 'get_positions'):
                positions = await futures_trader.exchange.get_positions(order.futures_symbol)
                
                # 过滤仅有效仓位
                valid_positions = []
                for position in positions:
                    # 🔥 修复：安全处理position size，防止None值
                    size_value = position.get('size', 0)
                    try:
                        position_size = float(size_value) if size_value is not None else 0.0
                    except (ValueError, TypeError):
                        self.logger.warning(f"Invalid position size in cleanup: {size_value}, skipping")
                        continue
                    
                    if position_size != 0:
                        valid_positions.append(position)
                
                # 检查现货余额变化（这里需要根据实际需求实现）
                # 可以记录开仓前后的余额变化来验证
                
                return True
            
        except Exception as e:
            self.logger.error(f"Error verifying position clean: {e}")
            return False
    
    async def _emergency_close_spot_position(self, order: ArbitrageOrder):
        """
        紧急平掉现货仓位（当期货对冲失败时）
        
        Args:
            order: 套利订单
        """
        try:
            if not order.spot_result or not order.spot_result.success:
                return
            
            spot_trader = self.spot_traders.get(order.spot_exchange)
            if not spot_trader:
                return
            
            # 反向平仓
            original_side = order.spot_result.side
            close_side = SpotOrderSide.SELL if original_side == 'buy' else SpotOrderSide.BUY
            close_quantity = order.spot_result.executed_quantity
            
            # 获取最新订单簿（这里需要实际的订单簿数据）
            # 简化处理，实际应该获取最新市场数据
            mock_orderbook = {
                "asks": [["50000", "1.0"]],
                "bids": [["49999", "1.0"]]
            }
            
            close_result = await spot_trader.execute_market_order(
                order.spot_symbol, close_side, close_quantity, mock_orderbook
            )
            
            if close_result.success:
                self.logger.info(f"Emergency spot position closed for order {order.id}")
            else:
                self.logger.error(f"Failed to emergency close spot position for order {order.id}")
                
        except Exception as e:
            self.logger.error(f"Error in emergency close spot position: {e}")
    
    async def force_close_all_positions(self, order: ArbitrageOrder) -> bool:
        """
        强制平仓所有仓位
        
        Args:
            order: 套利订单
            
        Returns:
            bool: 是否成功
        """
        try:
            order.status = OrderStatus.FORCE_CLOSING
            self.forced_close_orders += 1
            
            # 强制平仓期货
            futures_trader = self.futures_traders.get(order.futures_exchange)
            if futures_trader:
                await futures_trader.force_close_all_positions(order.futures_symbol)
            
            # 强制平仓现货（需要根据实际情况实现）
            # 这里简化处理
            
            order.status = OrderStatus.COMPLETED
            self.completed_orders[order.id] = order
            if order.id in self.active_orders:
                del self.active_orders[order.id]
            
            self.logger.warning(f"Force closed all positions for order {order.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error force closing positions: {e}")
            return False
    
    def get_active_orders(self) -> List[ArbitrageOrder]:
        """获取活跃订单列表"""
        return list(self.active_orders.values())
    
    def get_completed_orders(self, limit: int = 100) -> List[ArbitrageOrder]:
        """获取已完成订单列表"""
        orders = list(self.completed_orders.values())
        orders.sort(key=lambda x: x.created_time, reverse=True)
        return orders[:limit]
    
    def get_order_by_id(self, order_id: str) -> Optional[ArbitrageOrder]:
        """根据ID获取订单"""
        return self.active_orders.get(order_id) or self.completed_orders.get(order_id)
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total_execution_time = 0
        completed_orders_count = len(self.completed_orders)
        
        for order in self.completed_orders.values():
            if order.spot_end_time and order.spot_start_time:
                total_execution_time += (order.spot_end_time - order.spot_start_time)
            if order.futures_end_time and order.futures_start_time:
                total_execution_time += (order.futures_end_time - order.futures_start_time)
        
        return {
            "total_orders": self.total_orders,
            "successful_orders": self.successful_orders,
            "failed_orders": self.total_orders - self.successful_orders,
            "success_rate": (self.successful_orders / max(1, self.total_orders)) * 100,
            "perfect_hedge_orders": self.perfect_hedge_orders,
            "perfect_hedge_rate": (self.perfect_hedge_orders / max(1, self.successful_orders)) * 100,
            "forced_close_orders": self.forced_close_orders,
            "active_orders_count": len(self.active_orders),
            "completed_orders_count": completed_orders_count,
            "average_execution_time_ms": (total_execution_time / max(1, completed_orders_count)) * 1000
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.total_orders = 0
        self.successful_orders = 0
        self.perfect_hedge_orders = 0
        self.forced_close_orders = 0
        
        if DEBUG:
            self.logger.info("OrderManager统计数据已重置")
    
    def cleanup_old_orders(self, hours: int = 24):
        """清理旧订单"""
        cutoff_time = time.time() - (hours * 3600)
        
        # 清理已完成的旧订单
        old_order_ids = [
            order_id for order_id, order in self.completed_orders.items()
            if order.created_time < cutoff_time
        ]
        
        for order_id in old_order_ids:
            del self.completed_orders[order_id]
        
        if old_order_ids:
            self.logger.info(f"Cleaned up {len(old_order_ids)} old completed orders")

# 测试和调试功能
async def test_order_manager():
    """测试订单管理器"""
    logger.info("Testing OrderManager...")
    
    # 创建模拟交易器
    from trading.spot_trader import SpotTrader
    from trading.futures_trader import FuturesTrader
    
    class MockExchange:
        def __init__(self, name):
            self.name = name
        
        async def create_spot_order(self, **kwargs):
            return {
                'success': True,
                'order_id': f'{self.name}_spot_123',
                'executed_quantity': kwargs['quantity'],
                'executed_price': 50000.0,
                'executed_amount': kwargs['quantity'] * 50000.0,
                'fee': kwargs['quantity'] * 50000.0 * 0.001
            }
        
        async def create_futures_order(self, **kwargs):
            return {
                'success': True,
                'order_id': f'{self.name}_futures_123',
                'executed_quantity': kwargs['quantity'],
                'executed_price': 50000.0,
                'executed_amount': kwargs['quantity'] * 50000.0,
                'fee': kwargs['quantity'] * 50000.0 * 0.0004
            }
        
        async def get_positions(self, symbol):
            return []
    
    # 创建模拟交易器
    gate_exchange = MockExchange("gate")
    bybit_exchange = MockExchange("bybit")
    
    spot_traders = {
        "gate": SpotTrader(gate_exchange),
        "bybit": SpotTrader(bybit_exchange)
    }
    
    futures_traders = {
        "gate": FuturesTrader(gate_exchange),
        "bybit": FuturesTrader(bybit_exchange)
    }
    
    # 创建订单管理器
    order_manager = OrderManager(spot_traders, futures_traders)
    
    # 测试创建订单
    order = await order_manager.create_arbitrage_order(
        "BTCUSDT", "BTCUSDT", "gate", "bybit", 0.1, 0.003
    )
    
    assert order.id.startswith("ARB_"), "Order ID should start with ARB_"
    assert order.status == OrderStatus.PENDING, "Initial status should be PENDING"
    
    # 测试订单执行
    mock_spot_orderbook = {
        "asks": [["50000.0", "1.0"]],
        "bids": [["49999.0", "1.1"]]
    }
    
    mock_futures_orderbook = {
        "asks": [["50010.0", "1.0"]],
        "bids": [["50009.0", "1.1"]]
    }
    
    success = await order_manager.execute_spot_first_arbitrage(
        order, mock_spot_orderbook, mock_futures_orderbook
    )
    
    assert success, "Arbitrage execution should succeed"
    assert order.status == OrderStatus.HEDGED, "Status should be HEDGED after execution"
    assert order.spot_result.success, "Spot order should succeed"
    assert order.futures_result.success, "Futures order should succeed"
    
    # 测试性能统计
    stats = order_manager.get_performance_stats()
    assert stats["total_orders"] == 1, "Should have 1 total order"
    
    logger.info("✅ OrderManager test completed successfully")

if __name__ == '__main__':
    asyncio.run(test_order_manager())