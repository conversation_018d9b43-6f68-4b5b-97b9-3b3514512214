2025-08-01 08:43:48.941 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:43:50.406 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:45:00.704 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 08:45:00.704 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 08:45:00.704 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 08:45:00.705 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 08:45:00.705 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 08:45:00.705 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 08:45:00.705 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 08:45:00.705 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 08:45:00.706 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 08:45:00.706 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 08:45:00.706 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 08:45:00.707 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 08:45:00.707 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 08:45:00.707 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 08:45:00.707 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 08:45:00.707 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 08:45:00.708 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 08:45:00.709 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 08:45:00.709 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 08:45:00.729 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 08:45:00.730 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 08:45:00.731 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 08:45:00.731 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 08:45:00.731 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 08:45:00.732 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 08:45:00.732 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 08:45:00.732 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 08:45:00.732 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 08:45:00.733 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 08:45:00.733 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 08:45:01.861 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 08:45:01.861 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 08:45:01.861 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 08:45:14.569 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:45:16.040 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:45:25.171 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:45:26.688 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 08:46:31.072 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:46:33.305 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:06.555 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:07.664 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:18.086 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:21.331 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:37.704 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:47:54.297 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 08:48:08.772 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
