#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 真实代码深度诊断 - 2025-07-30

不使用模拟数据，直接测试实际代码逻辑
按照修复提示词要求进行深度审查
"""

import sys
import os
import json
import time
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RealCodeDiagnosis:
    """真实代码诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "diagnosis_results": {},
            "root_causes": {},
            "fix_status": {}
        }
    
    def test_trading_rules_preloader_real_logic(self):
        """测试交易规则预加载器的真实逻辑"""
        print("🔍 测试问题1: 交易规则预加载器真实逻辑")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试真实的get_trading_rule调用
            print("📊 测试真实的get_trading_rule调用...")
            result = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            
            if result:
                print(f"✅ 交易规则获取成功: {result.symbol}")
                self.results["diagnosis_results"]["trading_rules"] = "SUCCESS"
            else:
                print(f"❌ 交易规则获取失败 - 返回None")
                self.results["diagnosis_results"]["trading_rules"] = "FAILED"
                
                # 深度检查失败原因
                print("🔍 深度检查失败原因...")
                
                # 检查临时实例创建
                temp_instance = preloader._create_temporary_exchange_instance_sync("gate")
                if temp_instance:
                    print("✅ 临时Gate实例创建成功")
                    
                    # 检查同步API调用方法是否存在
                    if hasattr(preloader, '_get_precision_from_exchange_api_sync'):
                        print("✅ 同步API调用方法存在")
                        
                        # 尝试调用同步API方法
                        try:
                            precision_info = preloader._get_precision_from_exchange_api_sync(
                                temp_instance, "SPK-USDT", "spot"
                            )
                            if precision_info:
                                print(f"✅ 同步API调用成功: {precision_info}")
                            else:
                                print(f"❌ 同步API调用返回None")
                                self.results["root_causes"]["trading_rules"] = "同步API调用返回None"
                        except Exception as api_error:
                            print(f"❌ 同步API调用异常: {api_error}")
                            self.results["root_causes"]["trading_rules"] = f"同步API调用异常: {api_error}"
                    else:
                        print("❌ 同步API调用方法不存在")
                        self.results["root_causes"]["trading_rules"] = "同步API调用方法不存在"
                else:
                    print("❌ 临时Gate实例创建失败")
                    self.results["root_causes"]["trading_rules"] = "临时实例创建失败"
                    
        except Exception as e:
            print(f"❌ 交易规则测试异常: {e}")
            self.results["diagnosis_results"]["trading_rules"] = f"EXCEPTION: {e}"
    
    def test_closing_logic_real_code(self):
        """测试平仓逻辑的真实代码"""
        print("\n🔍 测试问题2: 平仓逻辑真实代码")
        
        try:
            from core.unified_closing_manager import UnifiedClosingManager
            
            # 读取实际的判断逻辑代码
            import inspect
            
            # 获取UnifiedClosingManager的源代码
            source_lines = inspect.getsourcelines(UnifiedClosingManager)[0]
            
            # 查找关键的判断逻辑行
            order_id_check_lines = []
            for i, line in enumerate(source_lines):
                if 'result.get("order_id")' in line:
                    order_id_check_lines.append((i, line.strip()))
            
            print(f"🔍 发现{len(order_id_check_lines)}处order_id检查:")
            for line_num, line_content in order_id_check_lines:
                print(f"  第{line_num}行: {line_content}")
            
            # 检查是否有兼容id字段的逻辑
            id_compatible_lines = []
            for i, line in enumerate(source_lines):
                if 'result.get("id")' in line or 'result.get("order_id") or result.get("id")' in line:
                    id_compatible_lines.append((i, line.strip()))
            
            print(f"🔍 发现{len(id_compatible_lines)}处id字段兼容:")
            for line_num, line_content in id_compatible_lines:
                print(f"  第{line_num}行: {line_content}")
            
            # 判断修复状态
            if order_id_check_lines and not id_compatible_lines:
                print("❌ 发现问题：仍然只检查order_id字段，没有兼容id字段")
                self.results["root_causes"]["closing_logic"] = "代码仍然只检查order_id字段，未兼容id字段"
                self.results["fix_status"]["closing_logic"] = "NOT_FIXED"
            elif id_compatible_lines:
                print("✅ 发现修复：已兼容id字段")
                self.results["fix_status"]["closing_logic"] = "FIXED"
            else:
                print("⚠️ 未发现相关检查逻辑")
                self.results["fix_status"]["closing_logic"] = "UNKNOWN"
                
        except Exception as e:
            print(f"❌ 平仓逻辑测试异常: {e}")
            self.results["diagnosis_results"]["closing_logic"] = f"EXCEPTION: {e}"
    
    def generate_precise_fix_plan(self):
        """生成精确修复方案"""
        print("\n🔧 生成精确修复方案")
        
        fix_plan = {}
        
        # 交易规则问题修复方案
        if "trading_rules" in self.results["root_causes"]:
            fix_plan["trading_rules"] = {
                "问题状态": "未完全修复",
                "根本原因": self.results["root_causes"]["trading_rules"],
                "修复方案": "完善同步API调用逻辑，确保临时实例能正确获取交易规则",
                "修复文件": "123/core/trading_rules_preloader.py",
                "修复方法": "检查并修复_get_precision_from_exchange_api_sync方法"
            }
        
        # 平仓逻辑问题修复方案
        if self.results["fix_status"].get("closing_logic") == "NOT_FIXED":
            fix_plan["closing_logic"] = {
                "问题状态": "未修复",
                "根本原因": self.results["root_causes"]["closing_logic"],
                "修复方案": "修改order_id检查逻辑，兼容id字段",
                "修复文件": "123/core/unified_closing_manager.py",
                "修复方法": "将result.get('order_id')改为result.get('order_id') or result.get('id')"
            }
        
        self.results["fix_plan"] = fix_plan
        
        for problem, plan in fix_plan.items():
            print(f"📋 {problem}修复方案:")
            for key, value in plan.items():
                print(f"  {key}: {value}")
    
    def save_results(self):
        """保存诊断结果"""
        os.makedirs("123/diagnostic_results", exist_ok=True)
        
        with open("123/diagnostic_results/real_code_diagnosis_20250730.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 真实代码诊断结果已保存")

def main():
    """主函数"""
    print("🚀 开始真实代码深度诊断 - 2025-07-30")
    print("📋 不使用模拟数据，直接测试实际代码逻辑")
    
    diagnosis = RealCodeDiagnosis()
    
    # 执行真实代码测试
    diagnosis.test_trading_rules_preloader_real_logic()
    diagnosis.test_closing_logic_real_code()
    diagnosis.generate_precise_fix_plan()
    diagnosis.save_results()
    
    print("\n✅ 真实代码深度诊断完成")

if __name__ == "__main__":
    main()