#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT_gate_spot交易规则获取失败问题精确诊断脚本
2025-07-30 创建

根据用户报告的错误：
2025-07-30 09:16:00.381 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_spot

精确诊断问题根源：
1. 检查get_global_exchanges()返回值
2. 验证set_global_exchanges()调用情况
3. 分析交易规则预加载器的调用链路
4. 检查临时实例创建机制
5. 验证API密钥配置
"""

import os
import sys
import asyncio
import time
import json
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_result(test_name: str, result: bool, details: str = ""):
    """打印测试结果"""
    status = "✅ 通过" if result else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

class SPKUSDTTradingRulesDiagnosis:
    """SPK-USDT交易规则获取失败问题诊断器"""
    
    def __init__(self):
        self.diagnosis_results = {
            "global_exchanges_check": False,
            "set_global_exchanges_check": False,
            "trading_rules_preloader_check": False,
            "temporary_instance_check": False,
            "api_keys_check": False,
            "initialize_all_systems_check": False,
            "spk_usdt_rule_test": False
        }
        self.details = {}
    
    def check_global_exchanges_function(self) -> bool:
        """检查全局交易所实例管理函数"""
        print_section("检查全局交易所实例管理函数")
        
        try:
            # 检查get_global_exchanges函数
            from core.trading_system_initializer import get_global_exchanges, set_global_exchanges
            
            # 测试初始状态
            initial_state = get_global_exchanges()
            print_result("get_global_exchanges函数导入", True, f"初始状态: {initial_state}")
            
            # 测试set_global_exchanges函数
            test_exchanges = {'test': 'value'}
            set_global_exchanges(test_exchanges)
            after_set = get_global_exchanges()
            
            set_works = after_set == test_exchanges
            print_result("set_global_exchanges函数工作", set_works, f"设置后状态: {after_set}")
            
            # 恢复初始状态
            set_global_exchanges(initial_state)
            
            self.details["global_exchanges"] = {
                "initial_state": str(initial_state),
                "set_works": set_works,
                "functions_available": True
            }
            
            return True
            
        except Exception as e:
            print_result("全局交易所实例管理函数", False, f"错误: {e}")
            self.details["global_exchanges"] = {"error": str(e)}
            return False
    
    def check_initialize_all_systems_method(self) -> bool:
        """检查initialize_all_systems方法是否调用set_global_exchanges"""
        print_section("检查initialize_all_systems方法")
        
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            
            # 检查方法是否存在
            initializer = TradingSystemInitializer()
            has_method = hasattr(initializer, 'initialize_all_systems')
            print_result("initialize_all_systems方法存在", has_method)
            
            if has_method:
                # 检查方法源码是否包含set_global_exchanges调用
                import inspect
                source = inspect.getsource(initializer.initialize_all_systems)
                has_set_call = "set_global_exchanges" in source
                print_result("方法中包含set_global_exchanges调用", has_set_call)
                
                # 检查具体调用位置
                lines = source.split('\n')
                set_lines = [i for i, line in enumerate(lines) if "set_global_exchanges" in line]
                
                self.details["initialize_all_systems"] = {
                    "method_exists": has_method,
                    "has_set_call": has_set_call,
                    "set_call_lines": set_lines,
                    "total_lines": len(lines)
                }
                
                return has_set_call
            else:
                self.details["initialize_all_systems"] = {"method_exists": False}
                return False
                
        except Exception as e:
            print_result("initialize_all_systems方法检查", False, f"错误: {e}")
            self.details["initialize_all_systems"] = {"error": str(e)}
            return False
    
    def check_trading_rules_preloader(self) -> bool:
        """检查交易规则预加载器"""
        print_section("检查交易规则预加载器")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            # 获取预加载器实例
            preloader = get_trading_rules_preloader()
            print_result("交易规则预加载器获取", True, f"实例类型: {type(preloader)}")
            
            # 检查get_trading_rule方法
            has_get_method = hasattr(preloader, 'get_trading_rule')
            print_result("get_trading_rule方法存在", has_get_method)
            
            # 测试SPK-USDT_gate_spot规则获取（不使用全局实例）
            if has_get_method:
                try:
                    rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                    rule_exists = rule is not None
                    print_result("SPK-USDT_gate_spot规则获取", rule_exists, 
                               f"规则: {rule}" if rule else "规则为None")
                    
                    self.details["trading_rules_preloader"] = {
                        "preloader_available": True,
                        "get_method_exists": has_get_method,
                        "spk_rule_exists": rule_exists,
                        "rule_details": str(rule) if rule else None
                    }
                    
                    return rule_exists
                    
                except Exception as rule_error:
                    print_result("SPK-USDT_gate_spot规则获取", False, f"错误: {rule_error}")
                    self.details["trading_rules_preloader"] = {
                        "preloader_available": True,
                        "get_method_exists": has_get_method,
                        "rule_error": str(rule_error)
                    }
                    return False
            else:
                self.details["trading_rules_preloader"] = {
                    "preloader_available": True,
                    "get_method_exists": False
                }
                return False
                
        except Exception as e:
            print_result("交易规则预加载器检查", False, f"错误: {e}")
            self.details["trading_rules_preloader"] = {"error": str(e)}
            return False
    
    def check_temporary_instance_creation(self) -> bool:
        """检查临时实例创建机制"""
        print_section("检查临时实例创建机制")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查临时实例创建方法
            has_temp_method = hasattr(preloader, '_create_temporary_exchange_instance_sync')
            print_result("临时实例创建方法存在", has_temp_method)
            
            if has_temp_method:
                # 测试三个交易所的临时实例创建
                exchanges = ["gate", "bybit", "okx"]
                creation_results = {}
                
                for exchange in exchanges:
                    try:
                        temp_instance = preloader._create_temporary_exchange_instance_sync(exchange)
                        success = temp_instance is not None
                        creation_results[exchange] = success
                        print_result(f"{exchange}临时实例创建", success, 
                                   f"实例类型: {type(temp_instance)}" if success else "创建失败")
                    except Exception as ex_error:
                        creation_results[exchange] = False
                        print_result(f"{exchange}临时实例创建", False, f"错误: {ex_error}")
                
                success_count = sum(creation_results.values())
                total_success = success_count == len(exchanges)
                
                self.details["temporary_instance"] = {
                    "method_exists": has_temp_method,
                    "creation_results": creation_results,
                    "success_count": success_count,
                    "total_count": len(exchanges)
                }
                
                return total_success
            else:
                self.details["temporary_instance"] = {"method_exists": False}
                return False
                
        except Exception as e:
            print_result("临时实例创建机制检查", False, f"错误: {e}")
            self.details["temporary_instance"] = {"error": str(e)}
            return False
    
    def check_api_keys_configuration(self) -> bool:
        """检查API密钥配置"""
        print_section("检查API密钥配置")
        
        api_keys = {
            "GATE_API_KEY": os.getenv("GATE_API_KEY"),
            "GATE_API_SECRET": os.getenv("GATE_API_SECRET"),
            "BYBIT_API_KEY": os.getenv("BYBIT_API_KEY"),
            "BYBIT_API_SECRET": os.getenv("BYBIT_API_SECRET"),
            "OKX_API_KEY": os.getenv("OKX_API_KEY"),
            "OKX_API_SECRET": os.getenv("OKX_API_SECRET"),
            "OKX_API_PASSPHRASE": os.getenv("OKX_API_PASSPHRASE")
        }
        
        configured_count = sum(1 for key, value in api_keys.items() if value)
        total_count = len(api_keys)
        
        print_result("API密钥配置检查", configured_count > 0, 
                   f"已配置: {configured_count}/{total_count}")
        
        for key, value in api_keys.items():
            has_value = bool(value)
            print_result(f"  {key}", has_value, "已配置" if has_value else "未配置")
        
        self.details["api_keys"] = {
            "configured_count": configured_count,
            "total_count": total_count,
            "keys_status": {k: bool(v) for k, v in api_keys.items()}
        }
        
        return configured_count > 0
    
    async def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行综合诊断"""
        print_section("SPK-USDT_gate_spot交易规则获取失败问题综合诊断")
        print("基于用户报告错误: 2025-07-30 09:16:00.381 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_spot")
        
        start_time = time.time()
        
        # 执行各项检查
        self.diagnosis_results["global_exchanges_check"] = self.check_global_exchanges_function()
        self.diagnosis_results["initialize_all_systems_check"] = self.check_initialize_all_systems_method()
        self.diagnosis_results["trading_rules_preloader_check"] = self.check_trading_rules_preloader()
        self.diagnosis_results["temporary_instance_check"] = self.check_temporary_instance_creation()
        self.diagnosis_results["api_keys_check"] = self.check_api_keys_configuration()
        
        # 综合分析
        print_section("综合诊断结果")
        
        passed_count = sum(self.diagnosis_results.values())
        total_count = len(self.diagnosis_results)
        success_rate = (passed_count / total_count) * 100
        
        print(f"📊 诊断统计:")
        print(f"   通过检查: {passed_count}/{total_count}")
        print(f"   成功率: {success_rate:.1f}%")
        print(f"   诊断耗时: {time.time() - start_time:.2f}秒")
        
        # 问题根源分析
        print(f"\n🎯 问题根源分析:")
        
        if not self.diagnosis_results["global_exchanges_check"]:
            print("❌ 全局交易所实例管理函数不可用")
        elif not self.diagnosis_results["initialize_all_systems_check"]:
            print("❌ initialize_all_systems方法未调用set_global_exchanges")
        elif not self.diagnosis_results["api_keys_check"]:
            print("❌ API密钥配置不完整，临时实例创建失败")
        elif not self.diagnosis_results["temporary_instance_check"]:
            print("❌ 临时实例创建机制不工作")
        else:
            print("✅ 所有检查通过，问题可能在其他地方")
        
        # 修复建议
        print(f"\n💡 修复建议:")
        if not self.diagnosis_results["initialize_all_systems_check"]:
            print("1. 在initialize_all_systems()方法中添加set_global_exchanges(self.exchanges)调用")
        if not self.diagnosis_results["api_keys_check"]:
            print("2. 配置完整的API密钥到.env文件")
        if not self.diagnosis_results["temporary_instance_check"]:
            print("3. 修复临时实例创建机制")
        
        return {
            "diagnosis_results": self.diagnosis_results,
            "details": self.details,
            "success_rate": success_rate,
            "passed_count": passed_count,
            "total_count": total_count
        }

async def main():
    """主函数"""
    diagnosis = SPKUSDTTradingRulesDiagnosis()
    results = await diagnosis.run_comprehensive_diagnosis()
    
    # 保存诊断结果
    output_file = "123/tests/spk_usdt_diagnosis_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 诊断结果已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
