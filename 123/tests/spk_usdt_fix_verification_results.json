{"verification_results": {"global_exchanges_timing_fix": true, "trading_system_init_fix": true, "spk_usdt_rule_after_fix": true, "batch_preload_test": true, "three_exchanges_consistency": true}, "details": {"timing_fix": {"set_line": 18, "preload_line": 30, "timing_fixed": true, "has_step_0": true}, "init_fix": {"initial_global_none": true, "set_success": true, "rule_success": true, "exchanges_count": 3}, "batch_preload": {"success_count": 18, "total_count": 18, "success_rate": 100.0}, "consistency": {"spot_success": 3, "futures_success": 3, "results": {"spot": {"gate": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='gate', market_type='spot', qty_step=Decimal('0.0001'), price_step=Decimal('0.01'), qty_precision=4, price_precision=4, min_qty=Decimal('0.0001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753874951.2766087)"}, "bybit": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='bybit', market_type='spot', qty_step=Decimal('0.001'), price_step=Decimal('0.01'), qty_precision=3, price_precision=2, min_qty=Decimal('0.001'), max_qty=Decimal('1000000'), min_notional=Decimal('5.0'), source='default', timestamp=1753874951.2816062)"}, "okx": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='okx', market_type='spot', qty_step=Decimal('0.00001'), price_step=Decimal('0.01'), qty_precision=5, price_precision=5, min_qty=Decimal('0.00001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753874951.2866037)"}}, "futures": {"gate": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='gate', market_type='futures', qty_step=Decimal('0.0001'), price_step=Decimal('0.01'), qty_precision=4, price_precision=4, min_qty=Decimal('0.0001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753874951.2786076)"}, "bybit": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='bybit', market_type='futures', qty_step=Decimal('0.001'), price_step=Decimal('0.01'), qty_precision=3, price_precision=2, min_qty=Decimal('0.001'), max_qty=Decimal('1000000'), min_notional=Decimal('5.0'), source='default', timestamp=1753874951.2826061)"}, "okx": {"success": true, "rule": "TradingRule(symbol='SPK-USDT', exchange='okx', market_type='futures', qty_step=Decimal('0.00001'), price_step=Decimal('0.01'), qty_precision=5, price_precision=5, min_qty=Decimal('0.00001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753874951.2886028)"}}}}}, "success_rate": 100.0, "passed_count": 5, "total_count": 5, "fix_quality": "优秀"}