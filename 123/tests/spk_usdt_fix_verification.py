#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT_gate_spot交易规则获取失败问题修复验证脚本
2025-07-30 创建

验证修复效果：
1. 验证全局交易所实例设置时机
2. 测试SPK-USDT_gate_spot交易规则获取
3. 验证临时实例创建机制
4. 确保三交易所一致性
"""

import os
import sys
import asyncio
import time
import json
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_result(test_name: str, result: bool, details: str = ""):
    """打印测试结果"""
    status = "✅ 通过" if result else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

class SPKUSDTFixVerification:
    """SPK-USDT交易规则获取失败问题修复验证器"""
    
    def __init__(self):
        self.verification_results = {
            "global_exchanges_timing_fix": False,
            "trading_system_init_fix": False,
            "spk_usdt_rule_after_fix": False,
            "batch_preload_test": False,
            "three_exchanges_consistency": False
        }
        self.details = {}
    
    def verify_global_exchanges_timing_fix(self) -> bool:
        """验证全局交易所实例设置时机修复"""
        print_section("验证全局交易所实例设置时机修复")
        
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            import inspect
            
            # 检查initialize_trading_system方法源码
            initializer = TradingSystemInitializer()
            source = inspect.getsource(initializer.initialize_trading_system)
            
            # 检查set_global_exchanges调用是否在预加载之前
            lines = source.split('\n')
            set_line = -1
            preload_line = -1
            
            for i, line in enumerate(lines):
                if "set_global_exchanges" in line and "Step 0" in lines[i-1] if i > 0 else False:
                    set_line = i
                if "preload_all_trading_rules" in line:
                    preload_line = i
            
            timing_fixed = set_line != -1 and preload_line != -1 and set_line < preload_line
            
            print_result("全局交易所实例设置时机", timing_fixed, 
                        f"set_global_exchanges在第{set_line}行，预加载在第{preload_line}行")
            
            # 检查Step 0是否存在
            has_step_0 = "Step 0" in source
            print_result("Step 0关键修复存在", has_step_0)
            
            self.details["timing_fix"] = {
                "set_line": set_line,
                "preload_line": preload_line,
                "timing_fixed": timing_fixed,
                "has_step_0": has_step_0
            }
            
            return timing_fixed and has_step_0
            
        except Exception as e:
            print_result("全局交易所实例设置时机修复验证", False, f"错误: {e}")
            self.details["timing_fix"] = {"error": str(e)}
            return False
    
    async def verify_trading_system_init_fix(self) -> bool:
        """验证交易系统初始化修复"""
        print_section("验证交易系统初始化修复")
        
        try:
            from core.trading_system_initializer import get_trading_system_initializer, get_global_exchanges
            
            # 创建模拟交易所实例
            mock_exchanges = {
                'gate': type('MockGateExchange', (), {'name': 'gate'}),
                'bybit': type('MockBybitExchange', (), {'name': 'bybit'}),
                'okx': type('MockOKXExchange', (), {'name': 'okx'})
            }
            
            # 测试初始化过程
            initializer = get_trading_system_initializer()
            
            # 验证初始状态
            initial_global = get_global_exchanges()
            print_result("初始全局交易所实例状态", initial_global is None, f"初始状态: {initial_global}")
            
            # 模拟initialize_trading_system调用（只测试全局实例设置部分）
            from core.trading_system_initializer import set_global_exchanges
            set_global_exchanges(mock_exchanges)
            
            # 验证设置后状态
            after_set_global = get_global_exchanges()
            set_success = after_set_global is not None and len(after_set_global) == 3
            print_result("全局交易所实例设置成功", set_success, 
                        f"设置后状态: {list(after_set_global.keys()) if after_set_global else None}")
            
            # 测试交易规则预加载器能否获取全局实例
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试SPK-USDT_gate_spot规则获取（现在应该能获取到全局实例）
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            rule_success = rule is not None
            print_result("SPK-USDT_gate_spot规则获取", rule_success, 
                        f"规则: {rule}" if rule else "规则为None")
            
            self.details["init_fix"] = {
                "initial_global_none": initial_global is None,
                "set_success": set_success,
                "rule_success": rule_success,
                "exchanges_count": len(after_set_global) if after_set_global else 0
            }
            
            return set_success and rule_success
            
        except Exception as e:
            print_result("交易系统初始化修复验证", False, f"错误: {e}")
            self.details["init_fix"] = {"error": str(e)}
            return False
    
    async def verify_batch_preload_test(self) -> bool:
        """验证批量预加载测试"""
        print_section("验证批量预加载测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import get_global_exchanges
            
            preloader = get_trading_rules_preloader()
            global_exchanges = get_global_exchanges()
            
            if not global_exchanges:
                print_result("批量预加载测试", False, "全局交易所实例未设置")
                return False
            
            # 测试多个交易对的规则获取
            test_symbols = ["SPK-USDT", "RESOLV-USDT", "ICNT-USDT"]
            success_count = 0
            total_count = 0
            
            for symbol in test_symbols:
                for exchange in ["gate", "bybit", "okx"]:
                    for market_type in ["spot", "futures"]:
                        total_count += 1
                        try:
                            rule = preloader.get_trading_rule(exchange, symbol, market_type)
                            if rule:
                                success_count += 1
                                print_result(f"  {symbol}_{exchange}_{market_type}", True, "规则获取成功")
                            else:
                                print_result(f"  {symbol}_{exchange}_{market_type}", False, "规则为None")
                        except Exception as e:
                            print_result(f"  {symbol}_{exchange}_{market_type}", False, f"错误: {e}")
            
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
            batch_success = success_rate > 50  # 至少50%成功率
            
            print_result("批量预加载测试", batch_success, 
                        f"成功率: {success_rate:.1f}% ({success_count}/{total_count})")
            
            self.details["batch_preload"] = {
                "success_count": success_count,
                "total_count": total_count,
                "success_rate": success_rate
            }
            
            return batch_success
            
        except Exception as e:
            print_result("批量预加载测试", False, f"错误: {e}")
            self.details["batch_preload"] = {"error": str(e)}
            return False
    
    async def verify_three_exchanges_consistency(self) -> bool:
        """验证三交易所一致性"""
        print_section("验证三交易所一致性")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            exchanges = ["gate", "bybit", "okx"]
            test_symbol = "SPK-USDT"
            
            # 测试现货和期货规则获取一致性
            consistency_results = {}
            
            for market_type in ["spot", "futures"]:
                market_results = {}
                for exchange in exchanges:
                    try:
                        rule = preloader.get_trading_rule(exchange, test_symbol, market_type)
                        market_results[exchange] = {
                            "success": rule is not None,
                            "rule": str(rule) if rule else None
                        }
                    except Exception as e:
                        market_results[exchange] = {
                            "success": False,
                            "error": str(e)
                        }
                
                consistency_results[market_type] = market_results
            
            # 计算一致性
            spot_success = sum(1 for ex in exchanges if consistency_results["spot"][ex]["success"])
            futures_success = sum(1 for ex in exchanges if consistency_results["futures"][ex]["success"])
            
            consistency_good = spot_success >= 2 and futures_success >= 2  # 至少2个交易所成功
            
            print_result("现货规则一致性", spot_success >= 2, f"成功: {spot_success}/3")
            print_result("期货规则一致性", futures_success >= 2, f"成功: {futures_success}/3")
            print_result("三交易所整体一致性", consistency_good)
            
            self.details["consistency"] = {
                "spot_success": spot_success,
                "futures_success": futures_success,
                "results": consistency_results
            }
            
            return consistency_good
            
        except Exception as e:
            print_result("三交易所一致性验证", False, f"错误: {e}")
            self.details["consistency"] = {"error": str(e)}
            return False
    
    async def run_comprehensive_verification(self) -> Dict[str, Any]:
        """运行综合修复验证"""
        print_section("SPK-USDT_gate_spot交易规则获取失败问题修复验证")
        print("验证2025-07-30修复效果")
        
        start_time = time.time()
        
        # 执行各项验证
        self.verification_results["global_exchanges_timing_fix"] = self.verify_global_exchanges_timing_fix()
        self.verification_results["trading_system_init_fix"] = await self.verify_trading_system_init_fix()
        self.verification_results["spk_usdt_rule_after_fix"] = self.verification_results["trading_system_init_fix"]  # 复用结果
        self.verification_results["batch_preload_test"] = await self.verify_batch_preload_test()
        self.verification_results["three_exchanges_consistency"] = await self.verify_three_exchanges_consistency()
        
        # 综合分析
        print_section("综合修复验证结果")
        
        passed_count = sum(self.verification_results.values())
        total_count = len(self.verification_results)
        success_rate = (passed_count / total_count) * 100
        
        print(f"📊 修复验证统计:")
        print(f"   通过验证: {passed_count}/{total_count}")
        print(f"   修复成功率: {success_rate:.1f}%")
        print(f"   验证耗时: {time.time() - start_time:.2f}秒")
        
        # 修复效果分析
        print(f"\n🎯 修复效果分析:")
        
        if self.verification_results["global_exchanges_timing_fix"]:
            print("✅ 全局交易所实例设置时机已修复")
        else:
            print("❌ 全局交易所实例设置时机修复失败")
            
        if self.verification_results["spk_usdt_rule_after_fix"]:
            print("✅ SPK-USDT_gate_spot交易规则获取已修复")
        else:
            print("❌ SPK-USDT_gate_spot交易规则获取仍有问题")
        
        if success_rate >= 80:
            print("🎉 修复质量：优秀 (≥80%)")
        elif success_rate >= 60:
            print("⚠️ 修复质量：良好 (≥60%)")
        else:
            print("❌ 修复质量：需要改进 (<60%)")
        
        return {
            "verification_results": self.verification_results,
            "details": self.details,
            "success_rate": success_rate,
            "passed_count": passed_count,
            "total_count": total_count,
            "fix_quality": "优秀" if success_rate >= 80 else "良好" if success_rate >= 60 else "需要改进"
        }

async def main():
    """主函数"""
    verification = SPKUSDTFixVerification()
    results = await verification.run_comprehensive_verification()
    
    # 保存验证结果
    output_file = "123/tests/spk_usdt_fix_verification_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 修复验证结果已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
