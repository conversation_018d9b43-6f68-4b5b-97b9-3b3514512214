{"test_start_time": "2025-07-31T18:07:34.337227", "test_summary": {"total_tests": 0, "passed_tests": 0, "failed_tests": 0, "success_rate": 0.0}, "stage_1_basic_tests": {"precision_handling_tests": [{"test_case": "极端高精度测试 1e-06", "step_size": 1e-06, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-07", "step_size": 1e-07, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-08", "step_size": 1e-08, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-09", "step_size": 1e-09, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-10", "step_size": 1e-10, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-11", "step_size": 1e-11, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-12", "step_size": 1e-12, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-13", "step_size": 1e-13, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-14", "step_size": 1e-14, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-15", "step_size": 1e-15, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-16", "step_size": 1e-16, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-17", "step_size": 1e-17, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-18", "step_size": 1e-18, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-19", "step_size": 1e-19, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-20", "step_size": 1e-20, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-21", "step_size": 1e-21, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-22", "step_size": 1e-22, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-23", "step_size": 1e-23, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-24", "step_size": 1e-24, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-25", "step_size": 1e-25, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 101.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 102.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 103.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 104.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 105.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 106.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 107.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 108.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 109.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 110.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 111.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 112.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 113.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 114.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 115.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 116.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 117.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 118.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 119.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 120.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 121.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 122.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 123.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 124.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 125.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 126.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 127.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 128.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 129.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.1, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 50.2, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 50.3, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.4, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.5, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 50.6, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 50.7, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.8, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.9, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.1, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 51.2, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 51.3, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.4, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.5, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 51.6, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 51.7, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.8, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.9, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 0", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 1", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 2", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 3", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 4", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 5", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 6", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 7", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 8", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 9", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 10", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 11", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 12", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 13", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 14", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 BTC-USDT", "step_size": 1e-06, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 ETH-USDT", "step_size": 1e-05, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 RESOLV-USDT", "step_size": 0.1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 DOGE-USDT", "step_size": 1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SOL-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 MATIC-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 DOT-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 CAKE-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 WIF-USDT", "step_size": 0.0001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 AI16Z-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 JUP-USDT", "step_size": 0.0001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SPK-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 ICNT-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 PEPE-USDT", "step_size": 1000, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SHIB-USDT", "step_size": 100000, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}], "default_value_tests": [{"exchange": "bybit", "market": "spot", "step_size": 0.1, "passed": true, "details": "默认值测试: 0.1"}, {"exchange": "bybit", "market": "futures", "step_size": 0.01, "passed": true, "details": "默认值测试: 0.01"}, {"exchange": "gate", "market": "spot", "step_size": 0.0001, "passed": false, "details": "默认值测试: 0.0001"}, {"exchange": "gate", "market": "futures", "step_size": 0.001, "passed": false, "details": "默认值测试: 0.001"}, {"exchange": "okx", "market": "spot", "step_size": 0.1, "passed": true, "details": "默认值测试: 0.1"}, {"exchange": "okx", "market": "futures", "step_size": 0.01, "passed": true, "details": "默认值测试: 0.01"}], "error_handling_tests": [], "performance_tests": []}, "stage_2_system_tests": {}, "stage_3_production_simulation": {}, "detailed_results": [], "performance_metrics": {}, "error_analysis": [{"test": "precision_test_0", "error": "'total_tests'"}, {"test": "precision_test_1", "error": "'total_tests'"}, {"test": "precision_test_2", "error": "'total_tests'"}, {"test": "precision_test_3", "error": "'total_tests'"}, {"test": "precision_test_4", "error": "'total_tests'"}, {"test": "precision_test_5", "error": "'total_tests'"}, {"test": "precision_test_6", "error": "'total_tests'"}, {"test": "precision_test_7", "error": "'total_tests'"}, {"test": "precision_test_8", "error": "'total_tests'"}, {"test": "precision_test_9", "error": "'total_tests'"}, {"test": "precision_test_10", "error": "'total_tests'"}, {"test": "precision_test_11", "error": "'total_tests'"}, {"test": "precision_test_12", "error": "'total_tests'"}, {"test": "precision_test_13", "error": "'total_tests'"}, {"test": "precision_test_14", "error": "'total_tests'"}, {"test": "precision_test_15", "error": "'total_tests'"}, {"test": "precision_test_16", "error": "'total_tests'"}, {"test": "precision_test_17", "error": "'total_tests'"}, {"test": "precision_test_18", "error": "'total_tests'"}, {"test": "precision_test_19", "error": "'total_tests'"}, {"test": "precision_test_20", "error": "'total_tests'"}, {"test": "precision_test_21", "error": "'total_tests'"}, {"test": "precision_test_22", "error": "'total_tests'"}, {"test": "precision_test_23", "error": "'total_tests'"}, {"test": "precision_test_24", "error": "'total_tests'"}, {"test": "precision_test_25", "error": "'total_tests'"}, {"test": "precision_test_26", "error": "'total_tests'"}, {"test": "precision_test_27", "error": "'total_tests'"}, {"test": "precision_test_28", "error": "'total_tests'"}, {"test": "precision_test_29", "error": "'total_tests'"}, {"test": "precision_test_30", "error": "'total_tests'"}, {"test": "precision_test_31", "error": "'total_tests'"}, {"test": "precision_test_32", "error": "'total_tests'"}, {"test": "precision_test_33", "error": "'total_tests'"}, {"test": "precision_test_34", "error": "'total_tests'"}, {"test": "precision_test_35", "error": "'total_tests'"}, {"test": "precision_test_36", "error": "'total_tests'"}, {"test": "precision_test_37", "error": "'total_tests'"}, {"test": "precision_test_38", "error": "'total_tests'"}, {"test": "precision_test_39", "error": "'total_tests'"}, {"test": "precision_test_40", "error": "'total_tests'"}, {"test": "precision_test_41", "error": "'total_tests'"}, {"test": "precision_test_42", "error": "'total_tests'"}, {"test": "precision_test_43", "error": "'total_tests'"}, {"test": "precision_test_44", "error": "'total_tests'"}, {"test": "precision_test_45", "error": "'total_tests'"}, {"test": "precision_test_46", "error": "'total_tests'"}, {"test": "precision_test_47", "error": "'total_tests'"}, {"test": "precision_test_48", "error": "'total_tests'"}, {"test": "precision_test_49", "error": "'total_tests'"}, {"test": "precision_test_50", "error": "'total_tests'"}, {"test": "precision_test_51", "error": "'total_tests'"}, {"test": "precision_test_52", "error": "'total_tests'"}, {"test": "precision_test_53", "error": "'total_tests'"}, {"test": "precision_test_54", "error": "'total_tests'"}, {"test": "precision_test_55", "error": "'total_tests'"}, {"test": "precision_test_56", "error": "'total_tests'"}, {"test": "precision_test_57", "error": "'total_tests'"}, {"test": "precision_test_58", "error": "'total_tests'"}, {"test": "precision_test_59", "error": "'total_tests'"}, {"test": "precision_test_60", "error": "'total_tests'"}, {"test": "precision_test_61", "error": "'total_tests'"}, {"test": "precision_test_62", "error": "'total_tests'"}, {"test": "precision_test_63", "error": "'total_tests'"}, {"test": "precision_test_64", "error": "'total_tests'"}, {"test": "precision_test_65", "error": "'total_tests'"}, {"test": "precision_test_66", "error": "'total_tests'"}, {"test": "precision_test_67", "error": "'total_tests'"}, {"test": "precision_test_68", "error": "'total_tests'"}, {"test": "precision_test_69", "error": "'total_tests'"}, {"test": "precision_test_70", "error": "'total_tests'"}, {"test": "precision_test_71", "error": "'total_tests'"}, {"test": "precision_test_72", "error": "'total_tests'"}, {"test": "precision_test_73", "error": "'total_tests'"}, {"test": "precision_test_74", "error": "'total_tests'"}, {"test": "precision_test_75", "error": "'total_tests'"}, {"test": "precision_test_76", "error": "'total_tests'"}, {"test": "precision_test_77", "error": "'total_tests'"}, {"test": "precision_test_78", "error": "'total_tests'"}, {"test": "precision_test_79", "error": "'total_tests'"}, {"test": "precision_test_80", "error": "'total_tests'"}, {"test": "precision_test_81", "error": "'total_tests'"}, {"test": "precision_test_82", "error": "'total_tests'"}, {"test": "precision_test_83", "error": "'total_tests'"}, {"test": "precision_test_84", "error": "'total_tests'"}, {"test": "precision_test_85", "error": "'total_tests'"}, {"test": "precision_test_86", "error": "'total_tests'"}, {"test": "precision_test_87", "error": "'total_tests'"}, {"test": "precision_test_88", "error": "'total_tests'"}, {"test": "precision_test_89", "error": "'total_tests'"}, {"test": "precision_test_90", "error": "'total_tests'"}, {"test": "precision_test_91", "error": "'total_tests'"}, {"test": "precision_test_92", "error": "'total_tests'"}, {"test": "precision_test_93", "error": "'total_tests'"}, {"test": "precision_test_94", "error": "'total_tests'"}, {"test": "precision_test_95", "error": "'total_tests'"}, {"test": "precision_test_96", "error": "'total_tests'"}, {"test": "precision_test_97", "error": "'total_tests'"}, {"test": "precision_test_98", "error": "'total_tests'"}, {"test": "precision_test_99", "error": "'total_tests'"}, {"stage": "main_validation", "error": "'total_tests'", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\64B 异步调用缺陷和精度错误\\123\\tests\\comprehensive_precision_validation.py\", line 137, in run_comprehensive_validation\n    await self._stage_2_system_tests()\n  File \"C:\\Users\\<USER>\\Desktop\\64B 异步调用缺陷和精度错误\\123\\tests\\comprehensive_precision_validation.py\", line 306, in _stage_2_system_tests\n    self.results[\"total_tests\"] += 1\nKeyError: 'total_tests'\n"}]}