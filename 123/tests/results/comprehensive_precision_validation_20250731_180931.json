{"test_start_time": "2025-07-31T18:09:31.071530", "test_summary": {"total_tests": 129, "passed_tests": 118, "failed_tests": 11, "success_rate": 0.9147286821705426}, "stage_1_basic_tests": {"precision_handling_tests": [{"test_case": "极端高精度测试 1e-06", "step_size": 1e-06, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-07", "step_size": 1e-07, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-08", "step_size": 1e-08, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-09", "step_size": 1e-09, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-10", "step_size": 1e-10, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-11", "step_size": 1e-11, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-12", "step_size": 1e-12, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-13", "step_size": 1e-13, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-14", "step_size": 1e-14, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-15", "step_size": 1e-15, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-16", "step_size": 1e-16, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-17", "step_size": 1e-17, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-18", "step_size": 1e-18, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-19", "step_size": 1e-19, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-20", "step_size": 1e-20, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-21", "step_size": 1e-21, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-22", "step_size": 1e-22, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-23", "step_size": 1e-23, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-24", "step_size": 1e-24, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "极端高精度测试 1e-25", "step_size": 1e-25, "test_amount": 173.123456789, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 101.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 102.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 103.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 104.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 105.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 106.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 107.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 108.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 109.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 110.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 111.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 112.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 113.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 114.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 115.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 116.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 117.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 118.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 119.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 120.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 121.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 122.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 123.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 124.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.1", "step_size": 0.1, "test_amount": 125.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.01", "step_size": 0.01, "test_amount": 126.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.001", "step_size": 0.001, "test_amount": 127.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 0.0001", "step_size": 0.0001, "test_amount": 128.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "正常精度测试 1e-05", "step_size": 1e-05, "test_amount": 129.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.1, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 50.2, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 50.3, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.4, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.5, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 50.6, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 50.7, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 50.8, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 50.9, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.1, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 51.2, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 51.3, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.4, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.5, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 0.0001", "step_size": 0.0001, "test_amount": 51.6, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-05", "step_size": 1e-05, "test_amount": 51.7, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-06", "step_size": 1e-06, "test_amount": 51.8, "passed": true, "details": "精度处理测试通过"}, {"test_case": "边界精度测试 1e-07", "step_size": 1e-07, "test_amount": 51.9, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 0", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 1", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 2", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 3", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 4", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 5", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 6", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 7", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 8", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 9", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 10", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 11", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 12", "step_size": 0, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 13", "step_size": -0.001, "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "异常精度测试 14", "step_size": "invalid", "test_amount": 25.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 BTC-USDT", "step_size": 1e-06, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 ETH-USDT", "step_size": 1e-05, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 RESOLV-USDT", "step_size": 0.1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 DOGE-USDT", "step_size": 1, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SOL-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 MATIC-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 DOT-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 CAKE-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 WIF-USDT", "step_size": 0.0001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 AI16Z-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 JUP-USDT", "step_size": 0.0001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SPK-USDT", "step_size": 0.01, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 ICNT-USDT", "step_size": 0.001, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 PEPE-USDT", "step_size": 1000, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}, {"test_case": "真实代币精度测试 SHIB-USDT", "step_size": 100000, "test_amount": 100.0, "passed": true, "details": "精度处理测试通过"}], "default_value_tests": [{"exchange": "bybit", "market": "spot", "step_size": 0.1, "passed": true, "details": "默认值测试: 0.1"}, {"exchange": "bybit", "market": "futures", "step_size": 0.01, "passed": true, "details": "默认值测试: 0.01"}, {"exchange": "gate", "market": "spot", "step_size": 0.0001, "passed": false, "details": "默认值测试: 0.0001"}, {"exchange": "gate", "market": "futures", "step_size": 0.001, "passed": false, "details": "默认值测试: 0.001"}, {"exchange": "okx", "market": "spot", "step_size": 0.1, "passed": true, "details": "默认值测试: 0.1"}, {"exchange": "okx", "market": "futures", "step_size": 0.01, "passed": true, "details": "默认值测试: 0.01"}], "error_handling_tests": [], "performance_tests": []}, "stage_2_system_tests": {"multi_exchange_consistency": [{"symbol": "RESOLV-USDT", "exchanges": ["bybit", "gate", "okx"], "results": {"bybit_spot": {"step_size": 0.1, "is_conservative": true}, "bybit_futures": {"step_size": 0.01, "is_conservative": true}, "gate_spot": {"step_size": 0.0001, "is_conservative": false}, "gate_futures": {"step_size": 0.001, "is_conservative": false}, "okx_spot": {"step_size": 0.1, "is_conservative": true}, "okx_futures": {"step_size": 0.01, "is_conservative": true}}, "consistency_ratio": 0.6666666666666666, "passed": false, "details": "一致性比例: 66.67%"}, {"symbol": "BTC-USDT", "exchanges": ["bybit", "gate", "okx"], "results": {"bybit_spot": {"step_size": 0.1, "is_conservative": true}, "bybit_futures": {"step_size": 0.01, "is_conservative": true}, "gate_spot": {"step_size": 0.0001, "is_conservative": false}, "gate_futures": {"step_size": 0.001, "is_conservative": false}, "okx_spot": {"step_size": 0.1, "is_conservative": true}, "okx_futures": {"step_size": 0.01, "is_conservative": true}}, "consistency_ratio": 0.6666666666666666, "passed": false, "details": "一致性比例: 66.67%"}, {"symbol": "ETH-USDT", "exchanges": ["bybit", "gate", "okx"], "results": {"bybit_spot": {"step_size": 0.1, "is_conservative": true}, "bybit_futures": {"step_size": 0.01, "is_conservative": true}, "gate_spot": {"step_size": 0.0001, "is_conservative": false}, "gate_futures": {"step_size": 0.001, "is_conservative": false}, "okx_spot": {"step_size": 0.1, "is_conservative": true}, "okx_futures": {"step_size": 0.01, "is_conservative": true}}, "consistency_ratio": 0.6666666666666666, "passed": false, "details": "一致性比例: 66.67%"}, {"symbol": "DOGE-USDT", "exchanges": ["bybit", "gate", "okx"], "results": {"bybit_spot": {"step_size": 0.1, "is_conservative": true}, "bybit_futures": {"step_size": 0.01, "is_conservative": true}, "gate_spot": {"step_size": 0.0001, "is_conservative": false}, "gate_futures": {"step_size": 0.001, "is_conservative": false}, "okx_spot": {"step_size": 0.1, "is_conservative": true}, "okx_futures": {"step_size": 0.01, "is_conservative": true}}, "consistency_ratio": 0.6666666666666666, "passed": false, "details": "一致性比例: 66.67%"}], "token_switching_tests": [{"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}, {"test_type": "token_switching", "total_switches": 12, "successful_switches": 12, "success_rate": 1.0, "passed": true, "details": [{"token": "BTC-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "BTC-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "BTC-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "RESOLV-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "RESOLV-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "ETH-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "ETH-USDT", "exchange": "okx", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "bybit", "success": true, "step_size": 0.1}, {"token": "DOGE-USDT", "exchange": "gate", "success": true, "step_size": 0.0001}, {"token": "DOGE-USDT", "exchange": "okx", "success": true, "step_size": 0.1}]}], "cache_integration_tests": [{"test_type": "cache_integration", "cache_miss_handled": true, "default_value_reasonable": true, "step_size": 0.1, "passed": true, "details": "缓存集成测试通过"}], "api_fallback_tests": [{"test_type": "api_fallback", "total_fallback_tests": 6, "successful_fallbacks": 4, "success_rate": 0.6666666666666666, "passed": false, "details": [{"exchange": "bybit", "market": "spot", "fallback_success": true, "is_conservative": true, "step_size": 0.01}, {"exchange": "bybit", "market": "futures", "fallback_success": true, "is_conservative": true, "step_size": 0.01}, {"exchange": "gate", "market": "spot", "fallback_success": true, "is_conservative": false, "step_size": 0.0001}, {"exchange": "gate", "market": "futures", "fallback_success": true, "is_conservative": false, "step_size": 0.001}, {"exchange": "okx", "market": "spot", "fallback_success": true, "is_conservative": true, "step_size": 0.1}, {"exchange": "okx", "market": "futures", "fallback_success": true, "is_conservative": true, "step_size": 0.01}]}]}, "stage_3_production_simulation": {"real_precision_simulation": [{"scenario": {"symbol": "RESOLV-USDT", "amount": 173.01038, "expected_adjusted": 173.0}, "exchange_results": {"bybit": {"step_size": 0.1, "adjusted_amount": 173.0, "reasonable": true}, "gate": {"step_size": 0.0001, "adjusted_amount": 173.01, "reasonable": false}, "okx": {"step_size": 0.1, "adjusted_amount": 173.0, "reasonable": true}}, "reasonable_exchanges": 2, "total_exchanges": 3, "passed": false, "details": "真实场景仿真: RESOLV-USDT"}, {"scenario": {"symbol": "BTC-USDT", "amount": 0.123456789, "expected_adjusted": 0.1}, "exchange_results": {"bybit": {"step_size": 0.1, "adjusted_amount": 0.1, "reasonable": true}, "gate": {"step_size": 0.0001, "adjusted_amount": 0.123, "reasonable": false}, "okx": {"step_size": 0.1, "adjusted_amount": 0.1, "reasonable": true}}, "reasonable_exchanges": 2, "total_exchanges": 3, "passed": false, "details": "真实场景仿真: BTC-USDT"}, {"scenario": {"symbol": "DOGE-USDT", "amount": 1234.56789, "expected_adjusted": 1234}, "exchange_results": {"bybit": {"step_size": 0.1, "adjusted_amount": 1234.6, "reasonable": true}, "gate": {"step_size": 0.0001, "adjusted_amount": 1234.568, "reasonable": false}, "okx": {"step_size": 0.1, "adjusted_amount": 1234.6, "reasonable": true}}, "reasonable_exchanges": 2, "total_exchanges": 3, "passed": false, "details": "真实场景仿真: DOGE-USDT"}, {"scenario": {"symbol": "ETH-USDT", "amount": 5.987654321, "expected_adjusted": 5.9}, "exchange_results": {"bybit": {"step_size": 0.1, "adjusted_amount": 6.0, "reasonable": true}, "gate": {"step_size": 0.0001, "adjusted_amount": 5.988, "reasonable": false}, "okx": {"step_size": 0.1, "adjusted_amount": 6.0, "reasonable": true}}, "reasonable_exchanges": 2, "total_exchanges": 3, "passed": false, "details": "真实场景仿真: ETH-USDT"}], "concurrent_access_tests": [{"test_type": "concurrent_access", "total_concurrent_tasks": 50, "successful_tasks": 50, "success_rate": 1.0, "passed": true, "details": "并发测试成功率: 100.00%"}], "extreme_scenario_tests": [{"test_type": "extreme_scenarios", "total_extreme_tests": 6, "gracefully_handled": 6, "success_rate": 1.0, "passed": true, "details": [{"extreme_input": "0", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}, {"extreme_input": "-1", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}, {"extreme_input": "inf", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}, {"extreme_input": "nan", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}, {"extreme_input": "invalid", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}, {"extreme_input": "None", "handled_gracefully": true, "reasonable_output": true, "step_size": 0.1}]}], "performance_stress_tests": [{"test_type": "performance_stress", "total_tests": 100, "successful_tests": 100, "avg_duration_ms": 0.0007829999999964254, "max_duration_ms": 0.0012000000000345068, "performance_ratio": 1.0, "passed": true, "details": "性能测试: 平均0.001ms, 100.0%在阈值内"}]}, "detailed_results": [], "performance_metrics": {"avg_duration_ms": 0.0007829999999964254, "max_duration_ms": 0.0012000000000345068, "min_duration_ms": 0.0006999999999646178, "performance_ratio": 1.0, "total_performance_tests": 100, "successful_performance_tests": 100}, "error_analysis": [], "test_end_time": "2025-07-31T18:09:31.079355", "final_assessment": "GOOD_FIX", "assessment_details": "👍 修复良好！大部分测试通过，需要关注失败项"}