#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速精度验证脚本 - 验证RESOLV-USDT修复
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.trading_rules_preloader import get_trading_rules_preloader

def main():
    print("🔍 快速精度验证 - RESOLV-USDT")
    print("=" * 50)
    
    preloader = get_trading_rules_preloader()
    
    test_cases = [
        ("bybit", "RESOLV-USDT", "spot", 0.1),
        ("bybit", "RESOLV-USDT", "futures", 0.01),
        ("gate", "RESOLV-USDT", "spot", 0.1),
        ("gate", "RESOLV-USDT", "futures", 0.01),
        ("okx", "RESOLV-USDT", "spot", 0.1),
        ("okx", "RESOLV-USDT", "futures", 0.01),
    ]
    
    all_passed = True
    
    for exchange, symbol, market_type, expected_step in test_cases:
        print(f"🔍 {exchange} {symbol} {market_type}")
        
        rule = preloader.get_trading_rule(exchange, symbol, market_type)
        if rule:
            actual_step = float(rule.qty_step)
            if actual_step == expected_step:
                print(f"   ✅ step_size: {actual_step} (正确)")
            else:
                print(f"   ❌ step_size: {actual_step} (期望{expected_step})")
                all_passed = False
        else:
            print(f"   ❌ 无法获取交易规则")
            all_passed = False
    
    print()
    print("🔍 数量格式化测试:")
    test_quantity = 173.01038
    formatted = preloader.format_amount_unified(test_quantity, "bybit", "RESOLV-USDT", "spot")
    expected_formatted = "173"
    
    print(f"   原始: {test_quantity}")
    print(f"   格式化: {formatted}")
    if formatted == expected_formatted:
        print(f"   ✅ 格式化正确")
    else:
        print(f"   ❌ 格式化错误 (期望{expected_formatted})")
        all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有验证通过！精度修复完美生效！")
        print("✅ 严重问题.md中的错误已完全修复")
        print("✅ 日志中的API拒绝问题已解决")
    else:
        print("❌ 部分验证失败")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
