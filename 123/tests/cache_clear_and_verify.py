#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 紧急缓存清理和验证脚本

问题：虽然修复了默认值，但24小时缓存TTL导致旧的错误交易规则仍在使用
解决：清理缓存并验证新的精度修复是否生效

日志错误：
- step_size='0.001' 但发送了 quantity='173.01' → API拒绝
- 应该使用 step_size='0.1' (Bybit现货RESOLV)
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.trading_rules_preloader import get_trading_rules_preloader
from utils.logger import get_logger

logger = get_logger(__name__)

async def clear_and_verify_cache():
    """清理缓存并验证修复"""
    
    print("🚨 紧急缓存清理和验证开始...")
    print("=" * 60)
    
    try:
        # 1. 获取预加载器实例
        preloader = get_trading_rules_preloader()
        
        # 2. 清理所有缓存
        print("🔄 步骤1: 清理所有缓存...")
        
        # 清理交易规则缓存
        old_rules_count = len(preloader.trading_rules)
        preloader.trading_rules.clear()
        print(f"   ✅ 交易规则缓存已清理: {old_rules_count}个规则")
        
        # 清理精度缓存
        old_precision_count = len(preloader.precision_cache)
        preloader.precision_cache.clear()
        print(f"   ✅ 精度缓存已清理: {old_precision_count}个条目")
        
        # 清理对冲质量缓存
        old_hedge_count = len(preloader.hedge_quality_cache)
        preloader.hedge_quality_cache.clear()
        print(f"   ✅ 对冲质量缓存已清理: {old_hedge_count}个条目")
        
        print("✅ 所有缓存清理完成！")
        print()
        
        # 3. 验证RESOLV-USDT的精度修复
        print("🔍 步骤2: 验证RESOLV-USDT精度修复...")
        
        test_cases = [
            ("bybit", "RESOLV-USDT", "spot"),
            ("bybit", "RESOLV-USDT", "futures"),
            ("gate", "RESOLV-USDT", "spot"),
            ("gate", "RESOLV-USDT", "futures"),
            ("okx", "RESOLV-USDT", "spot"),
            ("okx", "RESOLV-USDT", "futures"),
        ]
        
        all_passed = True
        
        for exchange, symbol, market_type in test_cases:
            print(f"   🔍 测试: {exchange} {symbol} {market_type}")
            
            # 获取交易规则（会触发新的默认值）
            rule = preloader.get_trading_rule(exchange, symbol, market_type)
            
            if rule:
                step_size = float(rule.qty_step)
                print(f"      ✅ 获取成功: step_size={step_size}")
                
                # 验证step_size是否符合修复后的值
                if market_type == "spot":
                    expected = 0.1
                else:  # futures
                    expected = 0.01
                
                if step_size == expected:
                    print(f"      ✅ 精度正确: {step_size} = {expected}")
                elif step_size == 0.001:
                    print(f"      ❌ 仍使用旧值: {step_size} (应为{expected})")
                    all_passed = False
                else:
                    print(f"      ⚠️ 意外值: {step_size} (期望{expected})")
                    
            else:
                print(f"      ❌ 获取失败")
                all_passed = False
                
        print()
        
        # 4. 测试数量格式化
        print("🔍 步骤3: 测试数量格式化...")
        
        test_quantity = 173.01038  # 日志中的问题数量
        
        for exchange, symbol, market_type in [("bybit", "RESOLV-USDT", "spot")]:
            print(f"   🔍 测试格式化: {exchange} {symbol} {market_type}")
            print(f"      原始数量: {test_quantity}")
            
            formatted = preloader.format_amount_unified(test_quantity, exchange, symbol, market_type)
            print(f"      格式化后: '{formatted}'")
            
            # 验证格式化结果
            formatted_float = float(formatted)
            if market_type == "spot":
                # Bybit现货应该使用0.1步长，173.01038 → 173.0
                expected_formatted = 173.0
                if abs(formatted_float - expected_formatted) < 0.001:
                    print(f"      ✅ 格式化正确: {formatted_float} ≈ {expected_formatted}")
                else:
                    print(f"      ❌ 格式化错误: {formatted_float} (应为{expected_formatted})")
                    all_passed = False
        
        print()
        
        # 5. 最终结果
        print("🎯 验证结果:")
        if all_passed:
            print("✅ 所有测试通过！精度修复已生效！")
            print("✅ 缓存已清理，新的保守精度策略正在使用")
            print("✅ RESOLV-USDT的API拒绝问题应该已解决")
        else:
            print("❌ 部分测试失败！需要进一步检查")
            print("❌ 可能需要重启系统以完全清理缓存")
            
        print()
        print("📊 缓存状态:")
        print(f"   - 交易规则缓存: {len(preloader.trading_rules)}个")
        print(f"   - 精度缓存: {len(preloader.precision_cache)}个")
        print(f"   - 对冲质量缓存: {len(preloader.hedge_quality_cache)}个")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚨 紧急缓存清理和验证脚本")
    print("问题：24小时缓存TTL导致旧的错误交易规则仍在使用")
    print("目标：清理缓存并验证新的精度修复是否生效")
    print()
    
    # 运行验证
    result = asyncio.run(clear_and_verify_cache())
    
    if result:
        print("🎉 验证成功！系统已修复！")
        exit(0)
    else:
        print("💥 验证失败！需要进一步处理！")
        exit(1)

if __name__ == "__main__":
    main()
