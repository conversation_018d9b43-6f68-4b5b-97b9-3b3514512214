#!/usr/bin/env python3
"""
API调用优化器 - 解决启动阶段API限速问题
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional
from collections import defaultdict

logger = logging.getLogger("api_optimizer")

class APICallOptimizer:
    """API调用优化器"""
    
    def __init__(self):
        self.logger = logger
        
        # API限制配置 - 🔥 优化修复：平衡限速和WebSocket性能
        self.rate_limits = {
            "gate": 8,     # 降低限制，确保健壮性
            "bybit": 4,    # 降低限制，确保健壮性
            "okx": 3       # 🔥 优化修复：提升到3次/秒，平衡限速和WebSocket性能
        }

        # 🔥 新增：冷却时间配置 (3.4秒 → 5秒)
        self.cooldown_config = {
            "base_cooldown": 1.5,      # 基础冷却1.5秒
            "buffer_cooldown": 3.5,    # 缓冲3.5秒
            "total_cooldown": 5.0      # 总冷却5.0秒
        }
        
        # 调用队列
        self.call_queues = {
            "gate": asyncio.Queue(),
            "bybit": asyncio.Queue(),
            "okx": asyncio.Queue()
        }
        
        # 调用统计
        self.call_stats = defaultdict(int)
        
    async def optimize_startup_api_calls(self, exchanges: Dict[str, Any], symbols: List[str]):
        """🔥 根源修复：分批预加载，确保30+代币健壮启动"""
        self.logger.info("🚀 开始分批预加载API调用优化...")

        # 🔥 实施分批预加载：每批5个任务，批间冷却2秒
        batch_size = 5
        batch_cooldown = 2.0

        # 1. 分批并行化交易规则预加载
        await self.batched_trading_rules_preload(exchanges, symbols, batch_size, batch_cooldown)

        # 2. 批量化余额查询（低频率）
        await self.batch_balance_queries(exchanges)

        # 3. 分批智能化合约信息获取
        await self.batched_contract_info_fetch(exchanges, symbols, batch_size, batch_cooldown)

        # 4. 延迟杠杆设置（最后执行）
        await self.delayed_leverage_setup(exchanges, symbols)

        self.logger.info("✅ 分批预加载API调用优化完成")
        
    async def parallel_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str]):
        """并行化交易规则预加载"""
        self.logger.info("📋 并行化交易规则预加载...")
        
        tasks = []
        
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_trading_rule,
                        exchange, symbol, market_type
                    )
                    tasks.append(task)
        
        # 并行执行，但受限于各交易所的API限制
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 交易规则预加载完成: {success_count}/{len(tasks)}")

    async def test_rate_limiting_effectiveness(self, exchange_name: str = "gate", test_calls: int = 5) -> Dict[str, Any]:
        """🔥 新增：测试限速效果的方法"""
        self.logger.info(f"🧪 测试{exchange_name}限速效果，调用次数: {test_calls}")

        rate_limit = self.rate_limits[exchange_name]
        expected_min_duration = (test_calls - 1) / rate_limit  # 前n-1次调用的最小间隔时间

        # 模拟API调用函数
        async def mock_api_call():
            await asyncio.sleep(0.01)  # 模拟10ms的API响应时间
            return {"status": "success", "timestamp": time.time()}

        # 执行测试
        test_start = time.time()
        results = []

        for i in range(test_calls):
            call_start = time.time()
            result = await self.rate_limited_api_call(exchange_name, mock_api_call)
            call_end = time.time()

            results.append({
                "call_index": i,
                "call_duration": call_end - call_start,
                "timestamp": call_end,
                "result": result is not None
            })

        test_duration = time.time() - test_start

        # 分析结果
        analysis = {
            "exchange": exchange_name,
            "rate_limit_per_sec": rate_limit,
            "test_calls": test_calls,
            "total_duration": test_duration,
            "expected_min_duration": expected_min_duration,
            "rate_limiting_working": test_duration >= expected_min_duration * 0.9,  # 允许10%误差
            "average_call_interval": test_duration / max(test_calls - 1, 1),
            "expected_interval": 1.0 / rate_limit,
            "results": results
        }

        self.logger.info(f"📊 {exchange_name}限速测试结果:")
        self.logger.info(f"   总耗时: {test_duration:.3f}秒 (预期最小: {expected_min_duration:.3f}秒)")
        self.logger.info(f"   平均间隔: {analysis['average_call_interval']:.3f}秒 (预期: {analysis['expected_interval']:.3f}秒)")
        self.logger.info(f"   限速有效: {'✅' if analysis['rate_limiting_working'] else '❌'}")

        return analysis

    async def _rate_limit_wait(self, exchange_name: str):
        """🔥 根源修复：健壮的限速等待方法，确保30+代币启动成功"""
        rate_limit = self.rate_limits.get(exchange_name, 10)

        # 🔥 根源修复：使用更保守的限速控制
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time

        # 🔥 优化修复：合理的冷却时间，平衡限速和性能
        if exchange_name == "okx":
            # OKX使用优化的冷却策略 - 调整到0.33秒支持3次/秒
            min_interval = 0.33  # 每秒3次调用，平衡限速和WebSocket性能
        else:
            # 其他交易所使用基础冷却 + 缓冲
            base_interval = 1.0 / rate_limit
            min_interval = max(base_interval, self.cooldown_config["base_cooldown"])

        # 🔥 确保严格遵守冷却时间
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.info(f"🕐 {exchange_name} 健壮冷却等待: {wait_time:.3f}秒 (要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在等待完成后立即更新时间戳
        setattr(self, f"_{exchange_name}_last_call", time.time())

    async def batched_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str],
                                          batch_size: int, batch_cooldown: float):
        """🔥 新增：分批预加载交易规则，避免连续大量API调用"""
        self.logger.info(f"📦 开始分批预加载交易规则: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建所有任务
        all_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task_info = {
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market_type": market_type
                    }
                    all_tasks.append(task_info)

        # 分批处理
        total_batches = (len(all_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 总任务数: {len(all_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(all_tasks))
            batch_tasks = all_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理第{batch_idx + 1}/{total_batches}批 ({len(batch_tasks)}个任务)")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_trading_rule,
                    task_info["exchange"], task_info["symbol"], task_info["market_type"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            batch_start = time.time()
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            batch_duration = time.time() - batch_start

            # 统计结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功, 耗时{batch_duration:.1f}秒")

            # 批间冷却（除了最后一批）
            if batch_idx < total_batches - 1:
                self.logger.info(f"🕐 批间冷却: {batch_cooldown}秒...")
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批预加载交易规则完成")

    async def batched_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str],
                                        batch_size: int, batch_cooldown: float):
        """🔥 新增：分批获取合约信息"""
        self.logger.info(f"📦 开始分批获取合约信息: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建合约信息任务
        contract_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    contract_tasks.append({
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol
                    })

        if not contract_tasks:
            self.logger.info("📋 无合约信息需要获取")
            return

        # 分批处理
        total_batches = (len(contract_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 合约信息任务数: {len(contract_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(contract_tasks))
            batch_tasks = contract_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理合约信息第{batch_idx + 1}/{total_batches}批")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_contract_info,
                    task_info["exchange"], task_info["symbol"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 合约信息第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功")

            # 批间冷却
            if batch_idx < total_batches - 1:
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批获取合约信息完成")

    async def rate_limited_api_call(self, exchange_name: str, func, *args, **kwargs):
        """限速API调用"""
        # 添加到调用队列
        await self.call_queues[exchange_name].put((func, args, kwargs))
        
        # 等待执行
        return await self._execute_rate_limited_call(exchange_name)
        
    async def _execute_rate_limited_call(self, exchange_name: str):
        """🔥 修复版：执行限速调用 - 精确控制API调用间隔"""
        queue = self.call_queues[exchange_name]
        rate_limit = self.rate_limits[exchange_name]

        if queue.empty():
            return None

        func, args, kwargs = await queue.get()

        # 🔥 修复：精确的限速控制逻辑
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time
        min_interval = 1.0 / rate_limit

        # 🔥 关键修复：确保严格遵守最小间隔
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.debug(f"🕐 {exchange_name} API限速等待: {wait_time:.3f}秒 (间隔要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在执行调用前立即更新时间戳，确保精确间隔控制
        call_start_time = time.time()
        setattr(self, f"_{exchange_name}_last_call", call_start_time)

        # 执行调用
        try:
            result = await func(*args, **kwargs)
            call_duration = time.time() - call_start_time
            self.call_stats[exchange_name] += 1

            self.logger.debug(f"✅ {exchange_name} API调用成功，耗时: {call_duration:.3f}秒")
            return result

        except Exception as e:
            call_duration = time.time() - call_start_time
            error_msg = str(e)

            # 🔥 更智能的重试机制：限速错误特殊处理
            if "Too Many Requests" in error_msg or "50011" in error_msg or "10006" in error_msg:
                self.logger.warning(f"⚠️ {exchange_name} 触发限速错误，特殊延迟5秒: {error_msg}")
                await asyncio.sleep(5.0)  # 限速错误特殊延迟5秒

                # 指数退避重试
                for retry_count in range(3):
                    retry_delay = 2 ** retry_count  # 指数退避: 1秒, 2秒, 4秒
                    self.logger.info(f"🔄 {exchange_name} 限速错误重试 {retry_count + 1}/3，延迟{retry_delay}秒")
                    await asyncio.sleep(retry_delay)

                    try:
                        # 重新执行调用
                        result = await func(*args, **kwargs)
                        self.logger.info(f"✅ {exchange_name} 限速错误重试成功")
                        return result
                    except Exception as retry_e:
                        if retry_count == 2:  # 最后一次重试
                            self.logger.error(f"❌ {exchange_name} 限速错误重试失败: {retry_e}")
                        continue
            else:
                self.logger.error(f"❌ {exchange_name} API调用失败 (耗时: {call_duration:.3f}秒): {e}")

            # 🔥 失败的调用也要计入限速，避免因为失败而绕过限速控制
            return None
            
    async def batch_balance_queries(self, exchanges: Dict[str, Any]):
        """批量化余额查询"""
        self.logger.info("💰 批量化余额查询...")

        tasks = []
        for exchange_name, exchange in exchanges.items():
            if hasattr(exchange, 'get_balance'):
                task = self.rate_limited_api_call(
                    exchange_name,
                    self._get_balance,
                    exchange
                )
                tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 余额查询完成: {success_count}/{len(tasks)}")

    async def smart_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str]):
        """智能化合约信息获取"""
        self.logger.info("📊 智能化合约信息获取...")

        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_contract_info,
                        exchange, symbol
                    )
                    tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 合约信息获取完成: {success_count}/{len(tasks)}")

    async def delayed_leverage_setup(self, exchanges: Dict[str, Any], symbols: List[str]):
        """延迟杠杆设置"""
        self.logger.info("🔧 延迟杠杆设置...")

        # 延迟1秒再设置杠杆，避免与其他API调用冲突
        await asyncio.sleep(1)

        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'set_leverage'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._set_leverage,
                        exchange, symbol, 3  # 3倍杠杆
                    )
                    tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 杠杆设置完成: {success_count}/{len(tasks)}")

    async def _get_trading_rule(self, exchange, symbol: str, market_type: str):
        """获取交易规则"""
        try:
            if hasattr(exchange, 'get_trading_rule'):
                return await exchange.get_trading_rule(symbol, market_type)
            return None
        except Exception as e:
            self.logger.debug(f"获取交易规则失败: {symbol} {market_type} - {e}")
            return None

    async def _get_balance(self, exchange):
        """获取余额"""
        try:
            # 使用现有的统一接口
            if hasattr(exchange, 'get_balance'):
                return await exchange.get_balance()
            return None
        except Exception as e:
            self.logger.debug(f"获取余额失败: {e}")
            return None

    async def _get_contract_info(self, exchange, symbol: str):
        """获取合约信息"""
        try:
            if hasattr(exchange, 'get_contract_info'):
                return await exchange.get_contract_info(symbol)
            return None
        except Exception as e:
            self.logger.debug(f"获取合约信息失败: {symbol} - {e}")
            return None

    async def _set_leverage(self, exchange, symbol: str, leverage: int):
        """设置杠杆"""
        try:
            if hasattr(exchange, 'set_leverage'):
                return await exchange.set_leverage(symbol, leverage)
            return None
        except Exception as e:
            self.logger.debug(f"设置杠杆失败: {symbol} {leverage}x - {e}")
            return None

# 全局优化器实例
_api_optimizer = None

def get_api_optimizer():
    """获取API优化器实例"""
    global _api_optimizer
    if _api_optimizer is None:
        _api_optimizer = APICallOptimizer()
    return _api_optimizer
