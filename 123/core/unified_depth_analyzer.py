# -*- coding: utf-8 -*-
"""
🔥 统一深度检查模块
将ExecutionEngine和ExecutionParamsPreparer中重复的深度检查逻辑提取到统一模块
确保深度分析的一致性和避免重复计算
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# 🔥 新增：导入统一Order差价计算模块
try:
    from .unified_order_spread_calculator import get_order_spread_calculator, get_depth_analysis_config
except ImportError:
    # 降级处理：如果模块不存在，使用None
    get_order_spread_calculator = None
    get_depth_analysis_config = None

logger = logging.getLogger(__name__)


@dataclass
class DepthAnalysisConfig:
    """深度分析配置 - 🔥 升级：支持30档深度分析，集成累积表算法"""
    max_depth_levels: int = 30  # 🔥 升级：最大分析深度档位（30档）
    safety_margin: float = 0.90  # 安全边际 (90%)
    price_deviation_threshold: float = 0.1  # 价格偏差阈值 (10%)
    slippage_threshold: float = 0.001  # 🔥 新增：滑点阈值 (0.1%)
    min_effective_levels: int = 0  # 🔥 修复：不要求最小档位，遵循MD文档设计
    volume_threshold: float = 0.0001  # 最小有效数量阈值


@dataclass
class DepthAnalysisResult:
    """深度分析结果 - 🔥 简化版本：只关心是否足够套利金额"""
    is_sufficient: bool                     # 深度是否足够套利金额
    total_volume: float = 0.0               # 总数量
    effective_volume: float = 0.0           # 有效数量（扣除安全边际）
    error_message: str = ""                 # 错误信息（深度不足时的原因）


class UnifiedDepthAnalyzer:
    """
    🔥 统一深度检查器
    替代ExecutionEngine和ExecutionParamsPreparer中的重复深度检查逻辑
    """
    
    def __init__(self, config: Optional[DepthAnalysisConfig] = None):
        self.config = config or DepthAnalysisConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def analyze_orderbook_depth(
        self,
        orderbook: Dict[str, Any],
        required_amount: float,
        side: str,  # "buy" or "sell"
        reference_price: float = 0.0,
        exchange: str = "",
        symbol: str = ""
    ) -> DepthAnalysisResult:
        """
        🔥 统一订单簿深度分析接口
        
        Args:
            orderbook: 订单簿数据
            required_amount: 需要的数量
            side: 交易方向 ("buy"检查asks, "sell"检查bids)
            reference_price: 参考价格(用于价格合理性检查)
            exchange: 交易所名称
            symbol: 交易对
            
        Returns:
            DepthAnalysisResult: 深度分析结果
        """
        try:
            if not orderbook:
                return DepthAnalysisResult(
                    is_sufficient=False,
                    error_message="订单簿数据为空"
                )
            
            # 根据交易方向选择深度数据
            if side.lower() == "buy":
                # 买入需要检查卖单深度(asks)
                depth_data = orderbook.get('asks', [])
                depth_side = "asks"
            elif side.lower() == "sell":
                # 卖出需要检查买单深度(bids)
                depth_data = orderbook.get('bids', [])
                depth_side = "bids"
            else:
                return DepthAnalysisResult(
                    is_sufficient=False,
                    error_message=f"不支持的交易方向: {side}"
                )
            
            # 🔥 简化：空数据直接返回不足
            if not depth_data:
                return DepthAnalysisResult(
                    is_sufficient=False,
                    total_volume=0.0,
                    effective_volume=0.0,
                    error_message=f"{depth_side}深度数据为空"
                )
            
            # 🔥 升级：直接分析前30档深度是否足够
            return self._simple_depth_check(depth_data, required_amount, depth_side)
            
        except Exception as e:
            self.logger.error(f"深度分析异常: {e}")
            return DepthAnalysisResult(
                is_sufficient=False,
                error_message=f"深度分析异常: {str(e)}"
            )

    def _simple_depth_check(self, depth_data: List, required_amount: float, depth_side: str) -> DepthAnalysisResult:
        """🔥 升级的深度检查：支持30档深度分析，集成累积表算法"""
        try:
            # 🔥 升级：取前30档数据（从10档升级到30档）
            levels = depth_data[:30]

            # 🔥 升级：使用累积表算法进行30档深度分析
            if get_order_spread_calculator is not None:
                # 使用新的累积表算法
                calculator = get_order_spread_calculator()

                # 构建累积表
                cum_table = calculator.build_cumulative_table_30_levels(levels, side=depth_side)

                if len(cum_table) > 0:
                    # 使用二分查找找到最优执行档位
                    execution_level = calculator.find_optimal_execution_level(cum_table, required_amount)

                    if execution_level is not None:
                        # 检查滑点是否在阈值内
                        if execution_level.slippage_percent <= self.config.slippage_threshold:
                            return DepthAnalysisResult(
                                is_sufficient=True,
                                total_volume=execution_level.cumulative_qty,
                                effective_volume=execution_level.cumulative_value
                            )

            # 🔥 降级处理：如果新算法不可用，使用原有10档算法
            # 计算总数量
            total_volume = 0.0
            for level in levels:
                if len(level) >= 2:
                    try:
                        volume = float(level[1])
                        total_volume += volume
                    except (ValueError, IndexError):
                        continue

            # 计算有效数量（扣除10%安全边际）
            effective_volume = total_volume * self.config.safety_margin

            # 简单判断：有效数量是否足够
            is_sufficient = effective_volume >= required_amount

            if is_sufficient:
                return DepthAnalysisResult(
                    is_sufficient=True,
                    total_volume=total_volume,
                    effective_volume=effective_volume
                )
            else:
                return DepthAnalysisResult(
                    is_sufficient=False,
                    total_volume=total_volume,
                    effective_volume=effective_volume,
                    error_message=f"{depth_side}深度不足: 需要{required_amount:.6f}, 有效深度{effective_volume:.6f}"
                )

        except Exception as e:
            return DepthAnalysisResult(
                is_sufficient=False,
                error_message=f"深度检查异常: {str(e)}"
            )
    
    # 🔥 删除所有复杂的评分计算垃圾代码
    
    def analyze_dual_orderbook_depth(
        self,
        spot_orderbook: Dict[str, Any],
        futures_orderbook: Dict[str, Any],
        spot_amount: float,
        futures_amount: float,
        spot_price: float = 0.0,
        futures_price: float = 0.0
    ) -> Tuple[DepthAnalysisResult, DepthAnalysisResult]:
        """
        🔥 双市场订单簿深度分析
        替代ExecutionEngine中的重复深度检查逻辑
        
        Args:
            spot_orderbook: 现货订单簿
            futures_orderbook: 期货订单簿
            spot_amount: 现货需求数量
            futures_amount: 期货需求数量
            spot_price: 现货参考价格
            futures_price: 期货参考价格
            
        Returns:
            Tuple[DepthAnalysisResult, DepthAnalysisResult]: (现货深度分析, 期货深度分析)
        """
        # 现货买入 → 检查现货卖单深度(asks)
        spot_result = self.analyze_orderbook_depth(
            spot_orderbook, spot_amount, "buy", spot_price, "spot", ""
        )
        
        # 期货卖出 → 检查期货买单深度(bids)
        futures_result = self.analyze_orderbook_depth(
            futures_orderbook, futures_amount, "sell", futures_price, "futures", ""
        )
        
        return spot_result, futures_result
    
    # 🔥 删除拆单策略，强制单笔执行


# 🌟 全局实例
_global_analyzer = None

def get_depth_analyzer(config: Optional[DepthAnalysisConfig] = None) -> UnifiedDepthAnalyzer:
    """获取全局深度分析器实例"""
    global _global_analyzer
    if _global_analyzer is None or config is not None:
        _global_analyzer = UnifiedDepthAnalyzer(config)
    return _global_analyzer


def analyze_orderbook_depth(
    orderbook: Dict[str, Any],
    required_amount: float,
    side: str,
    reference_price: float = 0.0,
    exchange: str = "",
    symbol: str = "",
    config: Optional[DepthAnalysisConfig] = None
) -> DepthAnalysisResult:
    """快速分析订单簿深度"""
    analyzer = get_depth_analyzer(config)
    return analyzer.analyze_orderbook_depth(
        orderbook, required_amount, side, reference_price, exchange, symbol
    )
