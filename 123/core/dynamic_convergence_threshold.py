#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DynamicConvergenceThreshold - 动态趋同阈值模块
实现时间推进自动收窄平仓阈值功能，提高平仓效率

核心功能：
1. 时间进度跟踪
2. 非线性衰减函数
3. 动态阈值计算
4. 自适应收窄策略
5. 完全向下兼容现有逻辑
"""

import time
import math
from typing import Dict, Optional, Any, Tuple
from utils.logger import get_logger


class DynamicConvergenceThreshold:
    """
    动态趋同阈值计算器

    按照07文档设计，实现：
    - 时间推进自动收窄阈值
    - 非线性衰减函数
    - 自适应调整策略
    - 完全替代传统阈值模式
    """

    def __init__(self,
                 initial_threshold: float = 0.0005,  # 初始阈值 0.05%
                 final_threshold: float = 0.0001,   # 最终阈值 0.01%
                 max_duration: float = 300.0,       # 最大持续时间 5分钟
                 decay_function: str = "exponential"):
        """
        初始化动态阈值计算器

        Args:
            initial_threshold: 初始阈值
            final_threshold: 最终阈值
            max_duration: 最大持续时间（秒）
            decay_function: 衰减函数类型 ("linear", "exponential", "sigmoid")
        """
        self.initial_threshold = initial_threshold
        self.final_threshold = final_threshold
        self.max_duration = max_duration
        self.decay_function = decay_function
        self.logger = get_logger(self.__class__.__name__)

        # 验证参数
        if initial_threshold < final_threshold:
            raise ValueError("初始阈值不能小于最终阈值")
        if max_duration <= 0:
            raise ValueError("最大持续时间必须大于0")

        self.logger.info(f"动态趋同阈值初始化完成")
        self.logger.info(f"   初始阈值: {initial_threshold*100:.3f}%")
        self.logger.info(f"   最终阈值: {final_threshold*100:.3f}%")
        self.logger.info(f"   最大持续时间: {max_duration}秒")
        self.logger.info(f"   衰减函数: {decay_function}")

    def calculate_current_threshold(self, start_time: float, current_time: Optional[float] = None) -> float:
        """
        计算当前时刻的动态阈值

        Args:
            start_time: 套利开始时间戳
            current_time: 当前时间戳，默认为当前时间

        Returns:
            float: 当前阈值
        """
        if current_time is None:
            current_time = time.time()

        # 计算时间进度
        elapsed_time = current_time - start_time

        # 🔥 修复：处理无效时间（未来时间或负数时间）
        if elapsed_time < 0:
            # 如果开始时间在未来，返回初始阈值
            return self.initial_threshold

        time_progress = min(elapsed_time / self.max_duration, 1.0)

        # 🔥 修复：超过最大时间后，阈值设为最终阈值
        if elapsed_time >= self.max_duration:
            return self.final_threshold

        # 根据衰减函数计算阈值
        if self.decay_function == "linear":
            threshold = self._linear_decay(time_progress)
        elif self.decay_function == "exponential":
            threshold = self._exponential_decay(time_progress)
        elif self.decay_function == "sigmoid":
            threshold = self._sigmoid_decay(time_progress)
        else:
            self.logger.warning(f"未知衰减函数: {self.decay_function}，使用线性衰减")
            threshold = self._linear_decay(time_progress)

        return threshold

    def _linear_decay(self, progress: float) -> float:
        """
        线性衰减函数

        Args:
            progress: 时间进度 [0, 1]

        Returns:
            float: 计算后的阈值
        """
        return self.initial_threshold - (self.initial_threshold - self.final_threshold) * progress

    def _exponential_decay(self, progress: float) -> float:
        """
        指数衰减函数

        Args:
            progress: 时间进度 [0, 1]

        Returns:
            float: 计算后的阈值
        """
        # 使用指数衰减，衰减率可调
        decay_rate = 3.0  # 增加衰减率，确保在progress=1时接近最终值
        decay_factor = math.exp(-decay_rate * progress)

        # 当progress >= 1时，直接返回最终阈值
        if progress >= 1.0:
            return self.final_threshold

        return self.final_threshold + (self.initial_threshold - self.final_threshold) * decay_factor

    def _sigmoid_decay(self, progress: float) -> float:
        """
        S型衰减函数

        Args:
            progress: 时间进度 [0, 1]

        Returns:
            float: 计算后的阈值
        """
        # 使用sigmoid函数，前期缓慢，中期快速，后期缓慢
        steepness = 6.0  # 控制S型曲线的陡峭程度
        sigmoid_value = 1 / (1 + math.exp(-steepness * (progress - 0.5)))
        return self.initial_threshold - (self.initial_threshold - self.final_threshold) * sigmoid_value

    def get_threshold_info(self, start_time: float, current_time: Optional[float] = None) -> Dict[str, Any]:
        """
        获取详细的阈值信息

        Args:
            start_time: 套利开始时间戳
            current_time: 当前时间戳，默认为当前时间

        Returns:
            Dict: 包含阈值和相关信息的字典
        """
        if current_time is None:
            current_time = time.time()

        elapsed_time = current_time - start_time
        time_progress = min(elapsed_time / self.max_duration, 1.0)
        current_threshold = self.calculate_current_threshold(start_time, current_time)

        return {
            'current_threshold': current_threshold,
            'current_threshold_pct': current_threshold * 100,
            'elapsed_time': elapsed_time,
            'time_progress': time_progress,
            'time_progress_pct': time_progress * 100,
            'initial_threshold': self.initial_threshold,
            'final_threshold': self.final_threshold,
            'max_duration': self.max_duration,
            'decay_function': self.decay_function,
            'is_at_final': time_progress >= 1.0
        }

    def should_close_position(self, current_spread: float, start_time: float,
                             current_time: Optional[float] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        判断是否应该平仓

        Args:
            current_spread: 当前差价
            start_time: 套利开始时间戳
            current_time: 当前时间戳，默认为当前时间

        Returns:
            Tuple[bool, Dict]: (是否应该平仓, 详细信息)
        """
        threshold_info = self.get_threshold_info(start_time, current_time)
        current_threshold = threshold_info['current_threshold']

        # 🔥 修复：只有现货溢价（负值）且达到阈值时才平仓
        # 套利逻辑：期货溢价开仓 → 等待趋同 → 现货溢价≥阈值平仓
        is_spot_premium = current_spread < 0  # 现货溢价（负值）
        meets_threshold = abs(current_spread) >= current_threshold  # 🔥 修复：现货溢价的绝对值 >= 阈值才平仓
        should_close = is_spot_premium and meets_threshold

        decision_info = {
            **threshold_info,
            'current_spread': current_spread,
            'current_spread_pct': current_spread * 100,
            'is_spot_premium': is_spot_premium,
            'meets_threshold': meets_threshold,
            'should_close': should_close,
            'spread_vs_threshold': abs(current_spread) / current_threshold if current_threshold > 0 else float('inf'),
            'decision_reason': self._get_decision_reason(current_spread, current_threshold, threshold_info, is_spot_premium, meets_threshold)
        }

        return should_close, decision_info

    def _get_decision_reason(self, current_spread: float, current_threshold: float,
                           threshold_info: Dict[str, Any], is_spot_premium: bool = None,
                           meets_threshold: bool = None) -> str:
        """
        获取决策原因

        Args:
            current_spread: 当前差价
            current_threshold: 当前阈值
            threshold_info: 阈值信息
            is_spot_premium: 是否为现货溢价
            meets_threshold: 是否达到阈值

        Returns:
            str: 决策原因
        """
        abs_spread = abs(current_spread)

        # 🔥 修复：提供更详细的决策原因
        if is_spot_premium is None:
            is_spot_premium = current_spread < 0
        if meets_threshold is None:
            meets_threshold = abs_spread >= current_threshold  # 🔥 修复：>= 才达到阈值

        if not is_spot_premium:
            return f"期货溢价{current_spread*100:.3f}%，等待价差收敛至现货溢价"
        elif not meets_threshold:
            return f"现货溢价{abs_spread*100:.3f}%未达到动态阈值{current_threshold*100:.3f}%，继续等待"
        else:
            if threshold_info['is_at_final']:
                return f"现货溢价{abs_spread*100:.3f}%达到最终阈值{current_threshold*100:.3f}%，强制平仓"
            else:
                return f"现货溢价{abs_spread*100:.3f}%达到动态阈值{current_threshold*100:.3f}%，建议平仓"

    def get_time_remaining(self, start_time: float, current_time: Optional[float] = None) -> float:
        """
        获取剩余时间

        Args:
            start_time: 套利开始时间戳
            current_time: 当前时间戳，默认为当前时间

        Returns:
            float: 剩余时间（秒），如果已超时返回0
        """
        if current_time is None:
            current_time = time.time()

        elapsed_time = current_time - start_time
        remaining_time = max(0, self.max_duration - elapsed_time)
        return remaining_time

    def is_expired(self, start_time: float, current_time: Optional[float] = None) -> bool:
        """
        检查是否已过期

        Args:
            start_time: 套利开始时间戳
            current_time: 当前时间戳，默认为当前时间

        Returns:
            bool: 是否已过期
        """
        return self.get_time_remaining(start_time, current_time) <= 0

    def create_compatible_threshold(self, fixed_threshold: float) -> 'DynamicConvergenceThreshold':
        """
        创建兼容传统阈值的动态阈值实例

        Args:
            fixed_threshold: 传统阈值

        Returns:
            DynamicConvergenceThreshold: 配置为传统阈值的实例
        """
        return DynamicConvergenceThreshold(
            initial_threshold=fixed_threshold,
            final_threshold=fixed_threshold,
            max_duration=float('inf'),  # 永不过期
            decay_function="linear"
        )